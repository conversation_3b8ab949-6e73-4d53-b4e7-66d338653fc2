﻿// Este servicio es el encargado de aplicar logica de negocio para los protocolos
using DAO;
using Domain;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Epas {
    public class ProtocolosService {

        /// <summary>
        /// Obtiene un protocolo por ID. 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static Domain.Protocolos GetById(int id) {
            Domain.Protocolos p;
            using(ISession sess = NHibernateSessionProvider.GetSession()) {
                p = (Domain.Protocolos) new ProtocolosRepository(sess).GetByKey(id);

                NHibernateUtil.Initialize(p.TipoAnalisis);                           
                NHibernateUtil.Initialize(p.Fuentes.Localidades.Departamentos.Provincias);               
                NHibernateUtil.Initialize(p.SubFuentes);
                NHibernateUtil.Initialize(p.ProtocoloDeterminaciones);
                NHibernateUtil.Initialize(p.ProtocolosEstados.Codigo);
                NHibernateUtil.Initialize(p.Contratos_IdContratoSolicitante);
                NHibernateUtil.Initialize(p.Contratos_IdContratoTomador);
                NHibernateUtil.Initialize(p.Usuario_IdUsuario);
                NHibernateUtil.Initialize(p.Usuario_IdUsuarioAprobo);

                foreach(ProtocoloDeterminaciones pd in p.ProtocoloDeterminaciones)
                {
                    NHibernateUtil.Initialize(pd.Determinaciones);
                }

                sess.Close();
                sess.Dispose();
                return p;
            }
        }

        /// <summary>
        /// Obtiene todos los protocolos de la base de datos
        /// </summary>
        /// <returns></returns>
        public static IList<Protocolos> GetAll() {
            IList<Protocolos> listaAux;
            IList<Domain.Protocolos> p;
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                listaAux = (IList<Protocolos>) new ProtocolosRepository(sess).GetAll();

                p = sess.Query<Domain.Protocolos>().ToList();

                if(p != null)
                {
                    foreach(Domain.Protocolos protocolo in p)
                    {
                        NHibernateUtil.Initialize(protocolo.TipoAnalisis);
                        NHibernateUtil.Initialize(protocolo.ProtocolosEstados);
                        if (protocolo.ProtocolosEstados != null)
                        {
                            NHibernateUtil.Initialize(protocolo.ProtocolosEstados.Codigo);
                        }
                        NHibernateUtil.Initialize(protocolo.Contratos_IdContratoSolicitante);
                        NHibernateUtil.Initialize(protocolo.Contratos_IdContratoTomador);
                        NHibernateUtil.Initialize(protocolo.Fuentes);
                        NHibernateUtil.Initialize(protocolo.SubFuentes);
                        NHibernateUtil.Initialize(protocolo.ProtocoloDeterminaciones);
                        NHibernateUtil.Initialize(protocolo.Usuario_IdUsuario);
                        NHibernateUtil.Initialize(protocolo.Usuario_IdUsuarioAprobo);

                    }
                }

                sess.Close();
                sess.Dispose();
                return p;
            }
        }
        /// <summary>
        /// Obtiene los protocolos por id de Solicitante
        /// </summary>
        /// <param name="idProtocolo"></param>
        /// <returns></returns>
        public static IList<Domain.Protocolos> GetByIdSolicitante(int idSolicitante)
        {

            IList<Domain.Protocolos> listaAux;
            using(ISession sess = NHibernateSessionProvider.GetSession())
            {

                listaAux = new ProtocolosRepository(sess).GetByIdSolicitante(idSolicitante);

                foreach(Protocolos p in listaAux)
                {
                    NHibernateUtil.Initialize(p.Contratos_IdContratoSolicitante);
                    NHibernateUtil.Initialize(p.Contratos_IdContratoTomador);
                    NHibernateUtil.Initialize(p.Fuentes);
                    NHibernateUtil.Initialize(p.SubFuentes);
                    NHibernateUtil.Initialize(p.TipoAnalisis);
                    NHibernateUtil.Initialize(p.ProtocolosEstados);
                    NHibernateUtil.Initialize(p.Usuario_IdUsuario);
                    NHibernateUtil.Initialize(p.Usuario_IdUsuarioAprobo);
                }

                sess.Close();
                sess.Dispose();
                return listaAux;

            }

        }

        /// <summary>
        /// Obtiene todos los protocolos activos de la base de datos .
        /// </summary>
        /// <param name="codigoEstado"></param>
        /// <returns></returns>
        public static IList<Protocolos> GetActive()
        {
            IList<Protocolos> listaAux;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                listaAux = (IList<Protocolos>)new ProtocolosRepository(sess).GetAll();
                sess.Close();
                sess.Dispose();
            }

            return (listaAux);
        }

        /// <summary>
        /// Guardar o actualizar protocolos.
        /// </summary>
        /// <param name="protocolos"></param>
        public static void SaveOrUpdate(Domain.Protocolos protocolos) {

            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                using(NHibernate.ITransaction tx = sess.BeginTransaction()) {
                    try {
                        new ProtocolosRepository(sess).Add(protocolos);
                        tx.Commit();
                    } catch(Exception e) {
                        tx.Rollback();
                        throw e;
                    } finally {
                        sess.Close();
                        sess.Dispose();
                    }
                }
            }
        }

        /// <summary>
        /// Eliminar un Protocolo de la base de datos. 
        /// </summary>
        /// <param name="P"></param>
        public static void Delete(Domain.Protocolos P) {
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                using(NHibernate.ITransaction tx = sess.BeginTransaction()) {
                    try {
                        new ProtocolosRepository(sess).Remove(P);
                        tx.Commit();
                    } catch(Exception e) {
                        tx.Rollback();
                        throw e;
                    } finally {
                        sess.Close();
                        sess.Dispose();
                    }
                }

            }
        }
    }
}
