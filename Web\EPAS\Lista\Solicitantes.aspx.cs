﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web.EPAS.Lista
{
    public partial class Solicitantes : System.Web.UI.Page
    {
        protected void Page_Load(object sender,EventArgs e)
        {
            if(!IsPostBack)
            {
                DataTable dt = new DataTable();
                dt.Columns.Add("Codigo");
                dt.Columns.Add("Descripcion");
                dt.Columns.Add("Unid.");
                dt.Columns.Add("Direccion");
                dt.Columns.Add("CondicionFiscal");
                dt.Columns.Add("TelDeContacto");
                dt.Columns.Add("Email");
                dt.Columns.Add("CUIT");

                // Agrega filas ficticias
                dt.Rows.Add("1","LABORATORIO","0","TUCUMÁN 123","CONSUMIDOR FINAL","3815465852","<EMAIL>","20-32165487-1");
                dt.Rows.Add("2","PARTICULARES","1","CHOCON 5845","MONOTRIBUTISTA","2994564565","<EMAIL>","20-45678952-1");
                dt.Rows.Add("3","PRESIDENCIA DEL E.P.A.S.","0","JUJUY 254","MONOTRIBUTISTA","2994876543","<EMAIL>","25-23545863-7");
                dt.Rows.Add("4","MUNICIPIOS","0","BAHÍA BLANCA 45","RESPONSABLE INSCRIPTO","2996418529","<EMAIL>","20-19564852-1");
                dt.Rows.Add("5","COMISIONES DE FOMENTO","1","SARMIENTO 548","CONSUMIDOR FINAL","2993526547","<EMAIL>","25-33548669-7");

                ASPxGridView1.DataSource = dt;
                ASPxGridView1.DataBind();
            }
        }

    
        protected void btnEditar_Click(object sender, EventArgs e)
        {

        }
    }
}