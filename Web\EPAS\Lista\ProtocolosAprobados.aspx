﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="ProtocolosAprobados.aspx.cs" Inherits="Web.EPAS.Lista.ProtocolosAprobados" %>

<%@ Register Assembly="DevExpress.Web.v18.1" Namespace="DevExpress.Web" TagPrefix="dx" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">

    <script type="text/javascript">

    function OnToolbarItemClick(s, e) {
        if (IsCustomExportToolbarCommand(e.item.name)) {
            e.processOnServer = true;
            e.usePostBack = true;
        } else {

        }
    }
    function IsCustomExportToolbarCommand(command) {
        return command == "CustomExportToXLS" || command == "CustomExportToXLSX";
    }

    </script>

</asp:Content>


<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

    <div class="page-header page-header-light shadow">
        <div class="page-header-content d-lg-flex border-top">
            <div class="d-flex">
                <div class="breadcrumb py-2">
                    <a href="<%= Web.Util.Helpers.GetApplicationPath() %>/Index.aspx" class="breadcrumb-item"><i class="icon-home2 mr-2"></i>Inicio</a>
                    <a href="<%= Web.Util.Helpers.GetApplicationPath() %>/EPAS/Lista/ProtocolosAprobados.aspx" class="breadcrumb-item"><i class="icon-display me-2"></i>Protocolo</a>
                    <span class="breadcrumb-item active">
                        <asp:Literal ID="ltr_nombreBreadcrumb" runat="server">Aprobados</asp:Literal>
                    </span>
                </div>
            </div>
        </div>

        <!-- /page header -->

        <div class="row gx-1" runat="server">

            <div class="col-lg-12">
                <div class="navbar navbar-expand-xl bg-secondary navbar-dark rounded-top mt-1">
                    <div class="container-fluid">
                        <div class="nav-item dropdown">
                            <h5 class="mt-2 mb-1 fs-5" data-bs-toggle="tab">Buscar Protocolos</h5>
                        </div>
                    </div>
                </div>
            </div>

            <!-- GridView Departamentos -->

            <div class="card row gx-1">
                <div class="card-body col-lg-12">
                    <div action="#">
                        <div class="row">

                            <dx:ASPxGridView ID="gv_ProtocolosAprobados" runat="server" CssClass="border border-black rounded" EnableRowsCache="false" ViewStateMode="Disabled" DataSourceForceStandardPaging="false" EnableViewState="false" Font-Size="Small" ClientInstanceName="gv_ProtocolosAprobados" AutoGenerateColumns="False" KeyFieldName="Id">

                                <Toolbars>
                                    <dx:GridViewToolbar ItemAlign="Right" EnableAdaptivity="true">
                                        <Items>
                                            <dx:GridViewToolbarItem Text="Exportar a" Image-IconID="actions_refresh_16x16office2013" DisplayMode="ImageWithText" BeginGroup="true">
                                                <Items>
                                                    <dx:GridViewToolbarItem Command="ExportToPdf" Text="Exportar a PDF" />
                                                    <dx:GridViewToolbarItem Command="ExportToXlsx" Text="Exportar a Excel" />
                                                </Items>
                                            </dx:GridViewToolbarItem>
                                            <dx:GridViewToolbarItem BeginGroup="true">
                                                <Template>
                                                    <dx:ASPxButtonEdit ID="tbToolbarSearch" runat="server" NullText="Buscar..." Height="100%" CssClass="form-control">
                                                        <Buttons>
                                                            <dx:SpinButtonExtended Image-IconID="find_find_16x16gray" />
                                                        </Buttons>
                                                    </dx:ASPxButtonEdit>
                                                </Template>
                                            </dx:GridViewToolbarItem>
                                        </Items>
                                    </dx:GridViewToolbar>
                                </Toolbars>
                                <SettingsSearchPanel CustomEditorID="tbToolbarSearch" />

                                <SettingsExport EnableClientSideExportAPI="true" ExcelExportMode="DataAware" />
                                <ClientSideEvents ToolbarItemClick="OnToolbarItemClick" />
                                <SettingsBehavior AllowFocusedRow="true" FilterRowMode="Auto" AllowDragDrop="false" AllowEllipsisInText="true" />
                                <SettingsPager PageSize="20" AlwaysShowPager="true"></SettingsPager>

                                <Columns>
                                    <dx:GridViewDataTextColumn FieldName="Id" Visible="false" CellStyle-Border-BorderColor="Black" CellStyle-HorizontalAlign="Center">
                                    </dx:GridViewDataTextColumn>

                                    <dx:GridViewDataTextColumn FieldName="NroProtocolo" Caption="N° de <br/> Prot." CellStyle-Border-BorderColor="Black" CellStyle-HorizontalAlign="Center" Width="7%">
                                        <Settings AllowAutoFilter="true" />
                                        <HeaderStyle HorizontalAlign="Center" />
                                    </dx:GridViewDataTextColumn>

                                    <dx:GridViewDataTextColumn FieldName="Contratos_IdContratoSolicitante.Nombre" Caption="Solicitante" CellStyle-Border-BorderColor="Black" CellStyle-HorizontalAlign="Center">
                                        <DataItemTemplate>

                                            <dx:ASPxLabel ID="ASPxLabel1423" runat="server" Text='<%#Eval("Contratos_IdContratoSolicitante.Nombre").ToString() %>' />

                                        </DataItemTemplate>
                                        <Settings AllowAutoFilter="true" />
                                        <HeaderStyle HorizontalAlign="Center" />
                                    </dx:GridViewDataTextColumn>

                                    <dx:GridViewDataTextColumn FieldName="Contratos_IdContratoTomador.Nombre" Caption="Muestreador" CellStyle-Border-BorderColor="Black" CellStyle-HorizontalAlign="Center">
                                        <DataItemTemplate>

                                           <%-- <dx:ASPxLabel ID="ASPxLabel1423" runat="server" Text='<%#Eval("Contratos_IdContratoTomador.Nombre").ToString() %>' />--%>
                                              <dx:ASPxLabel ID="ASPxLabel1423" runat="server" 
                     Text='<%# Eval("Contratos_IdContratoTomador.Nombre") != null ? Eval("Contratos_IdContratoTomador.Nombre").ToString() : "" %>' />
                                        </DataItemTemplate>
                                        <Settings AllowAutoFilter="true" />
                                        <HeaderStyle HorizontalAlign="Center" />
                                    </dx:GridViewDataTextColumn>

                                    <dx:GridViewDataTextColumn FieldName="Fuentes.Descripcion" Caption="Fuente" CellStyle-Border-BorderColor="Black" CellStyle-HorizontalAlign="Center">
                                        <DataItemTemplate>

                                            <dx:ASPxLabel ID="ASPxLabel1423" runat="server" Text='<%#Eval("Fuentes.Descripcion").ToString() %>' />

                                        </DataItemTemplate>
                                        <Settings AllowAutoFilter="true" />
                                        <HeaderStyle HorizontalAlign="Center" />
                                    </dx:GridViewDataTextColumn>

                                    <dx:GridViewDataTextColumn FieldName="SubFuentes.Descripcion" Caption="Sub-Fuente" CellStyle-Border-BorderColor="Black" CellStyle-HorizontalAlign="Center">
                                        <DataItemTemplate>

                                         <%--   <dx:ASPxLabel ID="ASPxLabel1423" runat="server" Text='<%#Eval("SubFuentes.Descripcion").ToString() %>' />--%>
                                              <dx:ASPxLabel ID="ASPxLabel1" runat="server" 
                     Text='<%# Eval("SubFuentes.Descripcion") != null && Eval("SubFuentes.Descripcion") != DBNull.Value ? 
                             Eval("SubFuentes.Descripcion").ToString() : "Sin descripción" %>' />
                                        </DataItemTemplate>
                                        <Settings AllowAutoFilter="true" />
                                        <HeaderStyle HorizontalAlign="Center" />
                                    </dx:GridViewDataTextColumn>

                                    <dx:GridViewDataTextColumn FieldName="TipoAnalisis.Descripcion" Caption="Análisis" CellStyle-Border-BorderColor="Black" CellStyle-HorizontalAlign="Center">
                                        <DataItemTemplate>

                                            <dx:ASPxLabel ID="ASPxLabel1423" runat="server" Text='<%#Eval("TipoAnalisis.Descripcion").ToString() %>' />

                                        </DataItemTemplate>
                                        <Settings AllowAutoFilter="true" />
                                        <HeaderStyle HorizontalAlign="Center" />
                                    </dx:GridViewDataTextColumn>

                                    <dx:GridViewDataTextColumn FieldName="Veredicto" Caption="Veredicto" CellStyle-Border-BorderColor="Black" CellStyle-HorizontalAlign="Center">
                                        <DataItemTemplate>

                                            <dx:ASPxLabel ID="ASPxLabel1423" runat="server" Text='<%#Eval("Veredicto").ToString() %>' />

                                        </DataItemTemplate>
                                        <Settings AllowAutoFilter="true" />
                                        <HeaderStyle HorizontalAlign="Center" />
                                    </dx:GridViewDataTextColumn>

                                    <dx:GridViewDataTextColumn FieldName="ProtocolosEstados.Nombre" Caption="Estado"
                                        CellStyle-Border-BorderColor="Black" CellStyle-HorizontalAlign="Center" Width="9%">
                                        <DataItemTemplate>

                                            <dx:ASPxLabel ID="ASPxLabel1423" CssClass='<%# Web.Util.Helpers.getBadgePorEstado(Eval("ProtocolosEstados.Codigo")) %>' runat="server" Text='<%#Eval("ProtocolosEstados.Nombre").ToString() %>' />

                                        </DataItemTemplate>
                                        <Settings AllowAutoFilter="true" />
                                        <HeaderStyle HorizontalAlign="Center" />
                                    </dx:GridViewDataTextColumn>

                                    <dx:GridViewDataTextColumn FieldName="Usuario_IdUsuarioAprobo.Username" Caption="Aprobado por" CellStyle-Border-BorderColor="Black" CellStyle-HorizontalAlign="Center" Width="12%">
                                        <DataItemTemplate>

                                            <dx:ASPxLabel ID="ASPxLabel1423" runat="server" Text='<%#Eval("Usuario_IdUsuarioAprobo.Username").ToString() %>' />

                                        </DataItemTemplate>
                                        <Settings AllowAutoFilter="true" />
                                        <HeaderStyle HorizontalAlign="Center" />
                                    </dx:GridViewDataTextColumn>

                                    <dx:GridViewDataTextColumn FieldName="FechaAprobado" Caption="Fecha de <br/> Aprobación" CellStyle-Border-BorderColor="Black" CellStyle-HorizontalAlign="Center" Width="10%">
                                        <DataItemTemplate>

                                            <dx:ASPxLabel ID="ASPxLabel1423" runat="server" Text='<%#Eval("FechaAprobado").ToString() %>' />

                                        </DataItemTemplate>
                                        <Settings AllowAutoFilter="true" />
                                        <HeaderStyle HorizontalAlign="Center" />
                                    </dx:GridViewDataTextColumn>

                                    <dx:GridViewDataTextColumn Caption="" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center" Width="8%">
                                        <DataItemTemplate>
                                            <dx:ASPxHyperLink ID="btnImpr" Text="Imprimir" CssClass="fw-medium text-decoration-none text-secondary" runat="server" Image-IconID="xaf_action_export_topdf_32x32" NavigateUrl='<%# String.Format( "~/Impresion.aspx?FileName=protocolo&id={0}", Eval("Id").ToString()) %>' ClientInstanceName="btnImpr">
                                                <ClientSideEvents Click="function (s,e){MostrarLoading();}" />
                                            </dx:ASPxHyperLink>
                                        </DataItemTemplate>
                                    </dx:GridViewDataTextColumn>

                                </Columns>
                            </dx:ASPxGridView>
                        
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</asp:Content>


<asp:Content ID="Content3" ContentPlaceHolderID="footer" runat="server">
</asp:Content>
