﻿using Common.DDL;
using DAO;
using Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Contratos {
    public class ContratoTipoServices {

        public static IList<ContratoTipo> GetAll() {
            IList<ContratoTipo> listaAux;
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                listaAux = (IList<ContratoTipo>) new ContratoTipoRepository(sess).GetAll();
                sess.Close();
                sess.Dispose();
            }

            return (listaAux);
        }

        public static ContratoTipo getById(int id) {
            ContratoTipo u;
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                u = new ContratoTipoRepository(sess).GetByKey(id);

                if(u != null) {
                    //NHibernateUtil.Initialize();

                }
                sess.Close();
                sess.Dispose();
                return u;
            }
        }

        public static List<ItemCombo> GetByContratoTipo(bool Activo) {
            IList<ContratoTipo> listaAux;
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                listaAux = (IList<ContratoTipo>) new ContratoTipoRepository(sess).GetByContratoTipo(Activo);
                sess.Close();
                sess.Dispose();
            }

            ItemCombo item;
            ListaCombo lista = new ListaCombo();
            foreach(ContratoTipo p in listaAux) {

                item = new ItemCombo();
                item.ID = p.Id.ToString();
                item.Descripcion = p.Descripcion;
                lista.Add(item);
            }
            return (lista);
        }
    }
}
