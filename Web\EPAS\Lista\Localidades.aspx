﻿<%@ Page Title="" Language="C#"  MasterPageFile="~/Site.Master" AutoEventWireup="true" EnableViewState="false" CodeBehind="Localidades.aspx.cs" Inherits="Web.EPAS.Lista.Localidades" %>

<%@ Register Assembly="DevExpress.Web.v18.1" Namespace="DevExpress.Web" TagPrefix="dx" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">

    <script type="text/javascript">

    function OnToolbarItemClick(s, e) {
        if (IsCustomExportToolbarCommand(e.item.name)) {
            e.processOnServer = true;
            e.usePostBack = true;
        } else {

        }
    }
    function IsCustomExportToolbarCommand(command) {
        return command == "CustomExportToXLS" || command == "CustomExportToXLSX";
        }


    </script>

</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

    <div class="page-header page-header-light shadow">
        <div class="page-header-content d-lg-flex border-top">
            <div class="d-flex">
                <div class="breadcrumb py-2">
                    <a href="<%= Web.Util.Helpers.GetApplicationPath() %>/Index.aspx" class="breadcrumb-item"><i class="icon-home2 mr-2"></i>Inicio</a>
                    <a href="<%= Web.Util.Helpers.GetApplicationPath() %>/EPAS/Lista/Localidades.aspx" class="breadcrumb-item"><i class="icon-display me-2"></i>Mantenimiento</a>
                    <span class="breadcrumb-item active">
                        <asp:Literal ID="ltr_nombreBreadcrumb" runat="server">Localidades</asp:Literal>
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- /page header -->

    <div class="row gx-1" runat="server">

        <div class="col-lg-12"">
            <div class="navbar navbar-expand-xl bg-secondary navbar-dark rounded-top mt-1">
                <div class="container-fluid">                           
                    <div class="nav-item dropdown">
                        <h5 class="mt-2 mb-1 fs-5" data-bs-toggle="tab">Mantenimiento de Localidades</h5>
                    </div>                        
                </div>
            </div>
        </div>
            
        <!-- GridView Localidades -->

        <div class="row gx-3">

            <div class="card col-lg-12 border border-black rounded mt-3 mx-2">
                <div class="card-body">
                    <div action="#">
                        <div class="row mb-1">
                            <label class="col-lg-1 ps-4 col-form-label fw-semibold btn-buscar">Buscar</label>
                            <div class="col-lg-2 px-0">
                                <dx:ASPxComboBox ID="cmbProvincias" ClientInstanceName="cmbProvincias" runat="server" DropDownStyle="DropDownList" OnSelectedIndexChanged="cmbProvincias_SelectedIndexChanged" NullText="Seleccionar Provincia" TextFormatString="{0} ({1})" Width="100%" CssClass="form-control altura-textbox" IncrementalFilteringMode="Contains" EnableSynchronization="False">
                                </dx:ASPxComboBox>
                            </div>

                            <div class="col-lg-1 pe-1 form-control-feedback form-control-feedback-start flex-grow-1">
                                <dx:ASPxButton ID="btn_filtro" CssClass="btn btn-secondary fw-semibold px-0 py-1" runat="server" Text="Filtrar" RenderMode="Button" OnClick="btn_filtro_Click">
                                    <ClientSideEvents Click="function(s,e){MostrarLoading();}" />
                                </dx:ASPxButton>
                            </div>

                            <div class="col-lg-1 mt-2 ps-4 form-control-feedback form-control-feedback-start flex-grow-1">
                                <dx:ASPxButton ID="btn_resetFiltro" CssClass="fw-medium text-secondary text-decoration-none" Theme="Moderno" runat="server" Text="Limpiar filtros" RenderMode="Link" OnClick="btn_resetFiltro_Click">
                                    <ClientSideEvents Click="function(s,e){MostrarLoading();}" />
                                </dx:ASPxButton>
                            </div>

                            <div class="col-lg-7"></div>

                        </div>
                    </div>
                </div>
                <div class="card-body col-lg-12 pt-0">
                    <div action="#">
                        <div class="row">
                            <dx:ASPxGridView ID="gv_Localidades" runat="server" CssClass="pt-0" EnableRowsCache="false" ViewStateMode="Disabled" DataSourceForceStandardPaging="false" EnableViewState="false" Font-Size="Small" ClientInstanceName="gv_Localidades" AutoGenerateColumns="False" KeyFieldName="Id">
                                <Toolbars>

                                    <dx:GridViewToolbar ItemAlign="Right" EnableAdaptivity="true">
                                        <Items>
                                            <dx:GridViewToolbarItem Text="Exportar a" Image-IconID="actions_refresh_16x16office2013" DisplayMode="ImageWithText" BeginGroup="true">
                                                <Items>
                                                    <dx:GridViewToolbarItem Command="ExportToPdf" Text="Exportar a PDF" />
                                                    <dx:GridViewToolbarItem Command="ExportToXlsx" Text="Exportar a Excel" />
                                                </Items>
                                            </dx:GridViewToolbarItem>
                                            <dx:GridViewToolbarItem BeginGroup="true">
                                                <Template>
                                                    <dx:ASPxButtonEdit ID="tbToolbarSearch" runat="server" NullText="Buscar..." Height="100%" CssClass="form-control">
                                                        <Buttons>
                                                            <dx:SpinButtonExtended Image-IconID="find_find_16x16gray" />
                                                        </Buttons>
                                                    </dx:ASPxButtonEdit>
                                                </Template>
                                            </dx:GridViewToolbarItem>
                                            <dx:GridViewToolbarItem BeginGroup="true">
                                                <Template>
                                                    <dx:ASPxHyperLink ID="ASPxHyperLink1" runat="server" CssClass="form-control h-100 mt-1 fw-medium rounded-2 text-white bg-info border border-secondary text-decoration-none p-2" Cursor="pointer" Text="Nuevo 📄" Font-Size="Small" Theme="Moderno" NavigateUrl="~/EPAS/AddEdit/AddEditLocalidades.aspx">
                                                        <ClientSideEvents Click="function(s,e){MostrarLoading();}" />
                                                    </dx:ASPxHyperLink>
                                                </Template>
                                            </dx:GridViewToolbarItem>
                                        </Items>
                                    </dx:GridViewToolbar>
                                </Toolbars>
                                <SettingsSearchPanel CustomEditorID="tbToolbarSearch" />

                                <SettingsExport EnableClientSideExportAPI="true" ExcelExportMode="DataAware" />
                                <ClientSideEvents ToolbarItemClick="OnToolbarItemClick" />
                                <SettingsBehavior AllowFocusedRow="true" FilterRowMode="Auto" AllowDragDrop="false" AllowEllipsisInText="true" />
                                <SettingsPager PageSize="20" AlwaysShowPager="true"></SettingsPager>

                                <Columns>

                                    <dx:GridViewDataTextColumn FieldName="Codigo" Caption="Código" CellStyle-Border-BorderColor="Black" CellStyle-HorizontalAlign="Center" Width="100">
                                        <HeaderStyle HorizontalAlign="Center" />
                                    </dx:GridViewDataTextColumn>

                                    <dx:GridViewDataTextColumn FieldName="Nombre" Caption="Descripción" Width="200px" CellStyle-Border-BorderColor="Black" CellStyle-HorizontalAlign="Center">
                                        <HeaderStyle HorizontalAlign="Center" />
                                    </dx:GridViewDataTextColumn>

                                    <dx:GridViewDataColumn FieldName="Provincias.Descripcion" Caption="Provincias" CellStyle-Border-BorderColor="Black" CellStyle-HorizontalAlign="Center">
                                        <HeaderStyle HorizontalAlign="Center" />
                                        <DataItemTemplate>
                                            <dx:ASPxLabel ID="labelProvincias" runat="server" Text='<%#Eval("Provincias.Descripcion") %>'>
                                            </dx:ASPxLabel>
                                        </DataItemTemplate>
                                    </dx:GridViewDataColumn>

                                    <dx:GridViewDataColumn FieldName="Departamentos.Descripcion" Caption="Departamento" CellStyle-Border-BorderColor="Black" CellStyle-HorizontalAlign="Center">
                                        <HeaderStyle HorizontalAlign="Center" />
                                        <DataItemTemplate>
                                            <dx:ASPxLabel ID="labelDpto" runat="server" Text='<%# Eval("Departamentos.Descripcion")  %>' />
                                        </DataItemTemplate>
                                    </dx:GridViewDataColumn>

                                    <dx:GridViewDataColumn FieldName="Zonas.Descripcion" Caption="Zona" CellStyle-Border-BorderColor="Black" CellStyle-HorizontalAlign="Center">
                                        <HeaderStyle HorizontalAlign="Center" />
                                        <DataItemTemplate>
                                            <dx:ASPxLabel ID="labelZona" runat="server" Text='<%# Eval("Zonas.Descripcion")  %>' />
                                        </DataItemTemplate>
                                    </dx:GridViewDataColumn>

                                    <dx:GridViewDataTextColumn Caption="Activo" FieldName="Activo" Visible="true" HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="true" CellStyle-HorizontalAlign="Center" Width="100">
                                        <Settings AllowAutoFilter="false" />
                                        <DataItemTemplate>
                                            <dx:ASPxImage ID="ASPxImage1" runat="server" Visible='<%# Eval("Activo")  %>' ShowLoadingImage="true" ImageUrl="~/App_Themes/Tema1/img/check.png" Width="30px">
                                            </dx:ASPxImage>
                                            <dx:ASPxImage ID="ASPxImage2" runat="server" Visible='<%# !Convert.ToBoolean(Eval("Activo"))  %>' ShowLoadingImage="true" ImageUrl="~/App_Themes/Tema1/img/no.png" Width="30px">
                                            </dx:ASPxImage>
                                        </DataItemTemplate>
                                    </dx:GridViewDataTextColumn>

                                    <dx:GridViewDataColumn Caption="" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center" Width="100px">
                                        <DataItemTemplate>
                                            <dx:ASPxButton ID="btnEditar" runat="server" RenderMode="Link" Text="Editar" OnClick="btnEditar_Click" CssClass="fw-semibold text-decoration-none text-secondary" Font-Size="Small" Theme="Moderno" CommandArgument='<%# Eval("Id")%>' AutoPostBack="false">
                                                <ClientSideEvents Click="function (s,e){MostrarLoading();}" />
                                            </dx:ASPxButton>
                                        </DataItemTemplate>
                                    </dx:GridViewDataColumn>

                                </Columns>
                            </dx:ASPxGridView>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</asp:Content>

<asp:Content ID="Content3" ContentPlaceHolderID="footer" runat="server">
</asp:Content>
