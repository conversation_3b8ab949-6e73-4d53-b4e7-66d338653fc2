﻿using Common.DDL;
using DAO;
using Domain;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Epas
{
    public class FuentesTiposService
    {
        //Trae todo
        public static IList<Domain.FuentesTipos> GetAll()
        {
            IList<Domain.FuentesTipos> listaAux;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                listaAux = (IList<Domain.FuentesTipos>)new FuentesTiposRepository(sess).GetAll();
                sess.Close();
                sess.Dispose();
            }
            return (listaAux);
        }

        //obtener por id
        public static Domain.FuentesTipos GetById(int id)
        {

            Domain.FuentesTipos p;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {

                p = (Domain.FuentesTipos)new FuentesTiposRepository(sess).GetByKey(id);
                NHibernateUtil.Initialize(sess);
                sess.Close();
                sess.Dispose();
                return p;
            }

        }

        //existe codigo
        public static bool ExisteCodigo(string codigo)
        {

            Domain.FuentesTipos p;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {

                p = (Domain.FuentesTipos)new FuentesTiposRepository(sess).ExisteCodigo(codigo);
                sess.Close();
                sess.Dispose();
                return p != null;

            }

        }

        //existe nombre 
        public static bool ExisteNombre(string Descripcion)
        {
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                return new FuentesTiposRepository(sess).ExisteNombre(Descripcion);
            }
        }


        //agregar y actualizar
        public static void SaveOrUpdate(Domain.FuentesTipos p)
        {

            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {

                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {

                    try
                    {

                        new FuentesTiposRepository(sess).Add(p);

                        tx.Commit();
                    }
                    catch (Exception e)
                    {

                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {

                        sess.Close();
                        sess.Dispose();
                    }

                }

            }

        }

        //borrar
        public static void Delete(Domain.FuentesTipos p)
        {
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new FuentesTiposRepository(sess).Remove(p);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }

                }

            }

        }


    }
}
