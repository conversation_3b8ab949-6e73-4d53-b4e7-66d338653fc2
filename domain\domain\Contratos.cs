//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 16/11/2023 12:35:14
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.Contratos, Domain in the schema.
    /// </summary>
    public partial class Contratos {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for Contratos constructor in the schema.
        /// </summary>
        public Contratos()
        {
            this.ContratoContactos = new HashSet<ContratoContactos>();
            this.ContratoDomicilios = new HashSet<ContratoDomicilios>();
            this.Protocolos_IdContratoSolicitante = new HashSet<Protocolos>();
            this.Protocolos_IdContratoTomador = new HashSet<Protocolos>();
            this.Comprobantes = new HashSet<Comprobantes>();
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Nombre in the schema.
        /// </summary>
        public virtual string Nombre
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for RazonSocial in the schema.
        /// </summary>
        public virtual string RazonSocial
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for NroContrato in the schema.
        /// </summary>
        public virtual System.Nullable<int> NroContrato
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for IdentificacionTributaria in the schema.
        /// </summary>
        public virtual string IdentificacionTributaria
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for EsTomador in the schema.
        /// </summary>
        public virtual System.Nullable<bool> EsTomador
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for FechaAlta in the schema.
        /// </summary>
        public virtual System.Nullable<System.DateTime> FechaAlta
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Baja in the schema.
        /// </summary>
        public virtual System.Nullable<bool> Baja
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for FechaBaja in the schema.
        /// </summary>
        public virtual System.Nullable<System.DateTime> FechaBaja
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Observaciones in the schema.
        /// </summary>
        public virtual string Observaciones
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for FechaGeneracion in the schema.
        /// </summary>
        public virtual System.Nullable<System.DateTime> FechaGeneracion
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for UltimaFechaFactura in the schema.
        /// </summary>
        public virtual System.Nullable<System.DateTime> UltimaFechaFactura
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Email in the schema.
        /// </summary>
        public virtual string Email
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for EmailAlter in the schema.
        /// </summary>
        public virtual string EmailAlter
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Telefono in the schema.
        /// </summary>
        public virtual string Telefono
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for TelefonoAlter in the schema.
        /// </summary>
        public virtual string TelefonoAlter
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ContratoContactos in the schema.
        /// </summary>
        public virtual ISet<ContratoContactos> ContratoContactos
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ContratoDomicilios in the schema.
        /// </summary>
        public virtual ISet<ContratoDomicilios> ContratoDomicilios
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Usuario in the schema.
        /// </summary>
        public virtual Usuario Usuario
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for IVACondiciones in the schema.
        /// </summary>
        public virtual IVACondiciones IVACondiciones
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Monedas in the schema.
        /// </summary>
        public virtual Monedas Monedas
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for DocTipos in the schema.
        /// </summary>
        public virtual DocTipos DocTipos
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for PersonasTipo in the schema.
        /// </summary>
        public virtual PersonasTipo PersonasTipo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for TelefonoTipo_IdTelefonoTipo in the schema.
        /// </summary>
        public virtual TelefonoTipo TelefonoTipo_IdTelefonoTipo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for TelefonoTipo_IdTelefonoTipoAlter in the schema.
        /// </summary>
        public virtual TelefonoTipo TelefonoTipo_IdTelefonoTipoAlter
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ContratoTipo in the schema.
        /// </summary>
        public virtual ContratoTipo ContratoTipo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Protocolos_IdContratoSolicitante in the schema.
        /// </summary>
        public virtual ISet<Protocolos> Protocolos_IdContratoSolicitante
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Protocolos_IdContratoTomador in the schema.
        /// </summary>
        public virtual ISet<Protocolos> Protocolos_IdContratoTomador
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Comprobantes in the schema.
        /// </summary>
        public virtual ISet<Comprobantes> Comprobantes
        {
            get;
            set;
        }
    }

}
