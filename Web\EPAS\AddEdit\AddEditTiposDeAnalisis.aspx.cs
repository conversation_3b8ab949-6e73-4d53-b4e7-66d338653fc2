﻿using Business.Provincias;
using Business.Services.Epas;
using Business.Services.Provincias;
using Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web.EPAS.AddEdit
{
    public partial class AddEditTiposDeAnalisis : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                txtcodigo.ClientEnabled = true;
                TxtID.Visible = false;
                btnVolverEditado.Visible = false;


                if (!IsPostBack)
                {
                    if (Request.QueryString["id"] != null)
                    {

                        int idtipoanalisis = Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["id"].ToString()));

                        TxtID.Text = idtipoanalisis.ToString();
                        btnVolverEditado.Visible = false;
                        CargartipoAnalisis(idtipoanalisis);

                        txtcodigo.ClientEnabled = false;

                    }
                    else
                    {
                        TxtID.Text = "0";
                        CB_Activo.Checked = true;
                        btnVolverEditado.Visible = false;

                    }
                }

            }
            catch
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1",
                    "showMessage('Error al cargar la página, consulte al administrador del sistema', 'error');", true);
            }
        }

        private void CargartipoAnalisis(int idtipoanalisis)
        {

            try
            {
                Domain.TipoAnalisis tipoanalisis = AnalisisTipoService.GetById(idtipoanalisis);

                if (tipoanalisis != null)
                {
                    TxtID.Text = idtipoanalisis.ToString();
                    txtcodigo.Text = tipoanalisis.Codigo.ToString();
                    txtDescripcion.Text = tipoanalisis.Descripcion.ToString();

                    CB_Activo.Checked = Convert.ToBoolean(tipoanalisis.Activo);
                }

            }
            catch (Exception ex)
            {
            }

        }

        protected void btnGrabar_Click(object sender, EventArgs e)
        {

            try
            {
                TipoAnalisis dep;

                //Verifica que se haya ingresado un codigo y nombre
                if (!string.IsNullOrEmpty(txtcodigo.Text) && !string.IsNullOrEmpty(txtDescripcion.Text))
                {
                    if (Request.QueryString["id"] != null)
                    {
                        // Se está editando un departamento existente.

                        int AnalisisId = Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["id"]));
                        //Obtengo el objeto departamento                        
                        dep = AnalisisTipoService.GetById(AnalisisId);
                        if (dep != null)
                        {
                            //El código si es edición, no se puede modificar, sólo actualizo descripción y activo y provincia
                            if (dep.Codigo.ToUpper().Equals(txtcodigo.Text.Trim().ToUpper()))
                            {
                                dep.Descripcion = txtDescripcion.Text.Trim().ToUpper();
                                dep.Activo = CB_Activo.Checked;
                                AnalisisTipoService.SaveOrUpdate(dep);
                                ScriptManager.RegisterStartupScript(this, this.GetType(), "message3", "showMessage('Fue editado correctamente.', 'success');", true);
                                txtcodigo.ClientEnabled = false;

                                Response.Redirect(Global.ApplicationPath + "/EPAS/Lista/TiposDeAnalisis.aspx", false);
                                Response.End();
                            }
                        }
                        //  ScriptManager.RegisterStartupScript(this, this.GetType(), "message1", "showMessage('El departamento ya existe en la base de datos.', 'error');", true);                                             
                    }
                    else
                    {
                        // Verificar si ya existe en la lista local, solo se verifica el código
                        if (AnalisisTipoService.ExisteCodigo(txtcodigo.Text.Trim()) || AnalisisTipoService.ExisteNombre(txtDescripcion.Text.Trim()))
                        {
                            ScriptManager.RegisterStartupScript(this, this.GetType(), "message2", "showMessage('Ya existe un Tipo de Analisis con ese código o nombre, no se puede guardar.', 'error');", true);
                        }
                        else
                        {
                            //No existe el código, puedo guardar en la base
                            dep = new TipoAnalisis();
                            dep.Codigo = txtcodigo.Text.Trim().ToUpper();
                            dep.Descripcion = txtDescripcion.Text.Trim().ToUpper();
                            dep.Activo = CB_Activo.Checked;
                            AnalisisTipoService.SaveOrUpdate(dep);
                            ScriptManager.RegisterStartupScript(this, this.GetType(), "message3", "showMessage('Agregado correctamente.', 'success');", true);
                            txtcodigo.ClientEnabled = false;
                            Response.Redirect(Global.ApplicationPath + "/EPAS/Lista/TiposDeAnalisis.aspx", false);
                        }

                    }



                }
                else
                {
                    ScriptManager.RegisterStartupScript(this, this.GetType(), "message4", "showMessage('Busque un departamento.', 'error');", true);
                }
            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "message5", "showMessage('Error al agregar Departamento, intente de nuevo.', 'error');", true);
            }


        }
    }
}