//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 3/11/2023 09:44:13
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class SexosRepository : NHibernateRepository<Domain.Sexos>, ISexosRepository
    {
        public SexosRepository(ISession session) : base(session)
        {
        }

        public virtual ICollection<Domain.Sexos> GetAll()
        {
            return session.CreateQuery(string.Format("from Sexos")).List<Domain.Sexos>();
        }

        public virtual Domain.Sexos GetByKey(int _Id)
        {
            return session.Get<Domain.Sexos>(_Id);
        }

        public virtual ICollection<Domain.Sexos> GetBySexos(bool Activo)
        {
            string hql = "from Sexos order BY Abreviatura asc";

            IQuery q = session.CreateQuery(hql);
            return q.List<Domain.Sexos>();

        }
    }
}
