﻿<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="Fuentes" table="Fuentes">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="NroDeFuente" type="String">
      <column name="Nro_de_Fuente" not-null="false"  sql-type="nvarchar" />
    </property>
    <property name="Descripcion" type="String">
      <column name="Descripcion" not-null="false" sql-type="nvarchar" />
    </property>
    <property name="Notas" type="String">
      <column name="Notas" not-null="false" sql-type="nvarchar" />
    </property>
    <property name="Latitud" type="String">
      <column name="Latitud" not-null="false" sql-type="nvarchar" />
    </property>
    <property name="Longitud" type="String">
      <column name="Longitud" not-null="false" sql-type="nvarchar" />
    </property>
    <property name="Activo" type="Boolean">
      <column name="Activo" not-null="false" sql-type="bit" />
    </property>
    <many-to-one name="FuentesTipos" class="FuentesTipos">
      <column name="IdFuente_Tipo" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <many-to-one name="Localidades" class="Localidades">
      <column name="IdLocalidad" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <set name="Protocolos" inverse="true" generic="true">
      <key>
        <column name="IdFuente" />
      </key>
      <one-to-many class="Protocolos" />
    </set>
    <set name="SubFuentes" inverse="true" generic="true">
      <key>
        <column name="IdFuente" />
      </key>
      <one-to-many class="SubFuentes" />
    </set>
  </class>
</hibernate-mapping>