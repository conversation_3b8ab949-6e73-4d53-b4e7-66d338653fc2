<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="Protocolos" table="Protocolos">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="NroProtocolo" type="Int32">
      <column name="NroProtocolo" not-null="false" precision="10" scale="0" sql-type="int" />
    </property>
    <property name="FechaCreado" type="DateTime">
      <column name="FechaCreado" not-null="false" sql-type="datetime2" />
    </property>
    <property name="FechaToma" type="DateTime">
      <column name="FechaToma" not-null="false" sql-type="datetime2" />
    </property>
    <property name="RecepcionToma" type="DateTime">
      <column name="RecepcionToma" not-null="false" sql-type="datetime2" />
    </property>
    <property name="Modulo" type="Int32">
      <column name="Modulo" not-null="false" precision="10" scale="0" sql-type="int" />
    </property>
    <property name="MontoTotal" type="Decimal">
      <column name="MontoTotal" not-null="false" precision="19" scale="4" sql-type="money" />
    </property>
    <property name="ObservacionSitioToma" type="String">
      <column name="ObservacionSitioToma" not-null="false" sql-type="nvarchar" />
    </property>
    <property name="Expediente" type="String">
      <column name="Expediente" not-null="false" length="50" sql-type="nvarchar" />
    </property>
    <property name="ImprimirLeyendas" type="Boolean">
      <column name="ImprimirLeyendas" not-null="false" sql-type="bit" />
    </property>
    <property name="UnidadesPrimarias" type="Boolean">
      <column name="UnidadesPrimarias" not-null="false" sql-type="bit" />
    </property>
    <property name="Laboratorio" type="String">
      <column name="Laboratorio" not-null="false" sql-type="nvarchar" />
    </property>
    <property name="OpcionesResultados" type="String">
      <column name="OpcionesResultados" not-null="false" sql-type="nvarchar" />
    </property>
    <property name="Informes" type="String">
      <column name="Informes" not-null="false" sql-type="nvarchar" />
    </property>
    <property name="Observaciones" type="String">
      <column name="Observaciones" not-null="false" sql-type="nvarchar" />
    </property>
    <property name="Veredicto" type="String">
      <column name="Veredicto" not-null="false" length="50" sql-type="nvarchar" />
    </property>
    <property name="ObservacionVeredicto" type="String">
      <column name="ObservacionVeredicto" not-null="false" sql-type="nvarchar" />
    </property>
    <property name="FechaAprobado" type="DateTime">
      <column name="FechaAprobado" not-null="false" sql-type="datetime2" />
    </property>
    <many-to-one name="ProtocolosEstados" class="ProtocolosEstados">
      <column name="IdEstado" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <many-to-one name="Usuario_IdUsuario" class="Usuario">
      <column name="IdUsuario" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <many-to-one name="Usuario_IdUsuarioAprobo" class="Usuario">
      <column name="IdUsuarioAprobo" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <many-to-one name="Contratos_IdContratoSolicitante" class="Contratos">
      <column name="IdContratoSolicitante" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <many-to-one name="Contratos_IdContratoTomador" class="Contratos">
      <column name="IdContratoTomador" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <many-to-one name="Fuentes" class="Fuentes">
      <column name="IdFuente" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <many-to-one name="SubFuentes" class="SubFuentes">
      <column name="IdSubFuente" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <many-to-one name="TipoAnalisis" class="TipoAnalisis">
      <column name="IdTipoAnalisis" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <set name="ProtocoloDeterminaciones" inverse="true" generic="true">
      <key>
        <column name="IdProtocolo" />
      </key>
      <one-to-many class="ProtocoloDeterminaciones" />
    </set>
  </class>
</hibernate-mapping>