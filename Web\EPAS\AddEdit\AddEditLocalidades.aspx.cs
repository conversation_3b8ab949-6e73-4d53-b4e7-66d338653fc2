﻿using Business.Provincias;
using Business.Services.Contratos;
using Business.Services.Provincias;
using DevExpress.Web;
using Domain;
using System;
using System.Collections.Generic;
using System.Diagnostics.Contracts;
using System.Linq;
using System.Security.Cryptography;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Web.EPAS.Lista;

namespace Web.EPAS.AddEdit
{
    public partial class AddEditLocalidades : System.Web.UI.Page
    {
        protected void Page_Load(object sender,EventArgs e) {

            try {
                btnVolverEditado.Visible = false;

                if(!IsPostBack) {

                    LlenadoCmb_Provincias();
                    LlenadoCmb_Zonas();
                    btnVolverEditado.Visible = false;

                    if (Request.QueryString["id"] != null) {

                        int idLocalidad = Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["id"].ToString()));
                        txtID.Text = idLocalidad.ToString();
                        btnVolverEditado.Visible = false;
                        CargarLocalidad(idLocalidad);

                    } else {
                        txtID.Text = "0"; 
                        txtID.Visible = false;
                        CB_Activo.Checked = true;
                        btnVolverEditado.Visible = false;
                    }


                }
            } catch {
                ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('Error al cargar la página, consulte al administrador del sistema', 'error');",true);
            }
        }

        private void CargarLocalidad(int idLocalidad) {

            try {

                Domain.Localidades localidad = LocalidadesService.GetById(idLocalidad);

                if(localidad != null) {
                    
                    txtID.Visible = false;
                    txtCodigo.Text = localidad.Id.ToString();
                    txtDescripcion.Text = localidad.Nombre;
                    CB_Activo.Checked=true;
                    CmbProvincias.SelectedItem = CmbProvincias.Items.FindByValue(localidad.Provincias.Id.ToString());

                    CompletarCombosDpto(Convert.ToInt32(localidad.Departamentos.Provincias.Id.ToString()));
                    CmbDpto.SelectedItem = CmbDpto.Items.FindByValue(localidad.Departamentos.Id.ToString());

                    CmbZonas.SelectedItem = CmbZonas.Items.FindByValue(localidad.Zonas.Id.ToString());


                }

            } catch(Exception ex) {
            }

        }
        protected void CmbDpto_Callback(object sender, CallbackEventArgsBase e)
        {
            try
            {
                if (CmbProvincias.SelectedItem != null)
                {
                    CompletarCombosDpto(Convert.ToInt32(CmbProvincias.SelectedItem.Value));
                }
            }
            catch (Exception ex)
            {
            }

        }
        protected void LlenadoCmb_Provincias()
        {
            CmbProvincias.DataSource = ProvinciasService.GetAll();
            CmbProvincias.TextField = "Descripcion";
            CmbProvincias.ValueField = "Id";
            CmbProvincias.DataBind();
            CmbProvincias.Items.Insert(0, new ListEditItem());
        }

        protected void CompletarCombosDpto(int idProvincia)
        {
            if (idProvincia > 0)
            {
                CmbDpto.DataSource = DepartamentosService.GetByProvincias(idProvincia);
                CmbDpto.TextField = "Descripcion";
                CmbDpto.ValueField = "Id";
                CmbDpto.DataBind();
                CmbDpto.Items.Insert(0, new ListEditItem());
            }
        }
        protected void LlenadoCmb_Zonas()
        {
            CmbZonas.DataSource = ZonasService.GetAll(true);
            CmbZonas.TextField = "Descripcion";
            CmbZonas.ValueField = "Id";
            CmbZonas.DataBind();
            CmbZonas.Items.Insert(0, new ListEditItem());

        }

        //protected void LlenadoCmb_Dpto()
        //{
            
        //    CmbDpto.DataSource = DepartamentosService.GetAll();
        //    CmbDpto.TextField = "Descripcion";
        //    CmbDpto.ValueField = "Id";
        //    CmbDpto.DataBind();
        //    CmbDpto.Items.Insert(0, new ListEditItem());

        //}

        protected void btnGrabar_Click(object sender,EventArgs e) {

            try {
                Domain.Localidades loc;

              //  if(!string.IsNullOrEmpty(txtCodigo.Text) && !string.IsNullOrEmpty(txtDescripcion.Text)) {
                    if(Request.QueryString["id"] != null) {
                        int locId = Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["id"]));
                        loc = LocalidadesService.GetById(locId);

                        if(loc != null) {
                            if(loc.Codigo.ToUpper().Equals(txtCodigo.Text.Trim().ToUpper())) {
                                loc.Nombre = txtDescripcion.Text.Trim().ToUpper();
                                loc.Provincias = ProvinciasService.GetById(Convert.ToInt32(CmbProvincias.SelectedItem.Value));
                                loc.Departamentos = DepartamentosService.GetById(Convert.ToInt32(CmbDpto.SelectedItem.Value));
                                loc.Zonas = ZonasService.GetById(Convert.ToInt32(CmbZonas.SelectedItem.Value));
                                loc.Activo = CB_Activo.Checked;

                                LocalidadesService.SaveOrUpdate(loc);
                                ScriptManager.RegisterStartupScript(this,this.GetType(),"message3","showMessage('Se edito correctamente.', 'success');",true);
                                txtCodigo.ClientEnabled = false;

                                // Response.Redirect(Global.ApplicationPath + "Epas/Lista/Localidades.aspx", false);
                            }


                        }
                    } else {
                    if(LocalidadesService.ExisteCodigo(txtCodigo.Text.Trim())) {
                        ScriptManager.RegisterStartupScript(this,this.GetType(),"message2","showMessage('Ya existe una localidad con ese código, no se puede guardar.', 'error');",true);
                    } else {
                    //No existe el código, puedo guardar en la base


                    //el codigo es autogenerado
                   
                            loc = new Domain.Localidades();
                            loc.Codigo = NumeracionServices.getByCodigo("LOC").Numero.ToString().Trim().ToUpper();
                            loc.Nombre = txtDescripcion.Text.Trim().ToUpper();

                            loc.Provincias = ProvinciasService.GetById(Convert.ToInt32(CmbProvincias.SelectedItem.Value));
                            loc.Departamentos = DepartamentosService.GetById(Convert.ToInt32(CmbDpto.SelectedItem.Value));
                            loc.Zonas = ZonasService.GetById(Convert.ToInt32(CmbZonas.SelectedItem.Value));
                            loc.Activo = CB_Activo.Checked;

                            LocalidadesService.SaveOrUpdate(loc);
                            ScriptManager.RegisterStartupScript(this,this.GetType(),"message3","showMessage('Agregado correctamente.', 'success');",true);
                            txtCodigo.ClientEnabled = false;
                            txtCodigo.Text = loc.Codigo;

                            //  Response.Redirect(Global.ApplicationPath + "Epas/Lista/Localidades.aspx", false);
                        }

                    }

               /* } else {
                    ScriptManager.RegisterStartupScript(this,this.GetType(),"ksdfsd4eddsedy1","showMessage('Busque una Localidad!', 'error');",true);

                }*/
            } catch(Exception ex) {
                ScriptManager.RegisterStartupScript(this,this.GetType(),"ksdfsd4eddsedy1","showMessage('Error al agregar Localidad, intente de nuevo', 'error');",true);

            }

        }
    }   
}