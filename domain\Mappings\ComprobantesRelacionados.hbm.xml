<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="ComprobantesRelacionados" table="ComprobantesRelacionados">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Importe" type="Decimal">
      <column name="Importe" not-null="false" precision="19" scale="4" sql-type="money" />
    </property>
    <many-to-one name="Comprobantes" class="Comprobantes">
      <column name="IdComprobantePrincipal" not-null="true" precision="10" scale="0" sql-type="int" />
    </many-to-one>
  </class>
</hibernate-mapping>