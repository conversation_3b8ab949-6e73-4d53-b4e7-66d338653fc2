﻿using DAO;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Provincias
{
    public class ZonasService
    {

        //Trae todo
        public static IList<Domain.Zonas> GetAll(bool v)
        {

            IList<Domain.Zonas> listaAux;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                listaAux = (IList<Domain.Zonas>)new ZonasRepository(sess).GetAll();
                sess.Close();
                sess.Dispose();
            }
            return (listaAux);

        }
        //Traelocalidadesconesazona
        public static IList<Domain.Zonas> GetByLocalidades(int idLocalidades)
        {

            IList<Domain.Zonas> listaAux;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {

                listaAux = (IList<Domain.Zonas>)new ZonasRepository(sess).GetByLocalidades(idLocalidades);
                sess.Close();
                sess.Dispose();
            }
            return (listaAux);

        }

        //obtener por id
        public static Domain.Zonas GetById(int id)
        {

            Domain.Zonas p;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {

                p = (Domain.Zonas)new ZonasRepository(sess).GetByKey(id);
                sess.Close();
                sess.Dispose();
                return p;
            }

        }


        public static bool ExisteCodigo(string codigo)
        {

            Domain.Zonas p;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {

                p = (Domain.Zonas)new ZonasRepository(sess).ExisteCodigo(codigo);
                sess.Close();
                sess.Dispose();
                return p != null;

            }

        }


        public static bool ExisteNombre(string Descripcion)
        {
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                return new ZonasRepository(sess).ExisteNombre(Descripcion);
            }
        }

        //agregar y actualizar
        public static void SaveOrUpdate(Domain.Zonas p)
        {

            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {

                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {

                    try
                    {

                        new ZonasRepository(sess).Add(p);

                        tx.Commit();
                    }
                    catch (Exception e)
                    {

                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {

                        sess.Close();
                        sess.Dispose();
                    }

                }

            }

        }

        //borrar
        public static void Delete(Domain.Zonas p)
        {
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new ZonasRepository(sess).Remove(p);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }

                }

            }

        }

    }
}

