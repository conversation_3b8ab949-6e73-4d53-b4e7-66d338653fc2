﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="AddEditRoles.aspx.cs" ValidateRequest="true"
    Inherits="Web.Config.AddEditRoles" %>

<%@ Register Assembly="DevExpress.Web.ASPxTreeList.v18.1" Namespace="DevExpress.Web.ASPxTreeList" TagPrefix="dx" %>


<%@ Register Assembly="DevExpress.Web.v18.1" Namespace="DevExpress.Web" TagPrefix="dx" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <asp:ScriptManager ID="ScriptManager1" runat="server"></asp:ScriptManager>
   <!-- Page header -->
    <div class="page-header page-header-light shadow">
     <div class="page-header-content d-lg-flex border-top">
         <div class="d-flex">
             <div class="breadcrumb py-2">
                    <a href="<%= Web.Util.Helpers.GetApplicationPath() %>/Index.aspx" class="breadcrumb-item"><i class="icon-home2 mr-2"></i>Inicio</a>
                    <a href="<%= Web.Util.Helpers.GetApplicationPath() %>/config/Roles.aspx" class="breadcrumb-item"><i class="icon-gear mr-2"></i>Áreas</a>
                    <span class="breadcrumb-item active">
                        <asp:Literal ID="ltr_nombreBreadcrumb" runat="server">Agregar ó editar</asp:Literal></span>
                </div>

                <a href="#" class="header-elements-toggle text-default d-md-none"><i class="icon-more"></i></a>
            </div>
                  
        </div>
    </div>
    <!-- /page header -->


    <!-- Page content -->
    <div class="page-content pt-0">

        <!-- Main content -->
        <div class="content-wrapper">

            <!-- Content area -->
            <div class="content">

                <!-- Basic card -->
                <div class="card">
                    <div class="card-header header-elements-inline">
                        <h5 class="card-title"></h5>
                        <div class="header-elements">
                            <div class="list-icons">
                                <a class="list-icons-item" data-action="collapse"></a>

                            </div>
                        </div>
                    </div>

                    <div class="card-body">
                        <div id="ResizedDiv" style="width: 100%;">
                            <dx:ASPxFormLayout ID="ASPxFormLayout1" runat="server" Width="100%" ClientInstanceName="FormLayout">
                                <Items>
                                    <dx:LayoutGroup Width="100%" Caption="Datos del rol" ColumnCount="3">
                                        <GridSettings StretchLastItem="true" WrapCaptionAtWidth="660">
                                            <Breakpoints>
                                                <dx:LayoutBreakpoint MaxWidth="500" ColumnCount="1" Name="S" />
                                                <dx:LayoutBreakpoint MaxWidth="800" ColumnCount="2" Name="M" />
                                            </Breakpoints>
                                        </GridSettings>
                                        <Items>
                                            <dx:LayoutItem Caption="Codigo" VerticalAlign="Middle">
                                                <SpanRules>
                                                    <dx:SpanRule ColumnSpan="1" RowSpan="1" BreakpointName="S"></dx:SpanRule>
                                                    <dx:SpanRule ColumnSpan="2" RowSpan="1" BreakpointName="M"></dx:SpanRule>
                                                </SpanRules>
                                                <LayoutItemNestedControlCollection>
                                                    <dx:LayoutItemNestedControlContainer>
                                                        <dx:ASPxTextBox ID="txt_codigo" runat="server" Width="100%">
                                                            <ValidationSettings SetFocusOnError="True" ErrorText="Campo requerido" Display="Dynamic">
                                                                <RequiredField IsRequired="True" ErrorText="Campo requerido" />
                                                            </ValidationSettings>
                                                        </dx:ASPxTextBox>
                                                    </dx:LayoutItemNestedControlContainer>
                                                </LayoutItemNestedControlCollection>
                                            </dx:LayoutItem>

                                            <dx:LayoutItem Caption="Nombre" VerticalAlign="Middle">
                                                <SpanRules>
                                                    <dx:SpanRule ColumnSpan="1" RowSpan="1" BreakpointName="S"></dx:SpanRule>
                                                    <dx:SpanRule ColumnSpan="1" RowSpan="1" BreakpointName="M"></dx:SpanRule>
                                                </SpanRules>
                                                <LayoutItemNestedControlCollection>
                                                    <dx:LayoutItemNestedControlContainer>
                                                        <dx:ASPxTextBox ID="txt_nombre" runat="server" Width="100%">
                                                            <ValidationSettings SetFocusOnError="True" ErrorText="Campo requerido" Display="Dynamic">
                                                                <RequiredField IsRequired="True" ErrorText="Campo requerido" />
                                                            </ValidationSettings>
                                                        </dx:ASPxTextBox>
                                                    </dx:LayoutItemNestedControlContainer>
                                                </LayoutItemNestedControlCollection>
                                            </dx:LayoutItem>


                                            <dx:LayoutItem Caption="Observacion" VerticalAlign="Middle">
                                                <SpanRules>
                                                    <dx:SpanRule ColumnSpan="1" RowSpan="1" BreakpointName="S"></dx:SpanRule>
                                                    <dx:SpanRule ColumnSpan="2" RowSpan="1" BreakpointName="M"></dx:SpanRule>
                                                </SpanRules>
                                                <LayoutItemNestedControlCollection>
                                                    <dx:LayoutItemNestedControlContainer>
                                                        <dx:ASPxTextBox runat="server" ID="txt_observacion" Width="100%">
                                                        </dx:ASPxTextBox>
                                                    </dx:LayoutItemNestedControlContainer>
                                                </LayoutItemNestedControlCollection>
                                            </dx:LayoutItem>

                                            <dx:LayoutItem Caption="Super admin" VerticalAlign="Middle">
                                                <SpanRules>
                                                    <dx:SpanRule ColumnSpan="1" RowSpan="1" BreakpointName="S"></dx:SpanRule>
                                                    <dx:SpanRule ColumnSpan="1" RowSpan="1" BreakpointName="M"></dx:SpanRule>
                                                </SpanRules>
                                                <LayoutItemNestedControlCollection>
                                                    <dx:LayoutItemNestedControlContainer>
                                                        <dx:ASPxCheckBox ID="cb_superadmin" runat="server"></dx:ASPxCheckBox>
                                                    </dx:LayoutItemNestedControlContainer>
                                                </LayoutItemNestedControlCollection>
                                            </dx:LayoutItem>
                                            
                                            <dx:LayoutItem Caption="Módulos/Permisos" VerticalAlign="Middle" ColumnSpan="3">
                                                <SpanRules>
                                                    <dx:SpanRule ColumnSpan="1" RowSpan="1" BreakpointName="S"></dx:SpanRule>
                                                    <dx:SpanRule ColumnSpan="1" RowSpan="1" BreakpointName="M"></dx:SpanRule>
                                                </SpanRules>
                                                <LayoutItemNestedControlCollection>
                                                    <dx:LayoutItemNestedControlContainer>
                                                        <dx:ASPxCallback ID="callback_modal_permisos" ClientInstanceName="callback_modal_permisos" OnCallback="callback_modal_permisos_Callback" runat="server">
                                                            <ClientSideEvents EndCallback="function(s,e){modalPermisos.Show();}" />
                                                        </dx:ASPxCallback>

  <dx:ASPxPageControl Paddings-PaddingTop="5px" Width="100%"  EnableViewState="false" ID="ASPxPageControl2"
                    EnableTabScrolling="false"
                    ClientInstanceName="tabs"
                    Theme="Material"
                    runat="server" ActiveTabIndex="0" TabSpacing="1px"
                    EnableHierarchyRecreation="false">
      <TabPages>
       <dx:TabPage Text="Web">
                <ContentCollection>
                    <dx:ContentControl ID="ContentControl1" runat="server">
                            <dx:ASPxTreeList ID="tl_web" AutoGenerateColumns="False" runat="server" KeyFieldName="Nodo"
                                ParentFieldName="NodoPadre" OnPreRender="tl_web_PreRender" >
                                                            <SettingsBehavior ExpandCollapseAction="NodeDblClick" />
                                                            <SettingsSelection Enabled="True" Recursive="true" />
                                                            <Columns>
                                                                <dx:TreeListDataColumn FieldName="Descripcion" Caption="Descripcion" />
                                                                
                                                            </Columns>
                                                        </dx:ASPxTreeList>
                    </dx:ContentControl>
                </ContentCollection>
            </dx:TabPage>
            <dx:TabPage Text="App Windows" Visible="false">
                <ContentCollection>
                    <dx:ContentControl ID="ContentControl2" runat="server">
                            <dx:ASPxTreeList ID="tl_app" AutoGenerateColumns="False" runat="server" KeyFieldName="Nodo"
                                ParentFieldName="NodoPadre" OnPreRender="tl_app_PreRender" >
                                                            <SettingsBehavior ExpandCollapseAction="NodeDblClick" />
                                                            <SettingsSelection Enabled="True" Recursive="true" />
                                                            <Columns>
                                                                <dx:TreeListDataColumn FieldName="Descripcion" Caption="Descripcion" />
                                                                <dx:TreeListDataColumn Caption="" Width="110">

                                                                    <DataCellTemplate>

                                                                        <dx:ASPxButton runat="server" ID="btnPermisos" RenderMode="Link" Text="Permisos" OnInit="btnPermisos_Init"
                                                                            CommandArgument='<%# Eval("Id" )%>' AutoPostBack="false">
                                                                          
                                                                        </dx:ASPxButton>


                                                                    </DataCellTemplate>
                                                                </dx:TreeListDataColumn>
                                                            </Columns>
                                                        </dx:ASPxTreeList>
                    </dx:ContentControl>
                </ContentCollection>
            </dx:TabPage>
          </TabPages>
      </dx:ASPxPageControl>

                                                 
                                                    </dx:LayoutItemNestedControlContainer>
                                                </LayoutItemNestedControlCollection>
                                            </dx:LayoutItem>


                                            <dx:LayoutItem ShowCaption="False" HorizontalAlign="Center" VerticalAlign="Middle" Paddings-PaddingTop="20px" CssClass="lastItem">
                                                <SpanRules>
                                                    <dx:SpanRule ColumnSpan="1" RowSpan="1" BreakpointName="S"></dx:SpanRule>
                                                    <dx:SpanRule ColumnSpan="1" RowSpan="1" BreakpointName="M"></dx:SpanRule>
                                                </SpanRules>
                                                <LayoutItemNestedControlCollection>
                                                    <dx:LayoutItemNestedControlContainer>
                                                        <dx:ASPxButton runat="server" ID="btn_volver" Text="Volver" CssClass="bg-secondary w-25 me-5" AutoPostBack="true" OnClick="btn_volver_Click" Image-IconID="navigation_backward_16x16office2013" />
                                                        <dx:ASPxButton runat="server" ID="btn_guardar" Text="Guardar" Theme="Material" OnClick="btn_guardar_Click" Image-IconID="actions_save_16x16devav" AutoPostBack="false" />
                                                    </dx:LayoutItemNestedControlContainer>
                                                </LayoutItemNestedControlCollection>
                                            </dx:LayoutItem>
                                        </Items>
                                    </dx:LayoutGroup>
                                </Items>
                            </dx:ASPxFormLayout>
                        </div>
                    </div>
                </div>
                <!-- /basic card -->
            </div>
        </div>

    </div>

    <dx:ASPxPopupControl ID="modalPermisos" runat="server" Width="450" Height="350" CloseAction="CloseButton" CloseOnEscape="true" Modal="True"
        PopupHorizontalAlign="WindowCenter" PopupVerticalAlign="WindowCenter" ClientInstanceName="modalPermisos"
        HeaderText="Permisos del módulo" AllowDragging="True" PopupAnimationType="Fade" EnableViewState="False" AutoUpdatePosition="false"  
        ScrollBars="Vertical">
        <ClientSideEvents Shown="function(s, e) { CallbackPanelPermisos.PerformCallback(); }" ></ClientSideEvents>
        <ContentCollection>
            <dx:PopupControlContentControl runat="server">
             


               
                <dx:ASPxCallbackPanel ID="CallbackPanelPermisos" EnableCallbackAnimation="false" SettingsLoadingPanel-Enabled="true" runat="server" Width="500px"
                    ClientInstanceName="CallbackPanelPermisos" OnCallback="CallbackPanelPermisos_Callback">
                    <PanelCollection>
                        <dx:PanelContent>

                            <dx:ASPxTreeList ID="tl_permisos" ClientInstanceName="tl_permisos" runat="server" EnableViewState="false"  AutoGenerateColumns="False" 
                                KeyFieldName="Id"                                
                                OnCustomDataCallback="tl_permisos_CustomDataCallback">
                                 <ClientSideEvents SelectionChanged="function(s, e){ s.PerformCustomDataCallback('');  }" />
                                <SettingsBehavior />
                                <SettingsSelection Enabled="True" Recursive="false" />
                                <Columns>
                                    <dx:TreeListDataColumn FieldName="Codigo" Caption="Código" Width="25" />
                                    <dx:TreeListDataColumn FieldName="Nombre" Caption="Nombre" />
                                </Columns>
                            </dx:ASPxTreeList>
                            </dx:PanelContent>
                        </PanelCollection>
                    </dx:ASPxCallbackPanel>

                   

                <dx:ASPxCallback ID="callback_save" ClientInstanceName="callback_save" OnCallback="callback_save_Callback" runat="server">
                    <ClientSideEvents CallbackComplete="function(s, e) { modalPermisos.Hide(); }" />
                </dx:ASPxCallback>
                <dx:ASPxButton ID="btOK" runat="server" Text="Guardar" Width="80px" ClientInstanceName="btnsave" Theme="Material" AutoPostBack="false" Style="float: right; margin-right: 8px">
                    <ClientSideEvents Click="function(s, e) { callback_save.PerformCallback(); }" />
                </dx:ASPxButton>
            </dx:PopupControlContentControl>
        </ContentCollection>
    </dx:ASPxPopupControl>

</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="footer" runat="server">
</asp:Content>
