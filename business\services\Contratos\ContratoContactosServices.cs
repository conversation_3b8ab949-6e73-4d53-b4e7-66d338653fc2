﻿using Business.Services.Caja;
using DAO;
using Domain;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Contratos
{
    public class ContratoContactosServices
    {
        public static void SaveOrUpdate(ContratoContactos contratoContactos)
        {

            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new ContratoContactosRepository(sess).Add(contratoContactos);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }
            }
        }

        public static IList<ContratoContactos> GetAllByIdContrato(int IdContrato) {
            IList<ContratoContactos> lista;
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                lista = new ContratoContactosRepository(sess).GetAllByIdContrato(IdContrato) as IList<Domain.ContratoContactos>;

                foreach(Domain.ContratoContactos contacto in lista) {

                    NHibernateUtil.Initialize(contacto.DocTipos);
                    NHibernateUtil.Initialize(contacto.Sexos);

                    foreach(ContratoContactosEmail b in contacto.ContratoContactosEmails) {

                        NHibernateUtil.Initialize(b);
                    }

                    foreach(ContratoContactosTel b in contacto.ContratoContactosTels) {

                        NHibernateUtil.Initialize(b.TelefonoTipo);
                    }
                }

                sess.Close();
                sess.Dispose();
                return (lista);
            }
        }

        public static IList<ContratoContactos> GetAll()
        {
            IList<ContratoContactos> listaAux;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                listaAux = (IList<ContratoContactos>)new ContratoContactosRepository(sess).GetAll();
                sess.Close();
                sess.Dispose();
            }

            return (listaAux);
        }

        public static ContratoContactos GetById(int id)
        {
            ContratoContactos CC;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                CC = new ContratoContactosRepository(sess).GetByKey(id);

                if (CC != null)
                {
                    NHibernateUtil.Initialize(CC);

                }
                sess.Close();
                sess.Dispose();
                return CC;
            }
        }
        public static void Delete(Domain.ContratoContactos CC)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new ContratoContactosRepository(sess).Remove(CC);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }

            }
        }
    }
}
