﻿using Business.Provincias;
using Business.Services.Provincias;
using Business.Services.Usuarios;
using DevExpress.Web;
using Domain;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web.EPAS.Lista
{
    public partial class Departamentos : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                CargarDatosEnGv_Dpto();

                if (!IsPostBack)
                {
                   
                }

            }
            catch (Exception ex)
            {
                string g = ex.Message;
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('"+g+"', 'error');", true);

            }
        }

        private void CargarDatosEnGv_Dpto()
        {

            try
            {

                List<Domain.Departamentos> departamentos = (List<Domain.Departamentos>) Business.Services.Provincias.DepartamentosService.GetAll();          
                gv_Dpto.DataSource = departamentos;
                gv_Dpto.DataBind();
            }
            catch (Exception ex)
            {
                string g = ex.Message;
            }

        }


        protected void btnEditar_Click(object sender, EventArgs e)
        {

            try
            {
                ASPxButton btn = (ASPxButton)sender;
                if (btn != null)
                {
                    GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer)btn.NamingContainer;
                    if (c != null)
                    {
                        var ind = DataBinder.Eval(c.DataItem, "Id");

                        if (ind != null)
                        {
                            Domain.Departamentos dpto = DepartamentosService.GetById(Convert.ToInt32(ind));

                            if(dpto.Activo == false)
                            {
                                string message = string.Format("El Departamento {0} no se puede editar ya que está anulado.",dpto.Descripcion);
                                ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('" + message + "')",true);

                            } else
                            {
                                Response.Redirect(Global.ApplicationPath + "/EPAS/AddEdit/AddEditDepartamentos.aspx?id=" + Util.Helpers.Encrypt(ind.ToString()), false);

                            }

                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al tratar de editar un elemento, consulte al administrador del sistema', 'error');", true);
            }
        }

        protected void btnAnularActivar_Click(object sender, EventArgs e)
        {
            try
            {
                ASPxButton btn = (ASPxButton)sender;
                GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer)btn.NamingContainer;
                var ind = DataBinder.Eval(c.DataItem, "Id");

                Domain.Departamentos DE = DepartamentosService.GetById(Convert.ToInt32(ind));
                DE.Activo = !DE.Activo;

                DepartamentosService.SaveOrUpdate(DE);
                CargarDatosEnGv_Dpto();

            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al tratar de eliminar un elemento, consulte al administrador del sistema', 'error');", true);

            }
        }
    }
}