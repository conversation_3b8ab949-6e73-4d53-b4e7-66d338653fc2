﻿<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="BancoCuentas" table="BancoCuentas">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Descripcion" type="String">
      <column name="Descripcion" not-null="true" length="50" sql-type="nvarchar" />
    </property>
    <property name="Comision" type="Decimal">
      <column name="Comision" not-null="false" precision="19" scale="4" sql-type="money" />
    </property>
    <property name="Retencion" type="Decimal">
      <column name="Retencion" not-null="false" precision="19" scale="4" sql-type="money" />
    </property>
    <property name="Deshabilitada" type="Int32">
      <column name="Deshabilitada" not-null="false" precision="10" scale="0" sql-type="int" />
    </property>
    <property name="IdOtraMoneda" type="Int32">
      <column name="`IdOtraMoneda*`" not-null="false" precision="10" scale="0" sql-type="int" />
    </property>
    <property name="IdPeriodo" type="Int32">
      <column name="IdPeriodo" not-null="true" precision="10" scale="0" sql-type="int" />
    </property>
    <many-to-one name="BancosTarjetas" class="BancosTarjetas">
      <column name="IdBancoTarjeta" not-null="true" precision="10" scale="0" sql-type="int" />
    </many-to-one>
  </class>
</hibernate-mapping>