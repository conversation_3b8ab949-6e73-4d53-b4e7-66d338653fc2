﻿using DAO;
using Domain;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Caja
{
    public class CuponesTarjetasServices
    {
        public static void SaveOrUpdate(CuponesTarjetas ct)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                       new CuponesTarjetasRepository(sess).Add(ct);

                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }
            }
        }
        public static IList<CuponesTarjetas> GetAll()
        {
            IList<CuponesTarjetas> listaAux;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                listaAux = (IList<CuponesTarjetas>)new CuponesTarjetasRepository(sess).GetAll();
                sess.Close();
                sess.Dispose();
            }

            return (listaAux);
        }
        public static CuponesTarjetas getById(int id)
        {
            CuponesTarjetas u;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                u = new CuponesTarjetasRepository(sess).GetByKey(id);

                if(u != null)

                {
                    NHibernateUtil.Initialize(u);

                }
                sess.Close();
                sess.Dispose();
                return u;
            }
        }
        public static void Delete(Domain.CuponesTarjetas u)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new CuponesTarjetasRepository(sess).Remove(u);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }

            }
        }

    }
}
