﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 18/03/2019 13:19:19
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using Domain;
using System;
using System.Collections.Generic;

namespace DAO

{
    public partial interface ITipoPermisoRepository : IRepository<TipoPermiso>
    {
        IList<TipoPermiso> GetAll();
        TipoPermiso GetByKey(int _Id);
    }
}
