﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 22/5/2023 14:55:38
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class RetencionesRepository : NHibernateRepository<Domain.Retenciones>, IRetencionesRepository
    {
        public RetencionesRepository(ISession session) : base(session)
        {
        }

        public virtual ICollection<Domain.Retenciones> GetAll()
        {
            return session.CreateQuery(string.Format("from Retenciones")).List<Domain.Retenciones>();
        }
        public virtual ICollection<Domain.Retenciones> GetAllRetenciones()
        {
            return session.CreateQuery(string.Format("from Retenciones")).List<Domain.Retenciones>();
        }
        public virtual Domain.Retenciones GetByKey(int _Id)
        {
            return session.Get<Domain.Retenciones>(_Id);
        }
    }
}
