﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 11/03/2019 14:47:12
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using Domain;
using System;
using System.Collections.Generic;

namespace DAO
{
    public partial interface IModuloRepository : IRepository<Modulo>
    {
        IList<Modulo> GetAll();
        Modulo GetByKey(int _Id);
    }
}
