﻿using Business.Provincias;
using Business.Services.Provincias;
using DevExpress.Web;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web.EPAS.Lista
{
    public partial class Provincias : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            CargarDatosEnGripV_Provincias();
            if (!IsPostBack)
            {

            }

        }

        protected void CargarDatosEnGripV_Provincias()
        {
            try
            {
                List<Domain.Provincias> provincias = (List<Domain.Provincias>)
                   ProvinciasService.GetAll();
                                
                //Grip_Provincias.DataSource = provincias;
                //Grip_Provincias.DataBind();
            }
            catch (Exception ex)
            {
                string g = ex.Message;
            }

        }



        //protected void btnEditar_Click(object sender, EventArgs e)
        //{
        //    try
        //    {
        //        ASPxButton btn = (ASPxButton)sender;
        //        if (btn != null)
        //        {
        //            GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer)btn.NamingContainer;
        //            if (c != null)
        //            {
        //                var ind = DataBinder.Eval(c.DataItem, "Id");
        //                if (ind != null)
        //                {

        //                    Response.Redirect(Global.ApplicationPath + "/EPAS/AddEdit/AddEditZonas.aspx?id=" +
        //                        Util.Helpers.Encrypt(ind.ToString()), false);
        //                }
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al tratar de editar un elemento, consulte al administrador del sistema', 'error');", true);
        //    }
        //}
    }
}