﻿using Business.Services.Contratos;
using Business.Services.Usuarios;
using DevExpress.Web;
using Domain;
using System;
using System.Collections.Generic;
using System.Diagnostics.Contracts;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web.Clientes {
    public partial class BuscarContrato : System.Web.UI.Page {

        protected void Page_Init(Object sender,EventArgs e) {

            try {


                if(!IsPostBack) {

                    Session["ListaContratos"] = null;

                }


            } catch(Exception ex) { 
            }


        }
        protected void Page_Load(object sender,EventArgs e) {            

            if(!IsPostBack) {
                GrVBuscarSolicitante.DataBind();
            }

        }


        protected void Btn_BuscarSolicitante_Click(object sender,EventArgs e) {
            

        }

        protected void GrVBuscarSolicitante_DataBinding(object sender,EventArgs e) {

            GrVBuscarSolicitante.DataSource = GetDatos();

        }

        private IList<Domain.Contratos> GetDatos() {

            if(cmbBuscarSolicitante.SelectedItem != null) {

                IList<Domain.Contratos> lst = Business.Services.Contratos.ContratosServices.GetByCamposBusqueda(Convert.ToInt32(cmbBuscarSolicitante.Value),txtBarraBusqueda.Text);

                if(lst != null && lst.Count > 0) {

                    return lst;

                } else {

                    ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('No se encontraron resultados.')",true);
                    return null;
                }
            } else {
                return null;
            }          
        }

        protected void btnEditar_Click(object sender,EventArgs e) {

            try {
                ASPxButton btn = (ASPxButton) sender;
                if (btn != null)
                {
                    GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer)btn.NamingContainer;
                    if (c != null)
                    {
                        var ind = DataBinder.Eval(c.DataItem, "Id");
                        if (ind != null)
                        {

                            Response.Redirect(Global.ApplicationPath + "/Clientes/AddEditContrato.aspx?id=" +
                                Util.Helpers.Encrypt(ind.ToString()), false);
                        }
                    }
                }

            } 
            catch(Exception ex) {
                ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('Error al tratar de editar un elemento, consulte al administrador del sistema', 'error');",true);

            }

        }

        protected void btnVerProtocolo_Click(object sender,EventArgs e)
        {
            try
            {
                ASPxButton btn = (ASPxButton) sender;
                if(btn != null)
                {
                    GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer) btn.NamingContainer;
                    if(c != null)
                    {
                        var ind = DataBinder.Eval(c.DataItem,"Id");
                        if(ind != null)
                        {

                            Response.Redirect(Global.ApplicationPath + "/EPAS/Lista/Protocolos.aspx?idSolicitante=" +
                                Util.Helpers.Encrypt(ind.ToString()),false);
                        }
                    }
                }

            } catch(Exception ex)
            {
                ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('Error al tratar de editar un elemento, consulte al administrador del sistema', 'error');",true);

            }
        }

        protected void btnNuevoProtocolo_Click(object sender,EventArgs e)
        {
            try
            {
                ASPxButton btn = (ASPxButton) sender;
                if(btn != null)
                {
                    GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer) btn.NamingContainer;
                    if(c != null)
                    {
                        var ind = DataBinder.Eval(c.DataItem,"Id");
                        if(ind != null)
                        {

                            Response.Redirect(Global.ApplicationPath + "/EPAS/AddEdit/AddEditProtocolos.aspx?idContrato=" +
                                Util.Helpers.Encrypt(ind.ToString()),false);
                        }
                    }
                }

            } catch(Exception ex)
            {
                ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('Error al tratar de editar un elemento, consulte al administrador del sistema', 'error');",true);

            }
        }

        protected void btnEliminar_Click(object sender,EventArgs e)
        {

            try
            {
                ASPxButton btn = (ASPxButton) sender;
                GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer) btn.NamingContainer;
                var ind = DataBinder.Eval(c.DataItem,"Id");

                List<Domain.Contratos> ListaContratos;

                if(Session["ListaContratos"] != null)
                {
                    ListaContratos = (List<Domain.Contratos>) Session["ListaContratos"];

                } else
                {
                    ListaContratos = new List<Domain.Contratos>();
                }

                Contratos con = ListaContratos.Find(q => q.Baja == false);

                if(con != null)
                {
                    ListaContratos.Remove(con);
                    Session["ListaContratos"] = ListaContratos;
                }

                if(Convert.ToInt32(ind) > 0)
                {
                    Domain.Contratos contrato = ContratosServices.GetById(Convert.ToInt32(ind));
                    ListaContratos.Remove(con);
                    contrato.Baja = true;
                    contrato.FechaBaja = DateTime.Now;
                    ContratosServices.SaveOrUpdateContratos(contrato);
                }

                GrVBuscarSolicitante.DataBind();

                ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('El Contacto se borró con éxito!');",true);

            } catch(Exception ex)
            {
                ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('Error al tratar de eliminar un elemento, consulte al administrador del sistema', 'error');",true);

            }
        }
    }
}