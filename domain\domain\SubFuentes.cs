﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 8/11/2023 16:05:45
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.SubFuentes, Domain in the schema.
    /// </summary>
    public partial class SubFuentes {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for SubFuentes constructor in the schema.
        /// </summary>
        public SubFuentes()
        {
            this.Activa = true;
            this.Protocolos = new HashSet<Protocolos>();
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Codigo in the schema.
        /// </summary>
        public virtual string Codigo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Descripcion in the schema.
        /// </summary>
        public virtual string Descripcion
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Activa in the schema.
        /// </summary>
        public virtual System.Nullable<bool> Activa
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Lantitud in the schema.
        /// </summary>
        public virtual string Lantitud
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Longitud in the schema.
        /// </summary>
        public virtual string Longitud
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Calle in the schema.
        /// </summary>
        public virtual string Calle
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Altura in the schema.
        /// </summary>
        public virtual System.Nullable<int> Altura
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Observacion in the schema.
        /// </summary>
        public virtual string Observacion
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Protocolos in the schema.
        /// </summary>
        public virtual ISet<Protocolos> Protocolos
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Fuentes in the schema.
        /// </summary>
        public virtual Fuentes Fuentes
        {
            get;
            set;
        }
    }

}
