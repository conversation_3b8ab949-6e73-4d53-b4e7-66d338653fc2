﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 26/9/2023 16:47:39
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;
using Antlr.Runtime.Misc;

namespace DAO
{
    public partial class DepartamentosRepository : NHibernateRepository<Domain.Departamentos>, IDepartamentosRepository
    {
        public DepartamentosRepository(ISession session) : base(session)
        {
        }

        public virtual IList<Domain.Departamentos> GetAll()
        {
            return session.CreateQuery(string.Format("from Departamentos ORDER BY Descripcion asc"))
                .List<Domain.Departamentos>();
        }
        public virtual IList<Domain.Departamentos> GetById(int id)
        {
            string hql = "from Departamentos v where v.Departamentos.Id = :idDepartamentos";

            IQuery q = session.CreateQuery(hql);
            q.SetParameter("idDepartamentos", id );

            return q.List<Domain.Departamentos>();
        }
        public virtual ICollection<Domain.Departamentos> GetByProvincia(int idProvincia)
        {
            return session.CreateQuery(string.Format("from Departamentos where Provincias.Id = :idProvincia"))
                .SetParameter("idProvincia", idProvincia)
                .List<Domain.Departamentos>();
        }

        public virtual ICollection<Domain.Departamentos> GetAll_Vista(bool Activo)
        {
            string hql = "from Departamentos Where Activo = :Activo Order BY Descripcion asc";

            IQuery q = session.CreateQuery(hql);
            q.SetParameter("Activo", Activo);
            return q.List<Domain.Departamentos>();
        }

        public virtual Domain.Departamentos ExisteCodigo(string stringCodigo)
        {
            string hql = "from Departamentos where  upper(Codigo)=:stringCodigo  and Activo=true";
            return session.CreateQuery(hql)
          
          .SetParameter("stringCodigo", stringCodigo.ToUpper())
          .UniqueResult<Domain.Departamentos>();
        }
        public virtual bool ExisteNombre(string Descripcion)
        {
            string hql = "select count(*) from Departamentos where upper(Descripcion) = :Descripcion and Activo = true";
            long count = session.CreateQuery(hql)
                .SetParameter("Descripcion", Descripcion.ToUpper())
                .UniqueResult<long>();

            return count > 0;
        }




        public virtual Domain.Departamentos GetByKey(int _Id)
        {
            return session.Get<Domain.Departamentos>(_Id);
        }

        public ICollection<Departamentos> GetAll_local(bool Activo)
        {
            throw new NotImplementedException();
        }

        public ICollection<Departamentos> GetActivo(bool Activo)
        {
            throw new NotImplementedException();
        }


        ICollection<Departamentos> IDepartamentosRepository.GetAll()
        {
            throw new NotImplementedException();
        }
    }
}


