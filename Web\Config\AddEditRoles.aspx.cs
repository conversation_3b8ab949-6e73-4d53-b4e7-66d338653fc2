﻿using Business.Services.Usuarios;
using DevExpress.Utils.Extensions;
using DevExpress.Web;
using DevExpress.Web.ASPxTreeList;
using Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web.Config
{
    public partial class AddEditRoles : System.Web.UI.Page
    {

        protected void Page_Init(object sender, EventArgs e)
        {

            // tl_web.DataSource = ModuloService.GetByApp("web");
            tl_web.DataSource = ModuloService.GetAll();
            tl_web.DataBind();
            tl_web.ExpandToLevel(3);
            

           /* tl_app.DataSource = ModuloService.GetByApp("app");
            tl_app.DataBind();
            tl_app.ExpandToLevel(3);*/

                                
        }
                 
        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
              
                //if (!IsPostBack)
               // {
                    Session["ModuloControl"] = null;
                    Session["idRol"] = null;

                    //Literal ltr_nombreBreadcrumb = (Literal)Master.FindControl("ltr_nombreBreadcrumb");
                    if (Request.QueryString["id"] != null)
                    {
                        ltr_nombreBreadcrumb.Text = "Editar área";
                        int idRol = Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["id"].ToString()));
                        CargarRol(idRol);
                        Session.Add("idRol", idRol);
                    }
                    else
                    {
                        ltr_nombreBreadcrumb.Text = "Nueva área";
                        Session.Add("idRol", 0);


                    }


                   // ltr_Titulo.Text = "Roles del sistema";

                   // iconPage.Attributes.Add("class", "icon-key mr2");

               // }



            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al cargar la página, consulte al administrador del sistema', 'error');", true);
            }
        }
        
        private void CargarRol(int idRol)
        {
            try
            {
                Rol r = RolService.getById(idRol);
                txt_codigo.Text = r.Codigo;
                txt_nombre.Text = r.Nombre;
                txt_observacion.Text = r.Observacion;
                cb_superadmin.Checked = r.Superadmin;

               

            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al cargar los datos del usuario, consulte al administrador del sistema', 'error');", true);

            }
        }

        protected override void OnPreRender(EventArgs e)
        {
            base.OnPreRender(e);

        }

        protected void btn_volver_Click(object sender, EventArgs e)
        {
            Response.Redirect(Global.ApplicationPath + "/Config/Roles.aspx", true);
        }

        protected void btn_guardar_Click(object sender, EventArgs e)
        {
            try
            {
                Rol r;
                if (Request.QueryString["id"] != null)
                {
                    r =RolService.getById(Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["id"].ToString())));
                }
                else
                {
                    r = new Rol();
                    r.Eliminable = true;
                    r.Editable = true;        

                }
                r.Codigo = txt_codigo.Text;        
                r.Nombre = txt_nombre.Text;
                r.Observacion = txt_observacion.Text;
                if (cb_superadmin.Checked)
                {
                    r.Superadmin = true;
                    r.Admin = false;
                }
                else
                {
                    r.Superadmin = false;
                    r.Admin = true;
                }
             
                RolService.SaveOrUpdate(r);

                List<RolModulo> lstBorrar = new List<RolModulo>();
                //Elimino los permisos generados anteriormente
                IList<RolModulo> lstMod = RolModuloService.getByRol(r.Id,1);
                 foreach (RolModulo rm in lstMod)
                 {
                    lstBorrar.Add(rm);
                 }
              
                //Guardo los permisos para web
                List<DevExpress.Web.ASPxTreeList.TreeListNode> ListaNodosWeb = tl_web.GetSelectedNodes();

                Modulo a;
                if (ListaNodosWeb.Count != 0)
                {
                    foreach (DevExpress.Web.ASPxTreeList.TreeListNode nodo in ListaNodosWeb)
                    {
                        string p = nodo.Key;                                                                     
                        a = ModuloService.getByNodo(Convert.ToInt32(p));
                        //if (a.Url != "/")
                        //{

                            if (lstMod.ToList<RolModulo>().Exists(pre => pre.Modulo.Id == a.Id))
                            {
                                //un seleccionado existe en la lista de la db
                                lstBorrar.Remove(lstMod.ToList<RolModulo>().Find(pre => pre.Modulo.Id == a.Id));
                            }
                            else
                            {

                            }

                            //GUARDO LOS PERMISOS POR MODULO
                          
                                //Guardo rol modulo
                                RolModulo rm = RolModuloService.getByRolAndModulo(r.Id, a.Id);
                                if (rm == null)
                                {
                                rm = new RolModulo();
                                rm.Modulo = a;
                                rm.Rol = r;
                                RolModuloService.SaveOrUpdate(rm);

                                if (a.NodoPadre != 0)
                                {
                                        //es hijo guardo el padre y el hijo


                                        Modulo padre=ModuloService.getByNodo(a.NodoPadre);
                                    RolModulo rm2 = RolModuloService.getByRolAndModulo(r.Id, padre.Id);
                                        if (rm2 == null)
                                        {
                                            rm = new RolModulo();
                                            rm.Modulo = padre;
                                            rm.Rol = r;
                                            RolModuloService.SaveOrUpdate(rm);
                                        }
                                    }

                                
                                }



                                /*foreach (ModuloControl mc in lst)
                                {
                                    mc.RolModulo = rm;
                                    ModuloControlService.SaveOrUpdate(mc);
                                }*/
                                Session.Remove("ModuloControl_" + a.Id.ToString());
                            



                            //Verifico si hay en session nuevo a guardar
                            IList<ModuloControl> lstMC = ModuloControlService.getByIdModulo(a.Id);
                            List<ModuloControl> lstSes = (List<ModuloControl>)Session["ModuloControl_" + a.Id.ToString()];

                            //Hay datos de sesion, borro todo y vuelvo a cargar
                            if (lstSes != null && lstSes.Count > 0)
                            {
                                //Borro
                                foreach (ModuloControl mc in lstMC)
                                {
                                    ModuloControlService.Delete(mc);
                                }

                             
                            }
                            else
                            {
                                //No debería hacer nada acá
                                if (lstMC != null && lstMC.Count > 0)
                                {

                                }
                            }


                       // }


                    }

                    //Guardo los de la lista
                    foreach(RolModulo rm in lstBorrar)
                    {
                       
                        RolModuloService.Delete(rm);
                    }


                }



                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Guardado correctamente', 'success');", true);




            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al guardar el rol, consulte al administrador del sistema', 'error');", true);

            }
        }

        protected void tl_web_PreRender(object sender, EventArgs e)
        {
            TreeListNodeIterator iterator = tl_web.CreateNodeIterator();
            TreeListNode node;

            if (Request.QueryString["id"] != null)
            {

                int idRol = Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["id"].ToString()));
                if (idRol != 0)
                {
                    Rol u = RolService.getById(idRol);
                 
                    Modulo a;
                    while (true)
                    {
                        node = iterator.GetNext();
                        if (node == null) break;

                        a = (Modulo)node.DataItem;
                        if (a.NodoPadre != 0)
                        {
                            if (u.RolModulos.ToList<RolModulo>().Exists(p => p.Modulo.Id == a.Id))
                            {
                                node.Selected = true;
                            }
                        }

                    }
                }
            }
            else
            {

            }


             
        }

        protected void tl_app_PreRender(object sender, EventArgs e)
        {
            TreeListNodeIterator iterator = tl_app.CreateNodeIterator();
            TreeListNode node;

            if (Request.QueryString["id"] != null)
            {

                int idRol = Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["id"].ToString()));
                if (idRol != 0)
                {
                    Rol u = RolService.getById(idRol);

                    Modulo a;
                    while (true)
                    {
                        node = iterator.GetNext();
                        if (node == null) break;

                        a = (Modulo)node.DataItem;

                        if (u.RolModulos.ToList<RolModulo>().Exists(p => p.Modulo.Id == a.Id))
                        {
                            node.Selected = true;
                        }

                    }
                }
            }
            else
            {

            }



        }

        protected void btnPermisos_Init(object sender, EventArgs e)
        {
            try
            {
                ASPxButton btn = (ASPxButton)sender;
                TreeListDataCellTemplateContainer c = (TreeListDataCellTemplateContainer)btn.NamingContainer;
                var ind = DataBinder.Eval(c.DataItem, "Id");

                //mando el id como parametro en el callback
                btn.ClientSideEvents.Click = String.Format("function(s,e){{ callback_modal_permisos.PerformCallback('{0}');}}", "showModal|" + ind);

                Modulo m = ModuloService.getById(Convert.ToInt32(ind));
                if (m.Url == "/")
                {
                    btn.Visible = false;
                }
                //dataview_images.FindItemControl("Your_ItemControlID",);
            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key4", "swal('Error al eliminar, intente nuevamente.');", true);
            }
        }

        protected void callback_modal_permisos_Callback(object source, CallbackEventArgs e)
        {
            try
            {
                string[] _params = e.Parameter.Split('|');
                if (_params[0] == "showModal")
                {
                    Session["idModulo"] = null;
                    int id = Convert.ToInt32(_params[1]);
                    Session.Add("idModulo", id.ToString());

                    CargarPermisos();

                  
                }
            }
            catch (Exception ex)
            {

            }
        }

        protected void callback_ficha_Callback(object source, CallbackEventArgs e)
        {
          
        }

        protected void callback_save_Callback(object source, CallbackEventArgs e)
        {
            
        }

        

        protected void tl_permisos_PreRender(object sender, EventArgs e)
        {

            CargarPermisos();


        }

        private void CargarPermisos()
        {
            try
            {
                // tl_permisos.ClearNodes();
                TreeListNodeIterator iterator = tl_permisos.CreateNodeIterator();
                TreeListNode node;

                if (Session["idModulo"] != null)
                {
                    int idModulo = Convert.ToInt32(Session["idModulo"]);
                    int idRol = Convert.ToInt32(Session["idRol"]);
                    RolModulo rm;
                    rm = RolModuloService.getByRolAndModulo(idRol, idModulo);
                    IList<ModuloControl> lst;
                    if (rm != null)
                    {
                       
                        if (Session["ModuloControl_" + Session["idModulo"].ToString()] != null)
                        {
                            IList<ModuloControl> lstmc = (IList<ModuloControl>)Session["ModuloControl_" + Session["idModulo"].ToString()];
                            lst = lstmc.Where<ModuloControl>(q => q.RolModulo.Id == idModulo).ToList<ModuloControl>();
                            IList<ModuloControl> lstaux = ModuloControlService.getByIdModulo(idModulo);
                           lst=lstaux;

                        }
                        else
                        {
                            IList<ModuloControl> lstaux = ModuloControlService.getByIdRolModulo(rm.Id);

                            lst = ModuloControlService.getByIdModulo(rm.Id);
                            lst = lstaux;
                        }
                    }
                    else
                    {
                        rm = new RolModulo();
                        rm.Rol = RolService.getById(idRol);
                        rm.Modulo = ModuloService.getById(idModulo);
                        rm.Visible = true;
                        RolModuloService.SaveOrUpdate(rm);

                        lst = ModuloControlService.getByIdModulo(rm.Id);
                    }
                    tl_permisos.UnselectAll();
                    if (lst != null && lst.Count>0)
                    {

                    }
                }
                else
                {

                }
            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "keyErr4", "swal('Error al eliminar, intente nuevamente.');", true);
            }
        }

        protected void tl_permisos_CustomCallback(object sender, TreeListCustomCallbackEventArgs e)
        {
           
        }

        protected void tl_permisos_CustomDataCallback(object sender, TreeListCustomDataCallbackEventArgs e)
        {
            
        }

       

        protected void CallbackPanelPermisos_Callback(object sender, CallbackEventArgsBase e)
        {
            CargarPermisos();
        }
    }
}