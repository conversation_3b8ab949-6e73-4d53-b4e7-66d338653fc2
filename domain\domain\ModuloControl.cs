﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 31/5/2023 15:22:01
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.ModuloControl, Domain in the schema.
    /// </summary>
    public partial class ModuloControl {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for ModuloControl constructor in the schema.
        /// </summary>
        public ModuloControl()
        {
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Visible in the schema.
        /// </summary>
        public virtual System.Nullable<bool> Visible
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Lectura in the schema.
        /// </summary>
        public virtual System.Nullable<bool> Lectura
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Escritura in the schema.
        /// </summary>
        public virtual System.Nullable<bool> Escritura
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Eliminar in the schema.
        /// </summary>
        public virtual System.Nullable<bool> Eliminar
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for RolModulo in the schema.
        /// </summary>
        public virtual RolModulo RolModulo
        {
            get;
            set;
        }
    }

}
