﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 13/11/2023 15:58:23
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.Provincias, Domain in the schema.
    /// </summary>
    [Serializable]    
    public partial class Provincias {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for Provincias constructor in the schema.
        /// </summary>
        public Provincias()
        {
            this.Activo = true;
            this.Departamentos = new HashSet<Departamentos>();
            this.Localidades = new HashSet<Localidades>();
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Descripcion in the schema.
        /// </summary>
        public virtual string Descripcion
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Activo in the schema.
        /// </summary>
        public virtual bool Activo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Departamentos in the schema.
        /// </summary>
        public virtual ISet<Departamentos> Departamentos
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Localidades in the schema.
        /// </summary>
        public virtual ISet<Localidades> Localidades
        {
            get;
            set;
        }
    }

}
