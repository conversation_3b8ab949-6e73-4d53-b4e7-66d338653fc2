<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="Cheques" table="Cheques">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="SerieNro" type="String">
      <column name="SerieNro" not-null="false" length="50" sql-type="nvarchar" />
    </property>
    <property name="ImporteCheque" type="Decimal">
      <column name="ImporteCheque" not-null="false" precision="18" scale="0" sql-type="decimal" />
    </property>
    <property name="FecEmision" type="DateTime">
      <column name="FecEmision" not-null="false" sql-type="datetime" />
    </property>
    <property name="FecDeposito" type="DateTime">
      <column name="FecDeposito" not-null="false" sql-type="datetime" />
    </property>
    <property name="Diferido" type="Boolean">
      <column name="Diferido" not-null="false" sql-type="bit" />
    </property>
    <many-to-one name="Comprobantes" class="Comprobantes">
      <column name="IdComprobante" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <many-to-one name="Monedas" class="Monedas">
      <column name="IdMonedas" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <many-to-one name="BancosTarjetas" class="BancosTarjetas">
      <column name="IdBancosTarjetas" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
  </class>
</hibernate-mapping>