﻿using DAO;
using Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Contratos {
    public class DomiciliosTiposServices {

        public static IList<DomiciliosTipos> GetAll() {
            IList<DomiciliosTipos> listaAux;
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                listaAux = (IList<DomiciliosTipos>) new DomiciliosTiposRepository(sess).GetAll();
                sess.Close();
                sess.Dispose();
            }

            return (listaAux);
        }

        public static DomiciliosTipos GetByCodigo(int Codigo) {
            DomiciliosTipos D;

            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {

                D = (DomiciliosTipos) new DomiciliosTiposRepository(sess).GetByCodigo(Codigo);

                if(D != null) {
                    //NHibernateUtil.Initialize(D.Codigo);
                }

                sess.Close();
                sess.Dispose();
                return D;
            }

        }
    }
}
