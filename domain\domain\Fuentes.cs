﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 20/11/2023 15:01:25
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.Fuentes, Domain in the schema.
    /// </summary>
    public partial class Fuentes {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for Fuentes constructor in the schema.
        /// </summary>
        public Fuentes()
        {
            this.Protocolos = new HashSet<Protocolos>();
            this.SubFuentes = new HashSet<SubFuentes>();
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for NroDeFuente in the schema.
        /// </summary>
        public virtual string NroDeFuente
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Descripcion in the schema.
        /// </summary>
        public virtual string Descripcion
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Notas in the schema.
        /// </summary>
        public virtual string Notas
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Latitud in the schema.
        /// </summary>
        public virtual string Latitud
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Longitud in the schema.
        /// </summary>
        public virtual string Longitud
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Activo in the schema.
        /// </summary>
        public virtual System.Nullable<bool> Activo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for FuentesTipos in the schema.
        /// </summary>
        public virtual FuentesTipos FuentesTipos
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Localidades in the schema.
        /// </summary>
        public virtual Localidades Localidades
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Protocolos in the schema.
        /// </summary>
        public virtual ISet<Protocolos> Protocolos
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for SubFuentes in the schema.
        /// </summary>
        public virtual ISet<SubFuentes> SubFuentes
        {
            get;
            set;
        }
    }

}
