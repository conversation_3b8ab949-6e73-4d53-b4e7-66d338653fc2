﻿using Business.Provincias;
using Business.Services.Epas;
using Business.Services.Provincias;
using Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web.EPAS.AddEdit {
    public partial class AddEditZonas : System.Web.UI.Page
    {
        protected void Page_Load(object sender,EventArgs e)
        {
            try
            {
                Txtcodigo.ClientEnabled = true;
                TxtID.Visible = false;
                btnVolverEditado.Visible = false;

                if (!IsPostBack)
                {
                    if (Request.QueryString["id"] != null)
                    {

                        int idzonas = Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["id"].ToString()));
                        TxtID.Text = idzonas.ToString();
                        btnVolverEditado.Visible = false;
                        CargarZonas(idzonas);
                    }
                    else
                    {
                        TxtID.Text = "0";
                        CB_Activo.Checked = true;
                        btnVolverEditado.Visible = false;
                    }
                }
            }

            catch
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1",
                    "showMessage('Error al cargar la página, consulte al administrador del sistema', 'error');", true);
            }

        }


        private void CargarZonas(int idzonas)
        {

            try
            {
                Domain.Zonas zonas = ZonasService.GetById(idzonas);

                if (zonas != null)
                {
                    Txtcodigo.Text = zonas.Codigo.ToString();
                    Txtdescripcion.Text = zonas.Descripcion.ToString();
                    CB_Activo.Checked = Convert.ToBoolean(zonas.Activo);
                }

            }
            catch (Exception ex)
            {
            }

        }

        protected void btnGrabar_Click(object sender,EventArgs e) {
            try
            {
                Zonas Z;

                //Verifica que se haya ingresado un codigo y nombre
                if (!string.IsNullOrEmpty(Txtcodigo.Text) && !string.IsNullOrEmpty(Txtdescripcion.Text))
                {
                    if (Request.QueryString["id"] != null)
                    {
                        // Se está editando un departamento existente.

                        int ZonasId = Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["id"]));
                        //Obtengo el objeto departamento                        
                        Z = ZonasService.GetById(ZonasId);
                        if (Z != null)
                        {
                            //El código si es edición, no se puede modificar, sólo actualizo descripción y activo y provincia
                            if (Z.Codigo.ToUpper().Equals(Txtcodigo.Text.Trim().ToUpper()))
                            {
                                Z.Descripcion = Txtdescripcion.Text.Trim().ToUpper();

                                Z.Activo = CB_Activo.Checked;
                                ZonasService.SaveOrUpdate(Z);
                                ScriptManager.RegisterStartupScript(this, this.GetType(), "message3", "showMessage('Fue editado correctamente.', 'success');", true);
                                Txtcodigo.ClientEnabled = false;

                                Response.Redirect(Global.ApplicationPath + "/EPAS/Lista/Zonas.aspx", false);
                                Response.End();
                            }
                        }
                        //  ScriptManager.RegisterStartupScript(this, this.GetType(), "message1", "showMessage('El departamento ya existe en la base de datos.', 'error');", true);                                             
                    }
                    else
                    {
                        // Verificar si ya existe en la lista local, solo se verifica el código
                        if (ZonasService.ExisteCodigo(Txtcodigo.Text.Trim()) || ZonasService.ExisteNombre(Txtdescripcion.Text.Trim()))
                        {
                            ScriptManager.RegisterStartupScript(this, this.GetType(), "message2", "showMessage('Ya existe una zona con ese código o nombre, no se puede guardar.', 'error');", true);
                        }
                        else
                        {
                            //No existe el código, puedo guardar en la base
                            Z = new Zonas();
                            Z.Codigo = Txtcodigo.Text.Trim().ToUpper();
                            Z.Descripcion = Txtdescripcion.Text.Trim().ToUpper();
                            Z.Activo = CB_Activo.Checked;
                            ZonasService.SaveOrUpdate(Z);
                            ScriptManager.RegisterStartupScript(this, this.GetType(), "message3", "showMessage('Agregado correctamente.', 'success');", true);
                            Txtcodigo.ClientEnabled = false;
                            Response.Redirect(Global.ApplicationPath + "/EPAS/Lista/Zonas.aspx", false);
                        }

                    }



                }
                else
                {
                    ScriptManager.RegisterStartupScript(this, this.GetType(), "message4", "showMessage('Busque una zona.', 'error');", true);
                }
            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "message5", "showMessage('Error al agregar una zona, intente de nuevo.', 'error');", true);
            }

        }
    }
}