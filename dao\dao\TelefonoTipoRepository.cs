//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 3/11/2023 09:44:13
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class TelefonoTipoRepository : NHibernateRepository<Domain.TelefonoTipo>, ITelefonoTipoRepository
    {
        public TelefonoTipoRepository(ISession session) : base(session)
        {
        }

        public virtual ICollection<Domain.TelefonoTipo> GetAll()
        {
            return session.CreateQuery(string.Format("from TelefonoTipo")).List<Domain.TelefonoTipo>();
        }

        public virtual Domain.TelefonoTipo GetByKey(int _Id)
        {
            return session.Get<Domain.TelefonoTipo>(_Id);
        }

        public virtual ICollection<Domain.TelefonoTipo> GetByTipoTel(bool Activo)
        {
            string hql = "from TelefonoTipo order BY Tipo asc";

            IQuery q = session.CreateQuery(hql);
            return q.List<Domain.TelefonoTipo>();

        }
    }
}
