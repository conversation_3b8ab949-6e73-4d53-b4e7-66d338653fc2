﻿using Business.Services.Epas;
using DevExpress.Web;
using Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web.EPAS.Lista
{
    public partial class ProtocolosRevision : System.Web.UI.Page
    {
        protected void Page_Load(object sender,EventArgs e)
        {
            if(!IsPostBack)
            {
                Session["ListaProtocolos"] = new List<Domain.Protocolos>();
            }

            CargarProtocolosEnRevision();
        }

        private void CargarProtocolosEnRevision()
        {
            try
            {
                List<Domain.Protocolos> protocolos = (List<Domain.Protocolos>) Business.Services.Epas.ProtocolosService.GetAll();
                List<Domain.Protocolos> protocolosPendientes = new List<Domain.Protocolos>();

                if(protocolos != null && protocolos.Count >= 1)
                {
                    foreach(var prot in protocolos)
                    {
                        if(prot.ProtocolosEstados.Codigo == "ER")
                        {
                            protocolosPendientes.Add(prot);
                        }
                    }
                }

                if(protocolosPendientes.Count >= 1)
                {
                    gv_ProtocolosRevision.DataSource = protocolosPendientes;
                    gv_ProtocolosRevision.DataBind();
                } else
                {
                    gv_ProtocolosRevision.DataSource = null;
                    // GrVTomador.DataBind();
                }
                
            } catch(Exception ex)
            {
                ScriptManager.RegisterStartupScript(this,this.GetType(),"ksdfsd4ey1","showMessage('" + ex.Message + "', 'error');",true);
            }
        }

        protected void btnRevisar_Click(object sender,EventArgs e)
        {
            try
            {
                ASPxButton btn = (ASPxButton) sender;
                if(btn != null)
                {
                    GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer) btn.NamingContainer;
                    if(c != null)
                    {
                        var ind = DataBinder.Eval(c.DataItem,"Id");

                        if(ind != null)
                        {
                            Domain.Protocolos protocolo = ProtocolosService.GetById(Convert.ToInt32(ind));

                            if(protocolo != null)
                            {
                                Response.Redirect(Global.ApplicationPath + "/EPAS/AddEdit/AddEditProtocolos.aspx?idRevision=" + Util.Helpers.Encrypt(ind.ToString()),false);

                            }
                        }
                    }
                }
            } catch(Exception ex)
            {
                ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('Error al tratar de editar un elemento.Consulte al administrador del sistema.', 'error');",true);
            }
        }

        protected string getTomador(object obj)
        {
            string salida = "-";
            if (obj != null)
            {
                Contratos ctr = (Contratos)obj;
                if (ctr != null)
                {
                    salida = ctr.Nombre;
                }
            }

            return salida;
        }
    }
}