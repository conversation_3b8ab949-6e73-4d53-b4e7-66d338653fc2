<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="Sexos" table="Sexos">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Descripcion" type="String">
      <column name="Descripcion" not-null="true" length="20" sql-type="nvarchar" />
    </property>
    <property name="Abreviatura" type="String">
      <column name="Abreviatura" not-null="true" length="50" sql-type="nvarchar" />
    </property>
    <set name="ContratoContactos" inverse="true" cascade="delete" generic="true">
      <key>
        <column name="IdTipoSexo" />
      </key>
      <one-to-many class="ContratoContactos" />
    </set>
  </class>
</hibernate-mapping>