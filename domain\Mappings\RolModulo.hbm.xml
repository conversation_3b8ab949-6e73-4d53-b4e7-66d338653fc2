<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="RolModulo" table="RolModulo">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Visible" type="Boolean">
      <column name="Visible" default="1" not-null="true" sql-type="bit" />
    </property>
    <many-to-one name="Modulo" class="Modulo">
      <column name="IdModulo" not-null="true" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <many-to-one name="Rol" class="Rol">
      <column name="IdRol" not-null="true" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <set name="ModuloControls" inverse="true" generic="true">
      <key>
        <column name="IdRolModulo" />
      </key>
      <one-to-many class="ModuloControl" />
    </set>
  </class>
</hibernate-mapping>