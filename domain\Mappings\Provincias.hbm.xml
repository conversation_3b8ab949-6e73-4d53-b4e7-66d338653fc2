﻿<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="Provincias" table="Provincias">
    <id name="Id" type="Int32">
      <column name="id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Descripcion" type="String">
      <column name="Descripcion" not-null="true" sql-type="varchar" />
    </property>
    <property name="Activo" type="Boolean">
      <column name="Activo" default="1" not-null="true" sql-type="bit" />
    </property>
    <set name="Departamentos" inverse="true" generic="true">
      <key>
        <column name="IdProvincia" />
      </key>
      <one-to-many class="Departamentos" />
    </set>
    <set name="Localidades" inverse="true" generic="true">
      <key>
        <column name="IdProvincia" />
      </key>
      <one-to-many class="Localidades" />
    </set>
  </class>
</hibernate-mapping>