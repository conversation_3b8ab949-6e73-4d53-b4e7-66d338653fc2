using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;
using DAO;
using Domain;
using System.Data;
using Common.Permisos;
using Domain.DTO;

namespace Business.Services.Usuarios
{

    public class ModuloService
    {

        public static IList<ModuloDTO> getByRol_DTO(int idRol, int Padre, int app)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                List<ModuloDTO> aux = new List<ModuloDTO>();
            
               
                    aux.AddRange(new CuponesTarjetasrepository(sess).getByRol_DTO(idRol, Padre,app));
               
                sess.Close();
                sess.Dispose();
                return aux;
            }
        }
        public static IList<ModuloDTO> getByRol_DTO(List<Rol> lstRol, int Padre, int app)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                List<ModuloDTO> aux = new List<ModuloDTO>();
                foreach (Rol r in lstRol)
                {
                    aux.AddRange(new CuponesTarjetasrepository(sess).getByRol_DTO(r.Id, Padre,app));
                }
                sess.Close();
                sess.Dispose();
                return aux;
            }
        }

        public static Modulo getByNodo(int nodo)
        {
            Modulo acc;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                acc = new CuponesTarjetasrepository(sess).GetByNodo(nodo);
                sess.Close();
                sess.Dispose();
                return acc;
            }

        }

     

        public static IList<Modulo> obtener_Accesos_By_NodoPadre(int nodo_Padre)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                IList<Modulo> aux = new CuponesTarjetasrepository(sess).obtener_Accesos_By_NodoPadre(nodo_Padre);
                sess.Close();
                sess.Dispose();
                return aux;
            }
        }

        protected static DataTable crear_DataTable_Accesos()
        {
            DataTable dt_Accesos = new DataTable();
            DataColumn column;

            column = new DataColumn();
            column.DataType = System.Type.GetType("System.String");
            column.ColumnName = "Descripcion";
            column.Caption = "Descripcion";
            column.ReadOnly = false;
            dt_Accesos.Columns.Add(column);

            column = new DataColumn();
            column.DataType = System.Type.GetType("System.Boolean");
            column.ColumnName = "PermitirAcceso";
            column.Caption = "PermitirAcceso";
            column.ReadOnly = false;
            dt_Accesos.Columns.Add(column);

            column = new DataColumn();
            column.DataType = System.Type.GetType("System.Boolean");
            column.ColumnName = "PermitirEliminar";
            column.Caption = "PermitirEliminar";
            column.ReadOnly = false;
            dt_Accesos.Columns.Add(column);

            column = new DataColumn();
            column.DataType = System.Type.GetType("System.Boolean");
            column.ColumnName = "PermitirEscritura";
            column.Caption = "PermitirEscritura";
            column.ReadOnly = false;
            dt_Accesos.Columns.Add(column);

            column = new DataColumn();
            column.DataType = System.Type.GetType("System.Boolean");
            column.ColumnName = "Mostrar_Acceso";
            column.Caption = "Mostrar_Acceso";
            column.ReadOnly = false;
            dt_Accesos.Columns.Add(column);

            column = new DataColumn();
            column.DataType = System.Type.GetType("System.Boolean");
            column.ColumnName = "Mostrar_Eliminar";
            column.Caption = "Mostrar_Eliminar";
            column.ReadOnly = false;
            dt_Accesos.Columns.Add(column);

            column = new DataColumn();
            column.DataType = System.Type.GetType("System.Boolean");
            column.ColumnName = "Mostrar_Escritura";
            column.Caption = "Mostrar_Escritura";
            column.ReadOnly = false;
            dt_Accesos.Columns.Add(column);

            column = new DataColumn();
            column.DataType = System.Type.GetType("System.Int32");
            column.ColumnName = "Nro_level";
            column.Caption = "Nro_level";
            column.ReadOnly = false;
            dt_Accesos.Columns.Add(column);

            column = new DataColumn();
            column.DataType = System.Type.GetType("System.Int32");
            column.ColumnName = "idAcceso";
            column.Caption = "idAcceso";
            column.ReadOnly = false;
            dt_Accesos.Columns.Add(column);

            column = new DataColumn();
            column.DataType = System.Type.GetType("System.Int32");
            column.ColumnName = "Nodo";
            column.Caption = "Nodo";
            column.ReadOnly = false;
            dt_Accesos.Columns.Add(column);

            column = new DataColumn();
            column.DataType = System.Type.GetType("System.Int32");
            column.ColumnName = "NodoPadre";
            column.Caption = "NodoPadre";
            column.ReadOnly = false;
            dt_Accesos.Columns.Add(column);

            column = new DataColumn();
            column.DataType = System.Type.GetType("System.String");
            column.ColumnName = "Url";
            column.Caption = "Url";
            column.ReadOnly = false;
            dt_Accesos.Columns.Add(column);

            return dt_Accesos;
        }






        public static IList<Modulo> GetByApp(string codigo)
        {
            IList<Modulo> lista;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                lista = new CuponesTarjetasrepository(sess).GetByApp(codigo);
                sess.Close();
                sess.Dispose();
                return lista;
            }

        }




        public static IList<Modulo> GetAll()
        {
            IList<Modulo> lista;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                CuponesTarjetasrepository moduloRepository = new CuponesTarjetasrepository(sess);
                lista = (IList<Modulo>)moduloRepository.GetAll();
                sess.Close();
                sess.Dispose();
                return lista;
            }

        }

        public static Modulo getById(int id)
        {
            Modulo a;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {

                a = new CuponesTarjetasrepository(sess).GetByKey(id);
                sess.Close();
                sess.Dispose();

                return a;



            }

        }

        public static void SaveOrUpdate(Modulo obj)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new CuponesTarjetasrepository(sess).Add(obj);

                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }
            }
        }

        public static void Delete(Modulo obj)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new CuponesTarjetasrepository(sess).Remove(obj);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }
            }
        }

     

      

      

        public static Modulo getByURL(string url)
        {
            Modulo acc;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                acc = new CuponesTarjetasrepository(sess).GetByURL(url);
                sess.Close();
                sess.Dispose();
                return acc;
            }

        }

        //public static List<TipoPermiso> getPermisoIndividualPagina(string pagina, List<Rol> roles)
        //{
        //    List<TipoPermiso> salida = new List<TipoPermiso>();

        //    //Obtengo el modulo
        //    if (pagina.Equals("/Config/AddEditRol.aspx") || pagina.Equals("/Config/AddEditUsuario.aspx"))
        //    {
        //        if (roles.Exists(pre => pre.Superadmin))
        //        {
        //            salida.Add(TipoPermisoService.getByCodigo("G"));
        //        }
        //    }
        //    else
        //    {
        //        Modulo ac = ModuloService.getByURL(pagina.Trim());
        //        if (ac != null)
        //        {
        //            foreach (Rol r in roles)
        //            {
        //                //Obtengo los modulos por rol
        //                IList<RolModulo> lista = RolModuloService.getByRol(r.Id);

        //                foreach (RolModulo rm in lista)
        //                {
        //                    if (rm.Modulo.Id == ac.Id)
        //                    {

        //                        salida.Add(TipoPermisoService.getByCodigo("G"));
        //                    }
        //                    /* IList<ModuloControl> lstRM = ModuloControlService.getByIdRolModulo(rm.Id);

        //                     foreach(ModuloControl mc in lstRM)
        //                     {
        //                         salida.Add(mc.TipoPermiso);
        //                     }*/
        //                }
        //            }
        //        }
        //    }

        //    return salida;
        }


    //public static PermisoModulo GetPermisosPagina(string pagina, int idRol)
    //    {
    //        PermisoModulo pp = new PermisoModulo();
    //        pp.Visible = false;
           

    //        Modulo ac = ModuloService.getByURL(pagina.Trim());


    //        IList<RolModulo> lista = RolModuloService.getByRol(idRol);

    //        foreach (RolModulo ra in lista)
    //        {
    //            if (ra.Modulo.Id == ac.Id)
    //            {
                 

    //                if (ra.Visible)
    //                {
    //                    pp.Visible = true;
    //                }
                 
    //            }

    //        }


    //        return pp;
    //    }
    

}
