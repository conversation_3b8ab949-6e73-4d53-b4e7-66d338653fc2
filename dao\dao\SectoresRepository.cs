﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 26/9/2023 16:47:39
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class SectoresRepository : NHibernateRepository<Domain.Sectores>, ISectoresRepository
    {
        public SectoresRepository(ISession session) : base(session)
        {
        }

        public virtual ICollection<Domain.Sectores> GetAll()
        {
            return session.CreateQuery(string.Format("from Sectores")).List<Domain.Sectores>();
        }

        public virtual Domain.Sectores GetByKey(int _Id)
        {
            return session.Get<Domain.Sectores>(_Id);
        }
    }
}
