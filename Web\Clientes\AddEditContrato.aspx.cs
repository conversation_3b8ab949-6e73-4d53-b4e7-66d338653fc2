﻿using Business.Provincias;
using Business.Services.Contratos;
using Business.Services.Provincias;
using DevExpress.Web;
using Domain;
using Domain.DTO;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web.Clientes {
    public partial class AddEditContrato : System.Web.UI.Page {

        protected void Page_Init(Object sender,EventArgs e) {


            if(!IsPostBack) {

                Session["ListaContratoContactos"] = null;

            }

            GrVContacto.DataBind();
            LlenadoCmbContratoTipo();
            LlenadoDrop_DocTipo();
            LlenadoDrop_Provincias();
            LlenadoDrop_IVACondiciones();
            LlenadoDrop_DocTipoContacto();
            LlenadoDrop_Telefono();
            LlenadoDrop_TelefonoAlternativo();
            LlenadoDrop_TelefonoContacto();
            LlenadoDrop_Sexos();
            Date();

        }
        protected void Page_Load(object sender,EventArgs e) {

            try {

                if(!IsPostBack) {

                    if(Request.QueryString["id"] != null) {

                        ltr_DatosContrato.Text = "Editar Solicitante";
                        ltrContacto.Text = "Editar/Cargar Contacto/s";
                        ltrDomicilio.Text = "Editar Domicilio";
                        int idCliente = Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["id"].ToString()));
                        CargarUsuario(idCliente);

                    } else {
                        nroSolicitante.Visible = false;
                        ltr_DatosContrato.Text = "Datos de Nuevo Solicitante";
                        ltrContacto.Text = "Datos de Contacto/s";
                        ltrDomicilio.Text = "Datos del Domicilio";
                        chbEsTomador.Checked = false;
                    }
                }

                GrVContacto.DataBind();

            } catch {
                ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('Error al cargar la página, consulte al administrador del sistema', 'error');",true);
            }
        }

        private void Date() {
            DateTime currentDate = DateTime.Now;
            DateTime minDate = currentDate.AddYears(-18);
            dateFechaNacContacto.MaxDate = minDate;
            dateFechaNacContacto.Date = minDate;
            dateFechaNacContacto.DataBind();
        }

        protected void LlenadoCmbContratoTipo() {

            cmbTipoSolicitante.DataSource = ContratoTipoServices.GetByContratoTipo(true);
            cmbTipoSolicitante.TextField = "Descripcion";
            cmbTipoSolicitante.ValueField = "ID";
            cmbTipoSolicitante.DataBind();
            cmbTipoSolicitante.Items.Insert(0,new ListEditItem());

        }

        protected void LlenadoDrop_Provincias() {

            cmbProvincias.DataSource = ProvinciasService.GetAll();
            cmbProvincias.TextField = "Descripcion";
            cmbProvincias.ValueField = "Id";
            cmbProvincias.DataBind();
            cmbProvincias.Items.Insert(0,new ListEditItem());

        }

        protected void Drop_Localidades_Callback(object source,CallbackEventArgsBase e) {

            try {
                if(cmbProvincias.SelectedItem != null) {

                    CompletarComboLocalidades(Convert.ToInt32(cmbProvincias.SelectedItem.Value));
                }
            } catch(Exception ex) {
            }
        }

        protected void CompletarComboLocalidades(int idProvincia) {
            if(idProvincia > 0) {

                cmbLocalidades.DataSource = LocalidadesService.GetByProvincias(idProvincia);
                cmbLocalidades.TextField = "NombreCodigo";
                cmbLocalidades.ValueField = "Id";
                cmbLocalidades.DataBind();
                cmbLocalidades.Items.Insert(0,new ListEditItem());
            }
        }

        protected void Drop_Calles_Callback(object source,CallbackEventArgsBase e) {

            CompletarComboCalles(Convert.ToInt32(e.Parameter));

            if(Request.QueryString["Id"] != null) {

                int idCliente = Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["id"].ToString()));

                Contratos cliente = ContratosServices.GetById(idCliente);

                ContratoDomicilios dom = cliente.ContratoDomicilios.Where(p => p.DomiciliosTipos.Descripcion.Equals("Fiscal")).FirstOrDefault<ContratoDomicilios>();

                if(dom != null && dom.Calles != null && dom.Calles.Localidades != null) {

                    cmbCalles.SelectedItem = cmbCalles.Items.FindByValue(dom.Calles.Id.ToString());
                }
            }
        }

        protected void CompletarComboCalles(int idLocalidades) {
            if(idLocalidades > 0) {
                cmbCalles.DataSource = CallesService.GetByLocalidades(idLocalidades);
                cmbCalles.TextField = "Nombre";
                cmbCalles.ValueField = "Id";
                cmbCalles.DataBind();
                cmbCalles.Items.Insert(0,new ListEditItem());
            }
        }

        private void LlenadoDrop_IVACondiciones() {
            cmbIVACondiciones.DataSource = IVACondicionesServices.GetByCondicion(true);
            cmbIVACondiciones.TextField = "Descripcion";
            cmbIVACondiciones.ValueField = "ID";
            cmbIVACondiciones.DataBind();
            cmbIVACondiciones.Items.Insert(0,new ListEditItem());
        }

        protected void LlenadoDrop_DocTipo() {
            cmbTipoDoc.DataSource = DocTiposServices.GetByTipoDoc(true);
            cmbTipoDoc.TextField = "Descripcion";
            cmbTipoDoc.ValueField = "ID";
            cmbTipoDoc.DataBind();
            cmbTipoDoc.Items.Insert(0,new ListEditItem());
        }

        protected void LlenadoDrop_DocTipoContacto() {
            cmbTipoDocContacto.DataSource = DocTiposServices.GetByTipoDoc(true);
            cmbTipoDocContacto.TextField = "Descripcion";
            cmbTipoDocContacto.ValueField = "ID";
            cmbTipoDocContacto.DataBind();
            cmbTipoDocContacto.Items.Insert(0,new ListEditItem());
        }

        protected void LlenadoDrop_Telefono() {
            cmbTelefono.DataSource = TelefonoTipoServices.GetByTipoTel(true);
            cmbTelefono.TextField = "Descripcion";
            cmbTelefono.ValueField = "ID";
            cmbTelefono.DataBind();
            cmbTelefono.Items.Insert(0,new ListEditItem());
        }

        protected void LlenadoDrop_TelefonoAlternativo() {
            cmbTelefonoAlternativo.DataSource = TelefonoTipoServices.GetByTipoTel(true);
            cmbTelefonoAlternativo.TextField = "Descripcion";
            cmbTelefonoAlternativo.ValueField = "ID";
            cmbTelefonoAlternativo.DataBind();
            cmbTelefonoAlternativo.Items.Insert(0,new ListEditItem());
        }

        private void LlenadoDrop_TelefonoContacto() {
            cmbTelefonoContacto.DataSource = TelefonoTipoServices.GetByTipoTel(true);
            cmbTelefonoContacto.TextField = "Descripcion";
            cmbTelefonoContacto.ValueField = "ID";
            cmbTelefonoContacto.DataBind();
            cmbTelefonoContacto.Items.Insert(0,new ListEditItem());
        }

        private void LlenadoDrop_Sexos() {
            cmbSexoContacto.DataSource = SexosServices.GetBySexos(true);
            cmbSexoContacto.TextField = "Descripcion";
            cmbSexoContacto.ValueField = "ID";
            cmbSexoContacto.DataBind();
            cmbSexoContacto.Items.Insert(0,new ListEditItem());
        }

        protected void btnEliminarContacto_Click(object sender,EventArgs e) {

            try {
                ASPxButton btn = (ASPxButton) sender;
                GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer) btn.NamingContainer;
                var ind = DataBinder.Eval(c.DataItem,"Id");
                List<Domain.DTO.ContratoContactosDTO> ListaContratoContactos;

                if(Session["ListaContratoContactos"] != null) {
                    ListaContratoContactos = (List<Domain.DTO.ContratoContactosDTO>) Session["ListaContratoContactos"];

                } else {
                    ListaContratoContactos = new List<Domain.DTO.ContratoContactosDTO>();
                }

                ContratoContactosDTO con = ListaContratoContactos.Find(q => q.Activo == true);

                if(con != null) {

                    ListaContratoContactos.Remove(con);
                    Session["ListaContratoContactos"] = ListaContratoContactos;
                }

                if(Convert.ToInt32(ind) > 0) {

                    Domain.ContratoContactos contacto = ContratoContactosServices.GetById(Convert.ToInt32(ind));
                    ListaContratoContactos.Remove(con);
                    contacto.Activo = false;
                    ContratoContactosServices.SaveOrUpdate(contacto);
                }

                GrVContacto.DataBind();

                ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('El Contacto se borró con éxito!');",true);

            } catch(Exception ex) {
                ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('Error al tratar de eliminar un elemento, consulte al administrador del sistema', 'error');",true);

            }
        }

        protected void GrVContacto_DataBinding(object sender,EventArgs e) {

            if(Session["ListaContratoContactos"] != null) {

                GrVContacto.DataSource = (List<Domain.DTO.ContratoContactosDTO>) Session["ListaContratoContactos"];
            }
        }

        protected void Callback_Agregar_Callback(object source,CallbackEventArgs e) {

            Domain.DTO.ContratoContactosDTO contacto = new Domain.DTO.ContratoContactosDTO();

            List<Domain.DTO.ContratoContactosDTO> ListaContratoContactos;

            if(Session["ListaContratoContactos"] != null) {
                ListaContratoContactos = (List<Domain.DTO.ContratoContactosDTO>) Session["ListaContratoContactos"];

            } else {
                ListaContratoContactos = new List<Domain.DTO.ContratoContactosDTO>();
            }

            contacto.Id = new Random().Next(0,1000) * -1;
            contacto.Nombre = txtNombreContacto.Text;
            contacto.Apellido = txtApellidoContacto.Text;
            DocTipos dt = DocTiposServices.getById(Convert.ToInt32(cmbTipoDocContacto.SelectedItem.Value));
            contacto.DocumentosTipo = dt.Abreviatura;
            contacto.IdDocumentosTipo = dt.Id;

            string tipoDocContactoSeleccionado = cmbTipoDocContacto.SelectedItem != null ? cmbTipoDocContacto.SelectedItem.Text : String.Empty;
            switch(tipoDocContactoSeleccionado) {
                case "DNI":
                contacto.NumeroDoc = txtDniContacto.Text;
                break;
                case "CUIL":
                contacto.NumeroDoc = txtCuilContacto.Text;
                break;
                case "CUIT":
                contacto.NumeroDoc = txtCuitContacto.Text;
                break;
                case "CI":
                contacto.NumeroDoc = txtCiContacto.Text;
                break;
                case "PAS":
                contacto.NumeroDoc = txtPasaporteContacto.Text;
                break;
                default:
                ScriptManager.RegisterStartupScript(this,this.GetType(),"keyError",$"alert('Error al guardar los datos');",true);
                break;
            }
            Sexos sexo = SexosServices.getById(Convert.ToInt32(cmbSexoContacto.SelectedItem.Value));
            contacto.SexoDescripcion = sexo.Descripcion;
            contacto.IdSexo = sexo.Id;
            contacto.FechaNac = Convert.ToDateTime(dateFechaNacContacto.Text);
            contacto.Email = txtEmailContacto.Text;

            TelefonoTipo tt = TelefonoTipoServices.getById(Convert.ToInt32(cmbTelefonoContacto.SelectedItem.Value));
            contacto.TelefonoTipoDescripcion = tt.Tipo.Trim();
            contacto.IdTelefonoTipo = tt.Id;
            contacto.Tel = txtTelefonoContacto.Text;

            ListaContratoContactos.Add(contacto);
            Session.Add("ListaContratoContactos",ListaContratoContactos);

            GrVContacto.DataSource = ListaContratoContactos;
            GrVContacto.DataBind();
            Date();

        }

        protected void btnGuardarContrato_Click(object sender,EventArgs e) {

            try {

                Domain.Contratos contrato;
                ContratoDomicilios domicilio;

                if(Request.QueryString["id"] != null) {
                    contrato = ContratosServices.GetById(Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["id"].ToString())));
                    var domicilioExistente = contrato.ContratoDomicilios.FirstOrDefault<Domain.ContratoDomicilios>();

                    if(domicilioExistente != null) {

                        domicilio = domicilioExistente;

                    } else {
                        domicilio = new ContratoDomicilios();
                        domicilio.DomiciliosTipos = DomiciliosTiposServices.GetByCodigo(1);
                        contrato.NroContrato = contrato.NroContrato;
                    }

                } else {

                    contrato = new Domain.Contratos();
                    domicilio = new ContratoDomicilios();
                    domicilio.DomiciliosTipos = DomiciliosTiposServices.GetByCodigo(1);
                    contrato.NroContrato = NumeracionServices.getByCodigo("C").Numero;
                    contrato.FechaAlta = DateTime.Now;

                }

                contrato.Baja = false;
                contrato.EsTomador = chbEsTomador.Checked;
                if(cmbTipoSolicitante.SelectedItem != null) {
                    ContratoTipo ct = ContratoTipoServices.getById(Convert.ToInt32(cmbTipoSolicitante.SelectedItem.Value));
                    contrato.ContratoTipo = ct;
                } 
                contrato.Nombre = txtNombre.Text;
                contrato.RazonSocial = txtRazonSocial.Text;

                if(cmbIVACondiciones.SelectedItem != null) {
                    IVACondiciones iva = IVACondicionesServices.getById(Convert.ToInt32(cmbIVACondiciones.SelectedItem.Value));
                    contrato.IVACondiciones = iva;
                };

                if(cmbTipoDoc.SelectedItem != null) {
                    DocTipos documentoTipo = DocTiposServices.getById(Convert.ToInt32(cmbTipoDoc.SelectedItem.Value));
                    contrato.DocTipos = documentoTipo;
                };

                string tipoDocSeleccionado = cmbTipoDoc.SelectedItem != null ? cmbTipoDoc.SelectedItem.Text : String.Empty;
                switch(tipoDocSeleccionado) {
                    case "DNI":
                    contrato.IdentificacionTributaria = txtDni.Text;
                    break;
                    case "CUIL":
                    contrato.IdentificacionTributaria = txtCuil.Text;
                    break;
                    case "CUIT":
                    contrato.IdentificacionTributaria = txtCuit.Text;
                    break;
                    case "CI":
                    contrato.IdentificacionTributaria = txtCi.Text;
                    break;
                    case "PAS":
                    contrato.IdentificacionTributaria = txtPasaporte.Text;
                    break;
                    default:
                    ScriptManager.RegisterStartupScript(this,this.GetType(),"keyError",$"alert('Error al guardar los datos');",true);
                    break;
                }

                contrato.Email = txtEmail.Text;
                contrato.EmailAlter = txtEmailAlternativo.Text;

                if(cmbTelefono.SelectedItem != null) {
                    TelefonoTipo telTipo = TelefonoTipoServices.getById(Convert.ToInt32(cmbTelefono.SelectedItem.Value));
                    contrato.TelefonoTipo_IdTelefonoTipo = telTipo;
                };
                contrato.Telefono = txtTelefono.Text;

                if(cmbTelefonoAlternativo.SelectedItem != null) {
                    TelefonoTipo telTipoAlter = TelefonoTipoServices.getById(Convert.ToInt32(cmbTelefonoAlternativo.SelectedItem.Value));
                    contrato.TelefonoTipo_IdTelefonoTipoAlter = telTipoAlter;
                };
                contrato.TelefonoAlter = txtTelefonoAlternativo.Text;
                nroSolicitante.Visible = false;

                ContratosServices.SaveOrUpdateContratos(contrato);

                if(domicilio != null) {

                    domicilio.Contratos = contrato;

                    if(cmbCalles.SelectedItem != null) {
                        Domain.Calles domi = CallesService.GetById(Convert.ToInt32(cmbCalles.SelectedItem.Value));
                        domicilio.Calles = domi;
                    }

                    domicilio.Altura = txtAltura.Text;
                    domicilio.Casa = txtCasaNro.Text;
                    domicilio.Piso = txtPisoNro.Text;
                    domicilio.Dpto = txtDpto.Text;
                    domicilio.Manzana = txtManzana.Text;
                    domicilio.Lote = txtLote.Text;
                    domicilio.DuplexModulo = txtDxMd.Text;
                    domicilio.Peatonal = txtPeat.Text;
                    domicilio.Tira = txtTira.Text;
                    domicilio.EntreCalles = txtEntreCalles.Text;
                    domicilio.Barrio = txtBarrio.Text;
                    domicilio.Latitud = txtLat.Text;
                    domicilio.Longitud = txtLng.Text;

                    ContratoDomiciliosServices.SaveOrUpdate(domicilio);

                }

                if(Session["ListaContratoContactos"] != null) {

                    List<Domain.DTO.ContratoContactosDTO> contratoContacto = (List<Domain.DTO.ContratoContactosDTO>) Session["ListaContratoContactos"];

                    foreach(Domain.DTO.ContratoContactosDTO contacto in contratoContacto) {

                        Domain.ContratoContactos contactoBis = Domain.DTO.ContratoContactosConverter.ToEntity(contacto);

                        contactoBis.Contratos = contrato;
                        contactoBis.Activo = true;

                        if(contactoBis.Id < 0) {
                            contactoBis.Id = 0;
                        }

                        //TIPO DE DOCUMENTO
                        DocTipos docTipo = DocTiposServices.getById(contacto.IdDocumentosTipo);

                        contactoBis.DocTipos = docTipo;

                        //TIPO SEXO
                        Sexos sexo = SexosServices.getById(contacto.IdSexo);

                        contactoBis.Sexos = sexo;


                        ContratoContactosServices.SaveOrUpdate(contactoBis);

                        //EMAIL
                        ContratoContactosEmail email = ContratoContactosEmailServices.GetByContratoContacto(contactoBis.Id);

                        if(email == null) {
                            email = new ContratoContactosEmail();
                        }
                        email.Email = contacto.Email;
                        email.ContratoContactos = contactoBis;
                        ContratoContactosEmailServices.SaveOrUpdate(email);

                        //TELEFONO
                        ContratoContactosTel tel = ContratoContactosTelServices.GetByContratoContacto(contactoBis.Id);

                        if(tel == null) {
                            tel = new ContratoContactosTel();
                        }
                        tel.NroTelefono = contacto.Tel;
                        tel.TelefonoTipo = TelefonoTipoServices.getById(contacto.IdTelefonoTipo);
                        tel.ContratoContactos = contactoBis;
                        ContratoContactosTelServices.SaveOrUpdate(tel);

                    };

                };

               
                Session["ListaContratoContactos"] = null;
                GrVContacto.DataSource = Session["ListaContratoContactos"] = null;
                GrVContacto.DataBind();
                LimpiarCamposContrato();
                LimpiarCamposContacto();
                LimpiarCamposDomicilio();
                Response.Redirect(Global.ApplicationPath + "/Clientes/Guarda.aspx?id=1");

            } catch(Exception ex) {
                ScriptManager.RegisterStartupScript(this,this.GetType(),"keyError",$"alert('Error al guardar los datos: {ex.Message}');",true);
            }
        }

        private void CargarUsuario(int idCliente) {

            try {
                Contratos contrato = ContratosServices.GetById(idCliente);

                if(contrato != null) {
                    txtNroContrato.Text = contrato.NroContrato.ToString();
                    chbEsTomador.Checked = Convert.ToBoolean(contrato.EsTomador);
                    cmbTipoSolicitante.SelectedItem = cmbTipoSolicitante.Items.FindByValue(contrato.ContratoTipo.Id.ToString());
                    cmbIVACondiciones.SelectedItem = cmbIVACondiciones.Items.FindByValue(contrato.IVACondiciones.Id.ToString());
                    cmbTipoDoc.SelectedItem = cmbTipoDoc.Items.FindByValue(contrato.DocTipos.Id.ToString());
                    txtCi.Text = contrato.IdentificacionTributaria;
                    txtCuil.Text = contrato.IdentificacionTributaria;
                    txtCuit.Text = contrato.IdentificacionTributaria;
                    txtDni.Text = contrato.IdentificacionTributaria;
                    txtPasaporte.Text = contrato.IdentificacionTributaria;
                    txtNombre.Text = contrato.Nombre;
                    txtRazonSocial.Text = contrato.RazonSocial;
                    txtEmail.Text = contrato.Email;
                    txtEmailAlternativo.Text = contrato.EmailAlter;
                    cmbTelefono.SelectedItem = cmbTelefono.Items.FindByValue(contrato.TelefonoTipo_IdTelefonoTipo.Id.ToString());
                    txtTelefono.Text = contrato.Telefono;
                    txtTelefonoAlternativo.Text = contrato.TelefonoAlter;

                    if(contrato.TelefonoTipo_IdTelefonoTipoAlter != null) {
                        cmbTelefonoAlternativo.SelectedItem = cmbTelefonoAlternativo.Items.FindByValue(contrato.TelefonoTipo_IdTelefonoTipoAlter.Id.ToString());
                    }

                    ContratoDomicilios dom = contrato.ContratoDomicilios.Where(p => p.DomiciliosTipos.Descripcion.Equals("Fiscal")).FirstOrDefault<ContratoDomicilios>();

                    if(dom != null) {

                        txtAltura.Text = dom.Altura.ToString();
                        txtCasaNro.Text = dom.Casa;
                        txtPisoNro.Text = dom.Piso;
                        txtDpto.Text = dom.Dpto;
                        txtManzana.Text = dom.Manzana;
                        txtLote.Text = dom.Lote;
                        txtDxMd.Text = dom.DuplexModulo;
                        txtPeat.Text = dom.Peatonal;
                        txtTira.Text = dom.Tira;
                        txtEntreCalles.Text = dom.EntreCalles;
                        txtBarrio.Text = dom.Barrio;
                        txtLat.Text = dom.Latitud;
                        txtLng.Text = dom.Longitud;
                        hfLatitud.Value = dom.Latitud;
                        hfLongitud.Value = dom.Longitud;

                        cmbProvincias.SelectedItem = cmbProvincias.Items.FindByValue(dom.Calles.Localidades.Provincias.Id.ToString());

                        CompletarComboLocalidades(Convert.ToInt32(dom.Calles.Localidades.Provincias.Id.ToString()));

                        cmbLocalidades.SelectedItem = cmbLocalidades.Items.FindByValue(dom.Calles.Localidades.Id.ToString());

                        CompletarComboCalles(Convert.ToInt32(dom.Calles.Localidades.Id.ToString()));

                        cmbCalles.SelectedItem = cmbCalles.Items.FindByValue(dom.Calles.Id.ToString());
                    }



                    List<Domain.DTO.ContratoContactosDTO> dtocc = new List<Domain.DTO.ContratoContactosDTO>();

                    if(contrato.ContratoContactos != null) {

                        foreach(ContratoContactos cc in Business.Services.Contratos.ContratoContactosServices.GetAllByIdContrato(contrato.Id)) {

                            dtocc.Add(Domain.DTO.ContratoContactosConverter.ToDto(cc));
                        }

                        if(Session["ListaContratoContactos"] == null) {
                            Session["ListaContratoContactos"] = dtocc;

                        }
                        GrVContacto.DataBind();
                    }
                } else {
                }


            } catch(Exception ex) {
            }

        }

        private void LimpiarCamposContrato() {

            txtNroContrato.Text = "";
            cmbTipoSolicitante.Text = "";
            cmbIVACondiciones.Text = "";
            cmbTipoDoc.Text = "";
            txtCi.Text = "";
            txtCuil.Text = "";
            txtCuit.Text = "";
            txtDni.Text = "";
            txtPasaporte.Text = "";
            txtRazonSocial.Text = "";
            txtNombre.Text = "";
            txtEmail.Text = "";
            txtEmailAlternativo.Text = "";
            cmbTelefono.Text = "";
            txtTelefono.Text = "";
            cmbTelefonoAlternativo.Text = "";
            txtTelefonoAlternativo.Text = "";
            chbEsTomador.Checked = false;
        }

        private void LimpiarCamposDomicilio() {

            cmbProvincias.Text = "";
            cmbLocalidades.Text = "";
            cmbCalles.Text = "";
            txtBarrio.Text = "";
            txtEntreCalles.Text = "";
            txtAltura.Text = "";
            txtCasaNro.Text = "";
            txtPisoNro.Text = "";
            txtDpto.Text = "";
            txtManzana.Text = "";
            txtLote.Text = "";
            txtDxMd.Text = "";
            txtPeat.Text = "";
            txtTira.Text = "";
            txtBarrio.Text = "";
            txtLat.Text = "";
            txtLng.Text = "";
        }

        private void LimpiarCamposContacto() {
            txtNombreContacto.Text = "";
            txtApellidoContacto.Text = "";
            cmbTipoDocContacto.Text = "";
            txtCiContacto.Text = "";
            txtCuilContacto.Text = "";
            txtCuitContacto.Text = "";
            txtDniContacto.Text = "";
            txtPasaporteContacto.Text = "";
            cmbSexoContacto.Text = "";
            txtEmailContacto.Text = "";
            cmbTelefonoContacto.Text = "";
            txtTelefonoContacto.Text = "";
            Date();
        }

        protected void btnCancelarContrato_Click(object sender,EventArgs e) {

        }

        protected void callback_editar_Callback(object source,CallbackEventArgs e) {



        }

        protected void GrVContacto_RowValidating(object sender,DevExpress.Web.Data.ASPxDataValidationEventArgs e) {



            string nombre = e.NewValues["Nombre"].ToString();



            //ASPxGridView grid = (ASPxGridView) sender;
            //object id = e.Keys[GrVContacto.KeyFieldName];

            //GridViewDataComboBoxColumn column1 = grid.Columns["Nombre"] as GridViewDataComboBoxColumn;
            //string nombre = e.NewValues["Nombre"] != null ? e.NewValues["Nombre"].ToString() : string.Empty;

        }

        protected void GrVContacto_CellEditorInitialize(object sender,ASPxGridViewEditorEventArgs e) {

            ASPxGridView gridView = sender as ASPxGridView;

        }
    }
}