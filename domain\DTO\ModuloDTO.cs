﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.DTO {
    public class ModuloDTO {
        /// <summary>
        /// There are no comments for IdAcceso in the schema.
        /// </summary>
        public int Id {
            get;
            set;
        }


        /// <summary>
        /// There are no comments for <PERSON><PERSON>Pad<PERSON> in the schema.
        /// </summary>
        public int NodoPadre {
            get;
            set;
        }


        /// <summary>
        /// There are no comments for <PERSON><PERSON> in the schema.
        /// </summary>
        public int Nodo {
            get;
            set;
        }

        /// <summary>
        /// There are no comments for <PERSON><PERSON>rip<PERSON> in the schema.
        /// </summary>
        public string Icon {
            get;
            set;
        }
        /// <summary>
        /// There are no comments for <PERSON><PERSON><PERSON><PERSON> in the schema.
        /// </summary>
        public string Descripcion {
            get;
            set;
        }


        /// <summary>
        /// There are no comments for Url in the schema.
        /// </summary>
        public string Url {
            get;
            set;
        }
    }
}
