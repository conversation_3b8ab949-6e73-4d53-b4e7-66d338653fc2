﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web.Clientes
{
    public partial class Guarda : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (IsPostBack)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('El Contrato se guardó con éxito!');", true);
            }

            Response.Redirect(Global.ApplicationPath + "/clientes/AddEditContrato.aspx");
        }

        protected void btnVolver_Click(object sender, EventArgs e)
        {
            Response.Redirect(Global.ApplicationPath + "/clientes/AddEditContrato.aspx");
        }

        protected void btnLista_Click(object sender, EventArgs e)
        {
            Response.Redirect(Global.ApplicationPath + "/clientes/BuscarContrato.aspx");
        }
    }
}