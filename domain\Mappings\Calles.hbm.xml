<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="Calles" table="Calles">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Nombre" type="String">
      <column name="Nombre" not-null="false" length="50" sql-type="nvarchar" />
    </property>
    <property name="Activa" type="Boolean">
      <column name="Activa" default="0" not-null="true" sql-type="bit" />
    </property>
    <many-to-one name="Localidades" class="Localidades">
      <column name="IdLocalidad" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <set name="ContratoDomicilios" inverse="true" generic="true">
      <key>
        <column name="IdCalle" />
      </key>
      <one-to-many class="ContratoDomicilios" />
    </set>
  </class>
</hibernate-mapping>