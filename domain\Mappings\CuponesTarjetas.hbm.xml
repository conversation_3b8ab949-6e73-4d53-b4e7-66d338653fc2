<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="CuponesTarjetas" table="Cupones_tarjetas">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="CodCupon" type="Int32">
      <column name="Cod_cupon" not-null="false" precision="10" scale="0" sql-type="int" />
    </property>
    <property name="NroLote" type="Int32">
      <column name="NroLote" not-null="false" precision="10" scale="0" sql-type="int" />
    </property>
    <property name="CantCuotas" type="Decimal">
      <column name="Cant_Cuotas" not-null="false" precision="18" scale="0" sql-type="decimal" />
    </property>
    <property name="CodAutorizacion" type="String">
      <column name="Cod_Autorizacion" not-null="false" length="50" sql-type="nvarchar" />
    </property>
    <property name="Importe" type="Decimal">
      <column name="Importe" not-null="false" precision="19" scale="4" sql-type="money" />
    </property>
    <many-to-one name="Monedas" class="Monedas">
      <column name="IdMoneda" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <many-to-one name="Comprobantes" class="Comprobantes">
      <column name="IdComprobante" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <many-to-one name="BancosTarjetas" class="BancosTarjetas">
      <column name="IdBancoTarjeta" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
  </class>
</hibernate-mapping>