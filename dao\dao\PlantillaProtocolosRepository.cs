//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 28/11/2023 16:48:49
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class PlantillaProtocolosRepository : NHibernateRepository<Domain.PlantillaProtocolos>, IPlantillaProtocolosRepository
    {
        public PlantillaProtocolosRepository(ISession session) : base(session)
        {
        }

        public virtual ICollection<Domain.PlantillaProtocolos> GetAll()
        {
            return session.CreateQuery(string.Format("from PlantillaProtocolos")).List<Domain.PlantillaProtocolos>();

        }

        public virtual Domain.PlantillaProtocolos ExisteCodigo(int numero)
        {
            string hql = "from PlantillaProtocolos where upper(Numero)=:numero and Activo=true";
            return session.CreateQuery(hql)

          .SetParameter("numero", numero)
          .UniqueResult<Domain.PlantillaProtocolos>();
        }
        public virtual bool ExisteNombre(string Descripcion)
        {
            string hql = "select count(*) from PlantillaProtocolos where upper(Descripcion) = :Descripcion and Activo = true";
            long count = session.CreateQuery(hql)
                .SetParameter("Descripcion", Descripcion.ToUpper())
                .UniqueResult<long>();

            return count > 0;
        }

        public virtual ICollection<Domain.PlantillaProtocolos> GetByPlantillaActiva(bool Activo)
        {
            string hql = "from PlantillaProtocolos where Activo = :Activo Order BY Numero asc";

            IQuery q = session.CreateQuery(hql);
            q.SetParameter("Activo",Activo);
            return q.List<Domain.PlantillaProtocolos>();
        }

        public virtual Domain.PlantillaProtocolos GetByKey(int _Id)
        {
            return session.Get<Domain.PlantillaProtocolos>(_Id);
        }
    }
}
