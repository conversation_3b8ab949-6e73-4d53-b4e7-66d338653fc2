﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 1/12/2023 16:42:48
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class ProtocolosEstadosRepository : NHibernateRepository<Domain.ProtocolosEstados>, IProtocolosEstadosRepository
    {
        public ProtocolosEstadosRepository(ISession session) : base(session)
        {
        }

        public virtual IList<Domain.ProtocolosEstados> GetAll()
        {
            return session.CreateQuery(string.Format("from ProtocolosEstados where Activo='true'")).List<Domain.ProtocolosEstados>();
        }

        public virtual Domain.ProtocolosEstados GetByCodigo(string codigo)
        {
            return session.CreateQuery(string.Format("from ProtocolosEstados where Activo='true' and Codigo=:codigo"))
                .SetParameter("codigo",codigo)
                .UniqueResult<Domain.ProtocolosEstados>();
        }

        public virtual Domain.ProtocolosEstados GetByKey(int _Id)
        {
            return session.Get<Domain.ProtocolosEstados>(_Id);
        }
    }
}
