﻿using Business.Services.Usuarios;
using DevExpress.Web;
using Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web.Config
{
    public partial class AddEditUsuario : System.Web.UI.Page
    {

        protected void Page_Init(Object sender, EventArgs e)
        {
            CargarCombos();
            CargarComboRoles();
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
               
                if (!IsPostBack)
                {
                  

                   // Literal ltr_nombreBreadcrumb = (Literal)Master.FindControl("ltr_nombreBreadcrumb");
                    if (Request.QueryString["id"] != null)
                    {
                        ltr_nombreBreadcrumb.Text = "Editar usuario";
                        int idUsuario = Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["id"].ToString()));
                        CargarUsuario(idUsuario);
                    }
                    else
                    {
                        ltr_nombreBreadcrumb.Text = "Nuevo usuario";
                        ch_activo.Checked = true;
                    }
                    

                   // ltr_Titulo.Text = "Usuarios del sistema";

                   // iconPage.Attributes.Add("class", "icon-user mr2");

                }

             

            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al cargar la página, consulte al administrador del sistema', 'error');", true);
            }
        }

        private void CargarCombos()
        {
            try
            {


            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al cargar los filtros, consulte al administrador del sistema', 'error');", true);

            }
        }

       


        private void CargarUsuario(int idUsuario)
        {
            try
            {
                Usuario user = UsuarioService.GetById(idUsuario);
                txt_apellido.Text = user.Apellido;
                txt_nombre.Text = user.Nombre;
                txt_direccion.Text = user.Direccion;
                txt_username.Text = user.Username;
                txt_password.Text = user.Password;
                txt_email.Text = user.Email;
                ch_activo.Checked = Convert.ToBoolean(user.Activo);
                List<Rol> st = user.Rols.ToList<Rol>();
                foreach (ListEditItem item in lb_roles.Items)
                {
                    if (st.Exists(p => p.Id.ToString().Equals(item.Value)))
                    {
                        item.Selected = true;
                    }
                }


                }catch(Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al cargar los datos del usuario, consulte al administrador del sistema', 'error');", true);

            }
        }

        protected override void OnPreRender(EventArgs e)
        {
            base.OnPreRender(e);

        }

            private void CargarComboRoles()
        {
            try
            {
                lb_roles.DataSource = RolService.getAllDDL();
                lb_roles.DataBind();
               
            }catch(Exception ex)
            {

            }
        }

        protected void btn_volver_Click(object sender, EventArgs e)
        {
            Response.Redirect(Global.ApplicationPath + "/Config/Usuarios.aspx", true);
        }

        protected void btn_guardar_Click(object sender, EventArgs e)
        {
            try
            {
                Usuario user;
                if (Request.QueryString["id"] != null)
                {
                    user = UsuarioService.GetById(Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["id"].ToString())));
                }
                else
                {
                    user = new Usuario();
                    user.IngresoActual = null;
                    user.PrimerIngreso = null;
                    user.UltimoIngreso = null;                   

                }
              
                user.Activo = ch_activo.Checked;
                user.Eliminado = false;
                user.Apellido = txt_apellido.Text;
                user.Cuit = "";
                user.Direccion = txt_direccion.Text;
                user.Email = txt_email.Text;
                user.Nombre = txt_nombre.Text;
                user.Password = txt_password.Text;
                user.Username = txt_username.Text.ToString().ToLower();
                user.Telefono = txt_telefono.Text;

                user.Rols.Clear();
                foreach (ListEditItem item in lb_roles.Items)
                {
                    if (item.Selected)
                    {
                        Rol r = RolService.getById(Convert.ToInt32(item.Value));
                        if (r != null)
                        {
                            user.Rols.Add(r);
                        }
                    }
                    
                }


                UsuarioService.SaveOrUpdate(user);

                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Guardado correctamente', 'success');", true);




            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al guardar el usuario, consulte al administrador del sistema', 'error');", true);

            }
        }

      

    }
}