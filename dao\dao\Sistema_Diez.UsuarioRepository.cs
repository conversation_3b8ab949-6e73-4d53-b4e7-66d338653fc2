﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 12/5/2023 11:08:46
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class UsuarioRepository : NHibernateRepository<Domain.Usuario>, IUsuarioRepository
    {
        public UsuarioRepository(ISession session) : base(session)
        {
        }

        public virtual ICollection<Domain.Usuario> GetAll()
        {
            return session.CreateQuery(string.Format("from Usuario")).List<Domain.Usuario>();
        }

        public virtual Domain.Usuario GetByKey(int _Id)
        {
            return session.Get<Domain.Usuario>(_Id);
        }
    }
}
