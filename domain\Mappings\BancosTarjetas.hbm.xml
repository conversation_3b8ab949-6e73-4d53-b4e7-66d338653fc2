<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="BancosTarjetas" table="BancosTarjetas">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Nombre" type="String">
      <column name="Nombre" not-null="false" length="50" sql-type="nvarchar" />
    </property>
    <property name="Cuit" type="String">
      <column name="Cuit" not-null="false" length="13" sql-type="varchar" />
    </property>
    <property name="QueEs" type="String">
      <column name="QueEs" not-null="false" length="1" sql-type="nvarchar" />
    </property>
    <property name="Mascara" type="String">
      <column name="Mascara" not-null="false" length="255" sql-type="nvarchar" />
    </property>
    <property name="MascaraDeFormato" type="String">
      <column name="MascaraDeFormato" not-null="false" length="255" sql-type="nvarchar" />
    </property>
    <property name="Abreviatura" type="String">
      <column name="Abreviatura" not-null="false" length="50" sql-type="nvarchar" />
    </property>
    <property name="CodExportacion" type="Int32">
      <column name="CodExportacion" not-null="false" precision="10" scale="0" sql-type="int" />
    </property>
    <property name="Activo" type="Boolean">
      <column name="Activo" not-null="false" sql-type="bit" />
    </property>
    <property name="ModalidadTarjeta" type="String">
      <column name="ModalidadTarjeta" not-null="false" length="50" sql-type="nvarchar" />
    </property>
    <property name="EsDebitoA" type="Boolean">
      <column name="EsDebitoA" not-null="false" sql-type="bit" />
    </property>
    <property name="IdComprobante" type="Int32">
      <column name="IdComprobante" not-null="false" precision="10" scale="0" sql-type="int" />
    </property>
    <set name="BancoCuentas" inverse="true" generic="true">
      <key>
        <column name="IdBancoTarjeta" />
      </key>
      <one-to-many class="BancoCuentas" />
    </set>
    <set name="Cheques" inverse="true" generic="true">
      <key>
        <column name="IdBancosTarjetas" />
      </key>
      <one-to-many class="Cheques" />
    </set>
    <set name="Comprobantes" inverse="true" generic="true">
      <key>
        <column name="IdBancoTarjeta" />
      </key>
      <one-to-many class="Comprobantes" />
    </set>
    <set name="CuponesTarjetas" inverse="true" generic="true">
      <key>
        <column name="IdBancoTarjeta" />
      </key>
      <one-to-many class="CuponesTarjetas" />
    </set>
  </class>
</hibernate-mapping>