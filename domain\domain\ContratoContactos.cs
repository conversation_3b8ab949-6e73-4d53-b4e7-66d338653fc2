//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 6/11/2023 09:33:14
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.ContratoContactos, Domain in the schema.
    /// </summary>
    public partial class ContratoContactos {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for ContratoContactos constructor in the schema.
        /// </summary>
        public ContratoContactos()
        {
            this.ContratoContactosEmails = new HashSet<ContratoContactosEmail>();
            this.ContratoContactosTels = new HashSet<ContratoContactosTel>();
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Nombre in the schema.
        /// </summary>
        public virtual string Nombre
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Apellido in the schema.
        /// </summary>
        public virtual string Apellido
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for NumeroDoc in the schema.
        /// </summary>
        public virtual string NumeroDoc
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for FechaNac in the schema.
        /// </summary>
        public virtual System.Nullable<System.DateTime> FechaNac
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Activo in the schema.
        /// </summary>
        public virtual System.Nullable<bool> Activo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for DocTipos in the schema.
        /// </summary>
        public virtual DocTipos DocTipos
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Sexos in the schema.
        /// </summary>
        public virtual Sexos Sexos
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Contratos in the schema.
        /// </summary>
        public virtual Contratos Contratos
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ContratoContactosEmails in the schema.
        /// </summary>
        public virtual ISet<ContratoContactosEmail> ContratoContactosEmails
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ContratoContactosTels in the schema.
        /// </summary>
        public virtual ISet<ContratoContactosTel> ContratoContactosTels
        {
            get;
            set;
        }
    }

}
