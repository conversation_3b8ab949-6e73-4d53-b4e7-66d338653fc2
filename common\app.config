<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="Common.Properties.Settings1" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false"/>
    </sectionGroup>
  </configSections>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Iesi.Collections" publicKeyToken="aa95f207798dfdb4" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.0.0.0" newVersion="4.0.0.0"/>
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <applicationSettings>
    <Common.Properties.Settings1>
      <setting name="mailUser" serializeAs="String">
        <value><EMAIL></value>
      </setting>
      <setting name="mailPassword" serializeAs="String">
        <value>NoResponder2020</value>
      </setting>
      <setting name="from" serializeAs="String">
        <value><EMAIL></value>
      </setting>
      <setting name="smtpServer" serializeAs="String">
        <value>mail.clubmassrl.com.ar </value>
      </setting>
      <setting name="smtpPort" serializeAs="String">
        <value>587</value>
      </setting>
    </Common.Properties.Settings1>
  </applicationSettings>
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8"/></startup></configuration>
