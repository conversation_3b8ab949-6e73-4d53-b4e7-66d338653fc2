﻿<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="IVAAlicuotas" table="IVAAlicuotas">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Alicuota" type="Decimal">
      <column name="Alicuota" not-null="false" precision="19" scale="4" sql-type="money" />
    </property>
    <property name="Descripcion" type="String">
      <column name="Descripcion" not-null="false" length="20" sql-type="nvarchar" />
    </property>
    <property name="Nula" type="Int16">
      <column name="Nula" not-null="true" precision="5" scale="0" sql-type="smallint" />
    </property>
  </class>
</hibernate-mapping>