﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 22/5/2023 14:55:38
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.Efectivo, Domain in the schema.
    /// </summary>
    public partial class Efectivo {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for Efectivo constructor in the schema.
        /// </summary>
        public Efectivo()
        {
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Importe in the schema.
        /// </summary>
        public virtual System.Nullable<decimal> Importe
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Fecha in the schema.
        /// </summary>
        public virtual System.Nullable<System.DateTime> Fecha
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ValorMoneda in the schema.
        /// </summary>
        public virtual System.Nullable<decimal> ValorMoneda
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Comprobantes in the schema.
        /// </summary>
        public virtual Comprobantes Comprobantes
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Monedas in the schema.
        /// </summary>
        public virtual Monedas Monedas
        {
            get;
            set;
        }
    }

}
