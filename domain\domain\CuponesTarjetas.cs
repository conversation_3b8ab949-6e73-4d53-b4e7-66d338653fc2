//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 25/9/2023 15:48:43
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.CuponesTarjetas, Domain in the schema.
    /// </summary>
    public partial class CuponesTarjetas {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for CuponesTarjetas constructor in the schema.
        /// </summary>
        public CuponesTarjetas()
        {
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for CodCupon in the schema.
        /// </summary>
        public virtual System.Nullable<int> CodCupon
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for NroLote in the schema.
        /// </summary>
        public virtual System.Nullable<int> NroLote
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for CantCuotas in the schema.
        /// </summary>
        public virtual System.Nullable<decimal> CantCuotas
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for CodAutorizacion in the schema.
        /// </summary>
        public virtual string CodAutorizacion
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Importe in the schema.
        /// </summary>
        public virtual System.Nullable<decimal> Importe
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Monedas in the schema.
        /// </summary>
        public virtual Monedas Monedas
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Comprobantes in the schema.
        /// </summary>
        public virtual Comprobantes Comprobantes
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for BancosTarjetas in the schema.
        /// </summary>
        public virtual BancosTarjetas BancosTarjetas
        {
            get;
            set;
        }
    }

}
