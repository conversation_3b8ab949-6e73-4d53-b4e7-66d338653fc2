﻿using Common.DDL;
using DAO;
using Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Caja
{
    public class TrasferenciaService
    {
            public static IList<Transferencias> GetAll()
            {
                IList<Transferencias> listaAux;
                using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
                {
                    listaAux = (IList<Transferencias>)new TransferenciasRepository(sess).GetAll();
                    sess.Close();
                    sess.Dispose();
                }

                return (listaAux);
            }
            public static List<ItemCombo> GetByBuscarBancos(bool Activo, string QueEs)
            {
                IList<BancosTarjetas> listaAux;
                using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
                {
                    listaAux = (IList<BancosTarjetas>)new BancosTarjetasRepository(sess).GetByBuscarBancos(true, "B");
                    sess.Close();
                    sess.Dispose();
                }

                ItemCombo item;
                ListaCombo lista = new ListaCombo();
                foreach (BancosTarjetas p in listaAux)
                {

                    item = new ItemCombo();
                    item.ID = p.Id.ToString();
                    item.Descripcion = p.Nombre;
                    lista.Add(item);
                }
                return (lista);
            }

        public static Transferencias getById(int id)
        {
            Transferencias u;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                u = new TransferenciasRepository(sess).GetByKey(id);

                if (u != null)
                {
                    //NHibernateUtil.Initialize();

                }
                sess.Close();
                sess.Dispose();
                return u;
            }
        }
        public static void SaveOrUpdate(Transferencias tr)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new TransferenciasRepository(sess).Add(tr);

                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }
            }
        }
        public static void Delete(Domain.Transferencias tr)
            {
                using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
                {
                    using (NHibernate.ITransaction tx = sess.BeginTransaction())
                    {
                        try
                        {
                            new TransferenciasRepository(sess).Remove(tr);
                            tx.Commit();
                        }
                        catch (Exception e)
                        {
                            tx.Rollback();
                            throw e;
                        }
                        finally
                        {
                            sess.Close();
                            sess.Dispose();
                        }
                    }

                }
            }

    }
}
