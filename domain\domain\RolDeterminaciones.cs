//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 8/11/2023 09:53:25
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.RolDeterminaciones, Domain in the schema.
    /// </summary>
    public partial class RolDeterminaciones {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for RolDeterminaciones constructor in the schema.
        /// </summary>
        public RolDeterminaciones()
        {
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Visible in the schema.
        /// </summary>
        public virtual System.Nullable<bool> Visible
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Rol in the schema.
        /// </summary>
        public virtual Rol Rol
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Determinaciones in the schema.
        /// </summary>
        public virtual Determinaciones Determinaciones
        {
            get;
            set;
        }
    }

}
