﻿using DAO;
using Domain;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Contratos {
    public class ContratoContactosEmailServices {

        public static void SaveOrUpdate(ContratoContactosEmail contratoContactosEmail) {

            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                using(NHibernate.ITransaction tx = sess.BeginTransaction()) {
                    try {
                        new ContratoContactosEmailRepository(sess).Add(contratoContactosEmail);
                        tx.Commit();
                    } catch(Exception e) {
                        tx.Rollback();
                        throw e;
                    } finally {
                        sess.Close();
                        sess.Dispose();
                    }
                }

            }
        }

        public static IList<ContratoContactosEmail> GetAll() {
            IList<ContratoContactosEmail> listaAux;
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                listaAux = (IList<ContratoContactosEmail>) new ContratoContactosEmailRepository(sess).GetAll();
                sess.Close();
                sess.Dispose();
            }

            return (listaAux);
        }

        public static ContratoContactosEmail GetById(int id) {
            ContratoContactosEmail EE;
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                EE = new ContratoContactosEmailRepository(sess).GetByKey(id);

                if(EE != null) {
                    NHibernateUtil.Initialize(EE);

                }
                sess.Close();
                sess.Dispose();
                return EE;
            }
        }


        public static ContratoContactosEmail GetByContratoContacto(int idContratoContacto) {
            ContratoContactosEmail EE;
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                EE = new ContratoContactosEmailRepository(sess).GetByContratoContacto(idContratoContacto);

                if(EE != null) {
                    NHibernateUtil.Initialize(EE);

                }
                sess.Close();
                sess.Dispose();
                return EE;
            }
        }


        public static void Delete(ContratoContactosEmail EE) {
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                using(NHibernate.ITransaction tx = sess.BeginTransaction()) {
                    try {
                        new ContratoContactosEmailRepository(sess).Remove(EE);
                        tx.Commit();
                    } catch(Exception e) {
                        tx.Rollback();
                        throw e;
                    } finally {
                        sess.Close();
                        sess.Dispose();
                    }
                }
            }
        }
    }
}
