//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 28/11/2023 16:48:49
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Collections.Generic;

namespace Domain
{
    public partial interface IProtocolosRepository : IRepository<Protocolos>
    {
        ICollection<Protocolos> GetAll();
        Protocolos GetByKey(int _Id);
    }
}
