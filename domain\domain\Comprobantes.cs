//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 25/9/2023 14:39:43
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.Comprobantes, Domain in the schema.
    /// </summary>
    public partial class Comprobantes {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for Comprobantes constructor in the schema.
        /// </summary>
        public Comprobantes()
        {
            this.Cheques = new HashSet<Cheques>();
            this.ComprobantesRelacionados = new HashSet<ComprobantesRelacionados>();
            this.ComprobantesRetenciones = new HashSet<ComprobantesRetenciones>();
            this.CuponesTarjetas = new HashSet<CuponesTarjetas>();
            this.Efectivos = new HashSet<Efectivo>();
            this.Recibos = new HashSet<Recibos>();
            this.Transferencias = new HashSet<Transferencias>();
            this.Contratos = new HashSet<Contratos>();
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Fecha in the schema.
        /// </summary>
        public virtual System.Nullable<System.DateTime> Fecha
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Letra in the schema.
        /// </summary>
        public virtual string Letra
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Prefijo in the schema.
        /// </summary>
        public virtual System.Nullable<short> Prefijo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Numero in the schema.
        /// </summary>
        public virtual System.Nullable<int> Numero
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Importe in the schema.
        /// </summary>
        public virtual System.Nullable<decimal> Importe
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Vencimiento in the schema.
        /// </summary>
        public virtual System.Nullable<System.DateTime> Vencimiento
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Observaciones in the schema.
        /// </summary>
        public virtual string Observaciones
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Saldo in the schema.
        /// </summary>
        public virtual System.Nullable<decimal> Saldo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for NroCopia in the schema.
        /// </summary>
        public virtual System.Nullable<short> NroCopia
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for NroOperacion in the schema.
        /// </summary>
        public virtual System.Nullable<int> NroOperacion
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Anulado in the schema.
        /// </summary>
        public virtual short Anulado
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for InteresMensualAplicableRecargos in the schema.
        /// </summary>
        public virtual System.Nullable<float> InteresMensualAplicableRecargos
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Eliminado in the schema.
        /// </summary>
        public virtual int Eliminado
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Orden in the schema.
        /// </summary>
        public virtual System.Nullable<int> Orden
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ValorMoneda in the schema.
        /// </summary>
        public virtual decimal ValorMoneda
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Contado in the schema.
        /// </summary>
        public virtual short Contado
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Copias in the schema.
        /// </summary>
        public virtual System.Nullable<int> Copias
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Cae in the schema.
        /// </summary>
        public virtual string Cae
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for VencimientoCae in the schema.
        /// </summary>
        public virtual string VencimientoCae
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for IdContrato in the schema.
        /// </summary>
        public virtual System.Nullable<int> IdContrato
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Cheques in the schema.
        /// </summary>
        public virtual ISet<Cheques> Cheques
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ComprobanteTipos in the schema.
        /// </summary>
        public virtual ComprobanteTipos ComprobanteTipos
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for BancosTarjetas in the schema.
        /// </summary>
        public virtual BancosTarjetas BancosTarjetas
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Monedas in the schema.
        /// </summary>
        public virtual Monedas Monedas
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ComprobantesCabeceras in the schema.
        /// </summary>
        public virtual ComprobantesCabeceras ComprobantesCabeceras
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ComprobantesRelacionados in the schema.
        /// </summary>
        public virtual ISet<ComprobantesRelacionados> ComprobantesRelacionados
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ComprobantesRetenciones in the schema.
        /// </summary>
        public virtual ISet<ComprobantesRetenciones> ComprobantesRetenciones
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for CuponesTarjetas in the schema.
        /// </summary>
        public virtual ISet<CuponesTarjetas> CuponesTarjetas
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Efectivos in the schema.
        /// </summary>
        public virtual ISet<Efectivo> Efectivos
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Recibos in the schema.
        /// </summary>
        public virtual ISet<Recibos> Recibos
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Transferencias in the schema.
        /// </summary>
        public virtual ISet<Transferencias> Transferencias
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Contratos in the schema.
        /// </summary>
        public virtual ISet<Contratos> Contratos
        {
            get;
            set;
        }
    }

}
