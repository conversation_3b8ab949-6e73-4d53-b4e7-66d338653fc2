﻿using Common.DDL;
using DAO;
using Domain;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Contratos
{
    public class DocTiposServices
    {
        private static string Abreviatura;

        public static IList<DocTipos> GetAll() {
            IList<DocTipos> listaAux;
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                listaAux = (IList<DocTipos>) new DocTiposRepository(sess).GetAll();
                sess.Close();
                sess.Dispose();
            }

            return (listaAux);
        }

        public static List<ItemCombo> GetByTipoDoc(bool Activo) {
            IList<DocTipos> listaAux;
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                listaAux = (IList<DocTipos>) new DocTiposRepository(sess).GetByTipoDoc(Activo);
                sess.Close();
                sess.Dispose();
            }

            ItemCombo item;
            ListaCombo lista = new ListaCombo();
            foreach(DocTipos p in listaAux) {

                item = new ItemCombo();
                item.ID = p.Id.ToString();
                item.Descripcion = p.Abreviatura;
                lista.Add(item);
            }
            return (lista);
        }

        public static DocTipos getById(int id) {
            DocTipos u;
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                u = new DocTiposRepository(sess).GetByKey(id);

                if(u != null) {
                    //NHibernateUtil.Initialize();

                }
                sess.Close();
                sess.Dispose();
                return u;
            }
        }
        public static List<ItemCombo> GetBybusquedadedoc(bool Activo, string Abreviatura)
        {
            IList<DocTipos> listaAux;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                listaAux = (IList<DocTipos>)new DocTiposRepository(sess).GetByBuscarDoc(Activo, Abreviatura);
                sess.Close();
                sess.Dispose();
            }

            ItemCombo item;
            ListaCombo lista = new ListaCombo();
            foreach (DocTipos p in listaAux)
            {

                item = new ItemCombo();
                item.ID = p.Id.ToString();
                item.Descripcion = p.Abreviatura;
                lista.Add(item);
            }
            return (lista);
        }

        public static void Delete(Domain.DocTipos u) {
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                using(NHibernate.ITransaction tx = sess.BeginTransaction()) {
                    try {
                        new DocTiposRepository(sess).Remove(u);
                        tx.Commit();
                    } catch(Exception e) {
                        tx.Rollback();
                        throw e;
                    } finally {
                        sess.Close();
                        sess.Dispose();
                    }
                }

            }
        }
    }
}
