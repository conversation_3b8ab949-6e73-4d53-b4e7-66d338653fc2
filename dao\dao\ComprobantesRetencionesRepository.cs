﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 10/8/2023 15:55:09
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class ComprobantesRetencionesRepository : NHibernateRepository<Domain.ComprobantesRetenciones>, IComprobantesRetencionesRepository
    {
        public ComprobantesRetencionesRepository(ISession session) : base(session)
        {
        }

        public virtual ICollection<Domain.ComprobantesRetenciones> GetAll()
        {
            return session.CreateQuery(string.Format("from ComprobantesRetenciones")).List<Domain.ComprobantesRetenciones>();
        }

        public virtual Domain.ComprobantesRetenciones GetByKey(int _Id)
        {
            return session.Get<Domain.ComprobantesRetenciones>(_Id);
        }
    }
}
