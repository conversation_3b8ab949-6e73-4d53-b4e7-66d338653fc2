﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 20/10/2023 15:17:33
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class FuentesRepository : NHibernateRepository<Domain.Fuentes>, IFuentesRepository
    {
        public FuentesRepository(ISession session) : base(session)
        {
        }

        public virtual ICollection<Domain.Fuentes> GetAll()
        {
            return session.CreateQuery(string.Format("from Fuentes")).List<Domain.Fuentes>();
        }

        public virtual Domain.Fuentes GetByKey(int _Id)
        {
            return session.Get<Domain.Fuentes>(_Id);
        }
        public virtual Domain.Fuentes ExisteCodigo(string stringCodigo)
        {
            string hql = "from Fuentes where upper(NroDeFuente)=:stringCodigo  and Activo=true";
            return session.CreateQuery(hql)

          .SetParameter("stringCodigo", stringCodigo.ToUpper())
          .UniqueResult<Domain.Fuentes>();
        }

        public virtual bool ExisteNombre(string Descripcion)
        {
            string hql = "select count(*) from Fuentes where upper(Descripcion) = :Descripcion and Activo = true";
            long count = session.CreateQuery(hql)
                .SetParameter("Descripcion", Descripcion.ToUpper())
                .UniqueResult<long>();

            return count > 0;
        }
        public virtual ICollection<Domain.Fuentes> GetByFuentes(bool Activo) {
            string hql = "from Fuentes order BY Descripcion asc";

            IQuery q = session.CreateQuery(hql);
            return q.List<Domain.Fuentes>();

        }
    }
}
