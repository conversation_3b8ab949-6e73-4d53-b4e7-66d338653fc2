//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 3/11/2023 09:44:13
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;
using System.Collections;

namespace DAO
{
    public partial class ProvinciasRepository : NHibernateRepository<Domain.Provincias>, IProvinciasRepository
    {
        public ProvinciasRepository(ISession session) : base(session)
        {
        }

        public virtual ICollection<Domain.Provincias> GetAll()
        {
            return session.CreateQuery(string.Format("from Provincias")).List<Domain.Provincias>();
        }

        public virtual Domain.Provincias GetByKey(int _Id)
        {
            return session.Get<Domain.Provincias>(_Id);
        }
        public virtual ICollection<Domain.Provincias> GetByBuscarProvincias(int Id, bool Activo)
        {
            string hql = "from Provincias  WHERE Id = :Id and Activo = :Activo order BY Nombre asc";

            IQuery q = session.CreateQuery(hql);
            q.SetParameter("Id", Id);
            q.SetParameter("Activo", Activo);
            
            return q.List<Domain.Provincias>();

        }
        public virtual ICollection<Domain.Provincias> GetAllNeuquen(bool Activo)
        {
            string hql = "from Provincias Where Activo = :Activo Order BY Nombre asc";

            IQuery q = session.CreateQuery(hql);
            q.SetParameter("Activo", Activo);
            return q.List<Domain.Provincias>();
        }
    }
}
