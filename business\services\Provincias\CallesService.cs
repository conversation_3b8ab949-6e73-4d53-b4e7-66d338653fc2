﻿using DAO;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Provincias
{
    public class CallesService {

        //Trae todo
        public static IList<Domain.Calles> GetByLocalidades(int idLocalidades) {

            IList<Domain.Calles> listaAux;
            using (ISession sess = NHibernateSessionProvider.GetSession()) {

                listaAux = (IList<Domain.Calles>) new CallesRepository(sess).GetByLocalidades(idLocalidades);
                sess.Close();
                sess.Dispose();
            }
            return (listaAux);

        }

        //obtener por id
        public static Domain.Calles GetById(int id) {

            Domain.Calles p;
            using(ISession sess = NHibernateSessionProvider.GetSession()) {

                p = (Domain.Calles) new CallesRepository(sess).GetByKey(id);
                sess.Close();
                sess.Dispose();
                return p;
            }

        }

        //agregar y actualizar
        public static void SaveOrUpdate(Domain.Calles p) {

            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {

                using(NHibernate.ITransaction tx = sess.BeginTransaction()) {

                    try {

                        new CallesRepository(sess).Add(p);

                        tx.Commit();
                    } catch(Exception e) {

                        tx.Rollback();
                        throw e;
                    } finally {

                        sess.Close();
                        sess.Dispose();
                    }

                }

            }

        }

        //borrar
        public static void Delete(Domain.Calles p)
        {
            using(ISession sess = NHibernateSessionProvider.GetSession())
            {
                using(ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new CallesRepository(sess).Remove(p);
                        tx.Commit();
                    } catch(Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    } finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }

                }

            }

        }

    }

}