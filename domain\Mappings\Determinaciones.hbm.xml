﻿<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="Determinaciones" table="Determinaciones">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="NroDeterminacion" type="Int32">
      <column name="NroDeterminacion" not-null="false" precision="10" scale="0" sql-type="int" />
    </property>
    <property name="Descripcion" type="String">
      <column name="Descripcion" not-null="false" sql-type="nvarchar" />
    </property>
    <property name="UnidadPrimaria" type="String">
      <column name="Unidad_primaria" not-null="false" length="100" sql-type="nvarchar" />
    </property>
    <property name="UnidadSecundaria" type="String">
      <column name="Unidad_secundaria" not-null="false" length="100" sql-type="nvarchar" />
    </property>
    <property name="FactorConversionPrefijo" type="Decimal">
      <column name="Factor_conversion_Prefijo" not-null="false" precision="10" scale="4" sql-type="decimal" />
    </property>
    <property name="FactorConversionSufijo" type="Decimal">
      <column name="Factor_conversion_Sufijo" not-null="false" precision="10" scale="4" sql-type="decimal" />
    </property>
    <property name="ValorReferencia" type="String">
      <column name="ValorReferencia" not-null="false" length="100" sql-type="nvarchar" />
    </property>
    <property name="DecimalesMostrar" type="Int32">
      <column name="Decimales_mostrar" not-null="false" precision="10" scale="0" sql-type="int" />
    </property>
    <property name="Activo" type="Boolean">
      <column name="Activo" not-null="false" sql-type="bit" />
    </property>
    <set name="ProtocoloDeterminaciones" inverse="true" generic="true">
      <key>
        <column name="IdDeterminacion" />
      </key>
      <one-to-many class="ProtocoloDeterminaciones" />
    </set>
    <set name="Rols" inverse="true" generic="true">
      <key>
        <column name="IdDeterminacion" />
      </key>
      <one-to-many class="Rol" />
    </set>
    <set name="RolDeterminaciones" inverse="true" generic="true">
      <key>
        <column name="IdDeterminación" />
      </key>
      <one-to-many class="RolDeterminaciones" />
    </set>
    <set name="PlantillaProtocolos" table="PlantillaProtocoloDeterminaciones" inverse="true" generic="true">
      <key>
        <column name="IdDeterminaciones" />
      </key>
      <many-to-many class="PlantillaProtocolos" fetch="join">
        <column name="IdProtocoloTipo" />
      </many-to-many>
    </set>
  </class>
</hibernate-mapping>