//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 26/9/2023 15:57:58
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using Domain;
using System;
using System.Collections.Generic;

namespace DAO
{
    public partial interface INumeracionesRepository : IRepository<Domain.Numeraciones>
    {
        IList<Domain.Numeraciones> GetAll();
        Domain.Numeraciones GetByKey(int _Id);
    }
}
