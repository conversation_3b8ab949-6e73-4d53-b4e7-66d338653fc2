//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 25/9/2023 14:39:43
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.Transferencias, Domain in the schema.
    /// </summary>
    public partial class Transferencias {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for Transferencias constructor in the schema.
        /// </summary>
        public Transferencias()
        {
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Descripcion in the schema.
        /// </summary>
        public virtual string Descripcion
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Importe in the schema.
        /// </summary>
        public virtual decimal Importe
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Fecha in the schema.
        /// </summary>
        public virtual System.Nullable<System.DateTime> Fecha
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for NroTransferencia in the schema.
        /// </summary>
        public virtual string NroTransferencia
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Comprobantes in the schema.
        /// </summary>
        public virtual Comprobantes Comprobantes
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Recibos in the schema.
        /// </summary>
        public virtual Recibos Recibos
        {
            get;
            set;
        }
    }

}
