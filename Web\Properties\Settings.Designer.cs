﻿//------------------------------------------------------------------------------
// <auto-generated>
//     Este código fue generado por una herramienta.
//     Versión de runtime:4.0.30319.42000
//
//     Los cambios en este archivo podrían causar un comportamiento incorrecto y se perderán si
//     se vuelve a generar el código.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Web.Properties {
    using System;
    
    
    /// <summary>
    ///   Clase de recurso fuertemente tipado, para buscar cadenas traducidas, etc.
    /// </summary>
    // StronglyTypedResourceBuilder generó automáticamente esta clase
    // a través de una herramienta como ResGen o Visual Studio.
    // Para agregar o quitar un miembro, edite el archivo .ResX y, a continuación, vuelva a ejecutar ResGen
    // con la opción /str o recompile su proyecto de VS.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "********")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Settings {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Settings() {
        }
        
        /// <summary>
        ///   Devuelve la instancia de ResourceManager almacenada en caché utilizada por esta clase.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Web.Properties.Settings", typeof(Settings).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Reemplaza la propiedad CurrentUICulture del subproceso actual para todas las
        ///   búsquedas de recursos mediante esta clase de recurso fuertemente tipado.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Busca una cadena traducida <NAME_EMAIL>.
        /// </summary>
        internal static string EmailApp {
            get {
                return ResourceManager.GetString("EmailApp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Busca una cadena traducida <NAME_EMAIL>.
        /// </summary>
        internal static string EmailInfo {
            get {
                return ResourceManager.GetString("EmailInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Busca una cadena traducida similar a Sistema de Gestión de Laboratorio - EPAS.
        /// </summary>
        internal static string NombreApp {
            get {
                return ResourceManager.GetString("NombreApp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Busca una cadena traducida <NAME_EMAIL>.
        /// </summary>
        internal static string SMTP_from {
            get {
                return ResourceManager.GetString("SMTP_from", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Busca una cadena traducida similar a 587.
        /// </summary>
        internal static string SMTP_port {
            get {
                return ResourceManager.GetString("SMTP_port", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Busca una cadena traducida similar a 21Ue#?OqOQEo.
        /// </summary>
        internal static string SMTP_pwd {
            get {
                return ResourceManager.GetString("SMTP_pwd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Busca una cadena traducida similar a mail.dityc.com.
        /// </summary>
        internal static string SMTP_server {
            get {
                return ResourceManager.GetString("SMTP_server", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Busca una cadena traducida <NAME_EMAIL>.
        /// </summary>
        internal static string SMTP_user {
            get {
                return ResourceManager.GetString("SMTP_user", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Busca una cadena traducida similar a http://mayus.com.ar/EPAS/.
        /// </summary>
        internal static string URLApp {
            get {
                return ResourceManager.GetString("URLApp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Busca una cadena traducida similar a http://mayus.com.ar/EPAS/.
        /// </summary>
        internal static string URLInfo {
            get {
                return ResourceManager.GetString("URLInfo", resourceCulture);
            }
        }
    }
}
