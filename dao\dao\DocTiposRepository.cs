//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 3/11/2023 09:44:13
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class DocTiposRepository : NHibernateRepository<Domain.DocTipos>, IDocTiposRepository
    {
        public DocTiposRepository(ISession session) : base(session)
        {
        }

        public virtual ICollection<Domain.DocTipos> GetAll()
        {
            return session.CreateQuery(string.Format("from DocTipos")).List<Domain.DocTipos>();
        }

        public virtual Domain.DocTipos GetByKey(int _Id)
        {
            return session.Get<Domain.DocTipos>(_Id);
        }

        public virtual ICollection<Domain.DocTipos> GetByTipoDoc(bool Activo)
        {
            string hql = "from DocTipos  WHERE Activo = :Activo order BY TipoDoc asc";

            IQuery q = session.CreateQuery(hql);
            q.SetParameter("Activo", Activo);
            return q.List<Domain.DocTipos>();

        }
        public virtual ICollection<Domain.DocTipos> GetByBuscarDoc(bool Activo, string Abreviatura)
        {
            string hql = "from DocTipos  WHERE Activo = :Activo and Abreviatura = :Abreviatura order BY TipoDoc asc";

            IQuery q = session.CreateQuery(hql);
            q.SetParameter("Activo", Activo);
            q.SetParameter("Abreviatura", Abreviatura);
            return q.List<Domain.DocTipos>();

        }


    }
}
