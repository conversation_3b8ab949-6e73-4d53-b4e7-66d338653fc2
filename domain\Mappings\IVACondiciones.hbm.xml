﻿<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="IVACondiciones" table="IVACondiciones">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Descripcion" type="String">
      <column name="Descripcion" not-null="false" length="25" sql-type="nvarchar" />
    </property>
    <property name="Abreviatura" type="String">
      <column name="Abreviatura" not-null="false" length="5" sql-type="nvarchar" />
    </property>
    <property name="LetraFactura" type="String">
      <column name="LetraFactura" not-null="true" length="1" sql-type="varchar" />
    </property>
    <property name="ExigeCUIT" type="Boolean">
      <column name="ExigeCUIT" not-null="false" sql-type="bit" />
    </property>
    <property name="Activo" type="Boolean">
      <column name="Activo" not-null="false" sql-type="bit" />
    </property>
    <set name="Contratos" inverse="true" generic="true">
      <key>
        <column name="IdIVACondicion" />
      </key>
      <one-to-many class="Contratos" />
    </set>
  </class>
</hibernate-mapping>