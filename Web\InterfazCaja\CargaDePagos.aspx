﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="CargaDePagos.aspx.cs" Inherits="Web.InterfazCaja.WebForm1" %>

<%@ Register Assembly="DevExpress.Web.v18.1" Namespace="DevExpress.Web" TagPrefix="dx" %>


<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server"> 

    <script type="text/javascript">

        var rows = document.querySelectorAll(".dxgvDataRow");

        for (var i = 0; i < rows.values; i++) {
            var row = rows[i];
            var cellValue = row.querySelector(".dxgv[data-fieldname='NroTransferencia']");
            var nroTran = document.getElementById('<%= seTransferNumero.ClientID %>');

            if (cellValue.value === nroTran.value) {

                confirm('Mismo número');
            }
        }

        function ValidateOnClick(s, e) {
            if (ASPxClientEdit.ValidateEditorsInContainer(null)) {
                LoadingPanel.Show();
                return true;
            }
            else {
                return false;
            }
        };

        function ValidateBusquedaOnClick(s, e) {
            if (ASPxClientEdit.ValidateGroup('buscarContrato')) {
                LoadingPanel.Show();

                return true;
            }
            else {
                return false;
            }
        };

        function RedireccionarAPaginaPrincipal() {
            window.location.href = "/SIGeLab/interfazcaja/cargadepagos.aspx";
            LoadingPanel.Show();
        }

        function OnDeleteChequeClick(id, visibleIndex) {

            if (confirm('Esta seguro que desea eliminar el cheque de Serie y Número ' + id + ' de la grilla?')) {


                Swal.fire({
                    title: 'Se perderán los datos cargados en esta fila.',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Si, eliminar.'
                }).then((result) => {
                    if (result.isConfirmed) {

                        GrVCheques.DeleteRow(visibleIndex);
                        CallbackPanelTotales.PerformCallback();

                    }
                })
            }
        };

        function OnDeleteTransfClick(id, visibleIndex) {

            if (confirm('Esta seguro que desea eliminar el número ' + id + ' de la transferencia en la grilla?')) {


                Swal.fire({
                    title: 'Se perderán los datos cargados en esta fila.',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Si, eliminar.'
                }).then((result) => {
                    if (result.isConfirmed) {

                        GrVTransf.DeleteRow(visibleIndex);
                        CallbackPanelTotales.PerformCallback();

                    }
                })
            }
        };

        function OnDeleteReteClick(id, visibleIndex) {

            if (confirm('Esta seguro que desea eliminar el número ' + id + ' de la retención en la grilla?')) {


                Swal.fire({
                    title: 'Se perderán los datos cargados en esta fila.',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Si, eliminar.'
                }).then((result) => {
                    if (result.isConfirmed) {

                        GrVRetencion.DeleteRow(visibleIndex);
                        CallbackPanelTotales.PerformCallback();
                    }
                })
            }
        };

        function OnDeleteTarjClick(id, visibleIndex) {

            if (confirm('Esta seguro que desea eliminar el codigo de cupón ' + id + ' de tarjeta en la grilla?')) {


                Swal.fire({
                    title: 'Se perderán los datos cargados en esta fila.',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Si, eliminar.'
                }).then((result) => {
                    if (result.isConfirmed) {

                        GrVTarjeta.DeleteRow(visibleIndex);
                        CallbackPanelTotales.PerformCallback();
                    }
                })
            }
        };

        function ValidateEfectivoOnClick(s, e) {

            if (ASPxClientEdit.ValidateGroup('efectivo')) {
                LoadingPanel.Show();
                AgregarPago_Callback.PerformCallback('efectivo');

                seEfectivo.SetText();

                return true;
            }
            else {

                return false;
            }
        };

        function ValidateChequeOnClick(s, e) {

            if (ASPxClientEdit.ValidateGroup('cheque')) {
                LoadingPanel.Show();
                AgregarPago_Callback.PerformCallback('cheque');

                dropBancos.SetSelectedIndex(-1);
                txtNro.SetText();
                txtSerie.SetText();
                dateChequeFecha.SetDate();
                dateChequeDeposito.SetDate();
                seImporteCheque.SetText();
                seInteresFinan.SetText();
                txtObservacion.SetText();


                return true;
            }
            else {
                return false;
            }
        };

        function ValidateTransferenciaOnClick(s, e) {



            if (ASPxClientEdit.ValidateGroup('transferencia')) {
                LoadingPanel.Show();
                AgregarPago_Callback.PerformCallback('transferencia');

                dropBancos1.SetSelectedIndex(-1);
                dateTransferFecha.SetDate();
                seTransferImporte.SetText();
                seTransferNumero.SetText();

                return true;
            }
            else {

                return false;
            }
        };

        function ValidateRetencionOnClick(s, e) {

            if (ASPxClientEdit.ValidateGroup('retencion')) {
                LoadingPanel.Show();
                AgregarPago_Callback.PerformCallback('retencion');

                dropRetenciones.SetSelectedIndex(-1);
                dateRetenFecha.SetDate();
                seNumeroReten.SetText();
                seRetenImporte.SetText();

                return true;
            }
            else {
                return false;
            }
        };

        function ValidateTarjetaOnClick(s, e) {

            if (ASPxClientEdit.ValidateGroup('tarjeta')) {
                LoadingPanel.Show();
                AgregarPago_Callback.PerformCallback('tarjeta');

                dropTarjeta.SetSelectedIndex(-1);
                txtTitular.SetText();
                txtNroCupon.SetText();
                txtNroLote.SetText();
                txtNroAutorizacion.SetText();
                txtPago.SetText();
                seCuotas.SetText();
                seCoeficiente.SetText();
                seInter.SetText();
                seImporteCupon.SetText();

                return true;
            }
            else {
                return false;
            }
        };

        document.addEventListener('keydown', function (event) {
            switch (event.key) {
                case '1':
                    if (event.ctrlKey) {
                        document.getElementById('tabEfectivo').click();
                        document.getElementById('tabEfectivo').focus();
                    }
                    break;
                case '2':
                    if (event.ctrlKey) {
                        document.getElementById('tabCheque').click();
                        document.getElementById('tabCheque').focus();
                    }
                    break;
                case '3':
                    if (event.ctrlKey) {
                        document.getElementById('tabTransferencia').click();
                        document.getElementById('tabTransferencia').focus();
                    }
                    break;
                case '4':
                    if (event.ctrlKey) {
                        document.getElementById('tabRetencion').click();
                        document.getElementById('tabRetencion').focus();
                    }
                    break;
                case '5':
                    if (event.ctrlKey) {
                        document.getElementById('tabTarjeta').click();
                        document.getElementById('tabTarjeta').focus();
                    }
                    break;
                case '0':
                    if (event.ctrlKey) {
                        document.getElementById('confirmarPagoTotal').click();
                        document.getElementById('confirmarPagoTotal').focus();
                    }
                    break;
                default:

                    break;
            }
        });



    </script>

</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

    <div class="row gx-1">

        <div class="col-lg-7"">

            <div class="navbar navbar-expand-xl bg-secondary navbar-dark rounded-top mt-1">
                <div class="container-fluid">                           
                    <div class="nav-item dropdown">
                        <h5 class="mt-2 mb-1 fs-5" data-bs-toggle="tab"">Cliente</h5>
                    </div>                        
                </div>
            </div>

            <!-- Card Cliente -->

            <div class="tab-content border border-1 rounded-bottom p-1">

                <div class="card">
                    <div class="card-body">
                        <div action="#">

                            <div class="row gx-2 mb-1">
                                <label class="col-lg-2 col-form-label pe-0 fw-semibold btn-buscar">Buscar:</label>
                                <div class="col-lg-4 p-0">
                                    <dx:ASPxComboBox ID="cmbBuscar" NullText="Seleccione Busqueda" CssClass="form-control altura-textbox" runat="server" EnableCallbackMode="true" DropDownStyle="DropDown" >
                                        <Items>
                                            <dx:ListEditItem Text="Contrato" Value="0" />
                                            <dx:ListEditItem Text="DNI / CUIT / CUIL" Value="1" />
                                            <dx:ListEditItem Text="Nombre / Razon Social" Value="2" />
                                        </Items>
                                        <ValidationSettings ValidationGroup="buscarContrato" Display="Dynamic" ErrorDisplayMode="ImageWithText" ErrorTextPosition="Bottom" SetFocusOnError="true">
                                        </ValidationSettings>
                                    </dx:ASPxComboBox>
                                </div>

                                <div class="col-lg-4 pe-0 form-control-feedback form-control-feedback-start flex-grow-1">
                                    <dx:ASPxTextBox ID="txtBarraBusqueda" ClientInstanceName="txtBarraBusqueda" CssClass="form-control altura-textbox" NullText="Ingrese Nombre y/o Apellido" AutoCompleteType="Disabled" runat="server">
                                        <ValidationSettings ValidationGroup="buscarContrato" Display="Dynamic" ErrorDisplayMode="ImageWithText" ErrorTextPosition="Bottom" SetFocusOnError="true">
                                        </ValidationSettings>
                                    </dx:ASPxTextBox>
								    <%--<div class="form-control-feedback-icon">
									    <i class="ph-magnifying-glass fw-bold"></i>
								    </div>--%>
							    </div>

                                <div class="col-lg-1 pe-5 form-control-feedback form-control-feedback-start flex-grow-1">
                                    <dx:ASPxButton ID="Btn_BuscarContrato" runat="server" OnClick="Btn_BuscarContrato_Click" CssClass="btn btn-secondary px-1 py-1" ClientInstanceName="Btn_BuscarContrato" AutoPostBack="False" Text="🔎">
                                        <ClientSideEvents Click="function(s,e) { ValidateBusquedaOnClick(s,e); }" />
                                    </dx:ASPxButton>                                    
                                </div>

                            </div>

							<div class="row mb-1">
                                <label class="col-lg-2 col-form-label fw-semibold">Clientes:</label>
                                <div class="col-lg-10 p-0">
                                    <dx:ASPxComboBox ID="Drop_Clientes" ClientInstanceName="DropClientes" runat="server" DropDownStyle="DropDownList" ValueType="System.Int32" NullText="Resultado de búsqueda" TextFormatString="{0} ({1})" Width="100%" CssClass="form-control altura-textbox" IncrementalFilteringMode="Contains">
                                        <ValidationSettings ValidationGroup="buscarContrato" Display="Dynamic" ErrorDisplayMode="ImageWithText" ErrorTextPosition="Bottom" SetFocusOnError="true">
                                        </ValidationSettings>
                                    </dx:ASPxComboBox>
                                </div>
                            </div>                                

                        </div>
                    </div>
                </div>

            </div>

            <!-- Navbar container -->            

            <div class="navbar navbar-expand-xl bg-secondary mt-1 navbar-dark rounded-top">
                <div class="container-fluid">
                    <div class="nav-tabs-responsive scrollbar-hidden shadow-none order-2 order-xl-1">
                        <ul class="nav nav-tabs fw-bold fs-5 border-0 mt-2 mt-xl-0">
                            <li class="nav-item dropdown ms-3">
                                <a href="#efectivo" id="tabEfectivo" class="navbar-nav-link rounded active px-3" data-bs-toggle="tab">Efectivo<span class="fs-xs fw-semibold">(1)</span></a>
                            </li>

                            <li class="nav-item dropdown ms-1">
                                <a href="#cheque" id="tabCheque" class="navbar-nav-link rounded px-3" data-bs-toggle="tab">Cheque<span class="fs-xs fw-semibold">(2)</span></a>
                            </li>

                            <li class="nav-item dropdown ms-1">
                                <a href="#transferencia" id="tabTransferencia" class="navbar-nav-link rounded px-3" data-bs-toggle="tab">Transferencia<span class="fs-xs fw-semibold">(3)</span></a>
                            </li>

                            <li class="nav-item dropdown ms-1">
                                <a href="#retencion" id="tabRetencion" class="navbar-nav-link rounded px-3" data-bs-toggle="tab">Retención<span class="fs-xs fw-semibold">(4)</span></a>
                            </li>

                            <li class="nav-item dropdown ms-1">
                                <a href="#tarjeta" id="tabTarjeta" class="navbar-nav-link tab-tarjeta rounded px-3" data-bs-toggle="tab">Tarjeta<span class="fs-xs fw-semibold">(5)</span></a>
                            </li>
                        </ul>
                    </div>                    
                </div>
            </div>


            <!-- Tabs container-->
            <div class="tab-content border border-1 rounded-bottom p-1">

                <!-- Efectivo container -->

                <div class="tab-pane fade active show" id="efectivo">
                        
                    <div class="card">
                        <%--<div class="card-header">
                            <h6 class="mb-0 fw-bold fs-5">Efectivo</h6>
                        </div>--%>

                        <fieldset><legend class="col-lg-12 border-secondary border-bottom border-width-2"></legend></fieldset>

                        <div class="card-body">
                            <div action="#">
                                <%--<div class="row mb-1">
                                    <label class="col-lg-2 col-form-label fw-semibold">Saldo:</label>
                                    <div class="col-lg-2 p-0">
                                        <dx:ASPxTextBox ID="txtEfectivoSaldo" CssClass="form-control altura-textbox" NullText="0.00" AutoCompleteType="Disabled" runat="server"></dx:ASPxTextBox>
                                    </div>
                                </div>--%>
                                <div class="row">
                                    <label class="col-lg-2 col-form-label fw-semibold"><span class="text-danger">•</span> Efectivo:</label>
                                    <div class="col-lg-2 p-0">
                                        <dx:ASPxSpinEdit ID="seEfectivo" ClientInstanceName="seEfectivo" SelectInputTextOnClick="true" SpinButtons-ClientVisible="false" NullText="0.00" CssClass="form-control altura-textbox" DisplayFormatString="${0:N2}" Width="125px" DecimalPlaces="2" runat="server" >
                                            <ValidationSettings ValidationGroup="efectivo" Display="Dynamic" ErrorDisplayMode="ImageWithText" SetFocusOnError="true" ErrorTextPosition="Bottom">
                                                <RequiredField IsRequired="true" ErrorText="Campo Obligatorio si paga en efectivo." />
                                            </ValidationSettings>
                                        </dx:ASPxSpinEdit>
                                    </div>
                                </div>

                                <div class="form-group d-flex justify-content-between align-items-center mt-5">
                                    <p class="fw-medium text-danger mb-0" style="font-size:11px">(<span class="fs-6">•</span>) Campos Obligatorios</p>

                                    <dx:ASPxButton ID="BtnEfectivoAgregar" runat="server" CssClass="btn btn-secondary fw-semibold px-0 py-1" CausesValidation="false" ClientInstanceName="BtnEfectivoAgregar" AutoPostBack="False" Text="Agregar">
                                        <ClientSideEvents Click="function(s,e) { ValidateEfectivoOnClick(s,e); }" />
                                    </dx:ASPxButton>
                                    
                                    <%--<dx:ASPxButton ID="BtnEfectivoModificar"  OnClick="btnEfectivoModificar_Click" runat="server" CssClass="btn text-white fw-semibold px-0 py-1 ms-3" BackColor="Brown" ClientInstanceName="BtnEfectivoModificar" AutoPostBack="False" Text="Modificar">
                                        <ClientSideEvents Click="function(s,e) { ValidateOnClick(s,e); }" />
                                    </dx:ASPxButton>--%>

                                </div>
                            </div>
                            
                        </div>
                    </div>
                    
                </div>

                <!-- Cheque container -->

                <div class="tab-pane fade" id="cheque">

                    <div class="card">
                        <%--<div class="card-header">
                            <h6 class="mb-0 fw-bold fs-5">Cheque</h6>
                        </div>--%>

                        <fieldset><legend class="col-lg-12 border-secondary border-bottom border-width-2"></legend></fieldset>

                        <div class="card-body">
                            <div action="#">
                                <div class="row mb-1 px-0">
                                    <label class="col-lg-2 col-form-label fw-semibold"><span class="text-danger">•</span> Banco:</label>
                                    <div class="col-lg-10 p-0">
                                        <dx:ASPxComboBox ID="Drop_Bancos" ClientInstanceName="dropBancos" runat="server" DropDownStyle="DropDownList" ValueType="System.Int32" NullText="[Seleccione Banco]" TextFormatString="{0} ({1})" Width="100%" CssClass="form-control py-0" IncrementalFilteringMode="Contains" Height=39.6px>
                                            <ValidationSettings ValidationGroup="cheque" Display="Dynamic" ErrorDisplayMode="ImageWithText" ErrorTextPosition="Bottom" SetFocusOnError="true">
                                                <RequiredField IsRequired="true" ErrorText="Campo Obligatorio." />
                                            </ValidationSettings>
                                        </dx:ASPxComboBox>
                                    </div>
                                </div>
                                <div class="row mb-1">
                                    <label class="col-lg-2 col-form-label fw-semibold"><span class="text-danger">•</span> Serie:</label>
                                    <div class="col-lg-1 px-0">
                                        <dx:ASPxTextBox ID="txtSerie" ClientInstanceName="txtSerie" CssClass="form-control altura-textbox" Width="60px" AutoCompleteType="Disabled" runat="server">
                                            <ValidationSettings ValidationGroup="cheque" Display="Dynamic" ErrorDisplayMode="ImageWithText" ErrorTextPosition="Bottom" SetFocusOnError="true" >
                                                <RequiredField IsRequired="true" ErrorText="Campo Obligatorio" />
                                            </ValidationSettings>
                                        </dx:ASPxTextBox>
                                    </div>
                                    
                                    <label class="col-lg-2 col-form-label fw-semibold ps-5"><span class="text-danger">•</span> Nro:</label>
                                    <div class="col-lg-7 ps-1 pe-0">
                                        <dx:ASPxTextBox ID="txtNro" ClientInstanceName="txtNro" CssClass="form-control altura-textbox text-end" AutoCompleteType="Disabled" Width="100%" runat="server">
                                            <ValidationSettings ValidationGroup="cheque" Display="Dynamic" ErrorDisplayMode="ImageWithText" ErrorTextPosition="Bottom" SetFocusOnError="true" >
                                                <RequiredField IsRequired="true" ErrorText="Campo Obligatorio" />
                                            </ValidationSettings>
                                        </dx:ASPxTextBox>
                                    </div>
                                </div>

                                <div class="row mb-1">
                                    <label class="col-lg-2 col-form-label fw-semibold"><span class="text-danger">•</span> Fecha:</label>
                                    <div class="col-lg-2 px-0">
                                        <dx:ASPxDateEdit ID="dateChequeFecha" ClientInstanceName="dateChequeFecha" CssClass="form-control py-1 px-0" Width="125px" runat="server" EditFormat="Custom" Height=39.6px>
                                            <TimeSectionProperties>
                                                <TimeEditProperties EditFormatString="hh:mm tt" />
                                            </TimeSectionProperties>
                                            <CalendarProperties>
                                                <FastNavProperties DisplayMode="Inline" />
                                            </CalendarProperties>
                                            <ValidationSettings ValidationGroup="cheque" Display="Dynamic" ErrorDisplayMode="ImageWithText" ErrorTextPosition="Bottom" SetFocusOnError="true">
                                                <RequiredField IsRequired="true" ErrorText="Campo Obligatorio" />
                                            </ValidationSettings>
                                        </dx:ASPxDateEdit>
                                    </div>

                                    <label class="col-lg-2 col-form-label ps-5 pe-0 fw-semibold">Diferido</label>
                                    <div class="col-lg-1 pt-2 ps-1">
                                        <asp:CheckBox ID="cboxDiferido" runat="server" />
                                    </div>

                                    <div class="col-lg-1"></div>

                                    <label class="col-lg-2 col-form-label ps-1 fw-semibold"><span class="text-danger">•</span> Fecha Cobro:</label>
                                    <div class="col-lg-2 p-0">
                                        <dx:ASPxDateEdit ID="dateChequeDeposito" ClientInstanceName="dateChequeDeposito" CssClass="form-control py-1 px-0 w-100" runat="server" EditFormat="Custom" Height=39.6px>
                                            <TimeSectionProperties>
                                                <TimeEditProperties EditFormatString="hh:mm tt" />
                                            </TimeSectionProperties>
                                            <CalendarProperties>
                                                <FastNavProperties DisplayMode="Inline" />
                                            </CalendarProperties>
                                            <ValidationSettings ValidationGroup="cheque" Display="Dynamic" ErrorDisplayMode="ImageWithText" ErrorTextPosition="Bottom" SetFocusOnError="true">
                                                <RequiredField IsRequired="true" ErrorText="Campo Oblig." />
                                            </ValidationSettings>
                                        </dx:ASPxDateEdit>
                                    </div>
                                </div>

                                <div class="row mb-1">
                                    <label class="col-lg-2 col-form-label px-0 fw-semibold"><span class="text-danger">•</span>Importe Cheque:</label>
                                    <div class="col-lg-2 p-0">
                                        <dx:ASPxSpinEdit ID="seImporteCheque" ClientInstanceName="seImporteCheque" SelectInputTextOnClick="true" SpinButtons-ClientVisible="false" NullText="0.00" CssClass="form-control altura-textbox" DisplayFormatString="${0:N2}" Width="125px" DecimalPlaces="2" runat="server" >
                                            <ValidationSettings ValidationGroup="cheque" Display="Dynamic" ErrorDisplayMode="ImageWithText" SetFocusOnError="true" ErrorTextPosition="Bottom">
                                                <RequiredField IsRequired="true" ErrorText="Campo Obligatorio." />
                                            </ValidationSettings>
                                        </dx:ASPxSpinEdit>
                                    </div>

                                    <label class="col-lg-3 col-form-label ps-5 pe-0 fw-semibold">Cobrar Interés</label>
                                    <div class="col-lg-1 pt-2 ps-0">
                                        <asp:CheckBox runat="server" />
                                    </div>

                                    <label class="col-lg-2 col-form-label ps-2 fw-semibold">Interés Finan.:</label>
                                    <div class="col-lg-2 p-0">
                                        <dx:ASPxSpinEdit ID="seInteresFinan" ClientInstanceName="seInteresFinan" SelectInputTextOnClick="true" SpinButtons-ClientVisible="false" NullText="0.00" CssClass="form-control altura-textbox" DisplayFormatString="${0:N2}" DecimalPlaces="2" runat="server" >
                                        </dx:ASPxSpinEdit>
                                    </div>
                                </div>

                                <div class="row mt-1">
                                    <label class="col-lg-2 col-form-label fw-semibold">Observación:</label>
                                    <div class="col-lg-10 p-0">
                                        <dx:ASPxTextBox ID="txtObservacion" ClientInstanceName="txtObservacion" CssClass="form-control altura-textbox" AutoCompleteType="Disabled" runat="server"></dx:ASPxTextBox>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between align-items-center mt-4">
                                    <p class="fw-medium text-danger mb-0" style="font-size:11px">(<span class="fs-5">•</span>) Campos Obligatorios</p>

                                    <dx:ASPxButton ID="BtnChequeAgregar" runat="server" CausesValidation="false" CssClass="btn btn-secondary fw-semibold px-0 py-1" ClientInstanceName="BtnChequeAgregar" AutoPostBack="False" Text="Agregar"> 
                                        <ClientSideEvents Click="function(s,e) { ValidateChequeOnClick(s,e); }" />
                                    </dx:ASPxButton>

<%--                                    <dx:ASPxButton ID="BtnChequeEliminar" OnClick="btnChequeEliminar_Click"  runat="server" CssClass="btn text-white fw-semibold px-0 py-1 ms-3" BackColor="Brown" ClientInstanceName="BtnChequeEliminar" AutoPostBack="False" Text="Eliminar"  >--%>
                                        <%--<ClientSideEvents Click="function(s,e) { ValidateOnClick(s,e); }" />--%>
                                    <%--</dx:ASPxButton>--%>
                                </div>
                                
     
                                <div class="row mt-5 align-content-center" style="margin-top: 10px; margin-left:10px;">
                                     <dx:ASPxGridView ID="GrVCheques" OnDataBinding="GrVCheques_DataBinding" runat="server"  KeyFieldName="SerieNro" ClientInstanceName="GrVCheques" OnRowDeleting="GrVCheques_RowDeleting" >
                                       <Columns>
                                                                                    
                                            <dx:GridViewDataTextColumn FieldName="SerieNro" caption="Serie/Nro"  />
                                            <dx:GridViewDataTextColumn FieldName="ImporteCheque" caption="Importe" />
                                            <dx:GridViewDataDateColumn FieldName="FecEmision" caption="Fecha" />
                                            <dx:GridViewDataDateColumn FieldName="FecDeposito" caption="Fecha Cobro" />
                                           <dx:GridViewDataCheckColumn  FieldName="Diferido" caption="Dif." Width="50px"  />
                                                                                                                                
                                          <dx:GridViewDataColumn Caption="" VisibleIndex="7" Settings-AllowEllipsisInText="False" Width="110">
                                         <DataItemTemplate>
                                        <%--<dx:ASPxButton runat="server" ID="btnEditar" RenderMode="Link" Text="Editar"  OnClick="btnEditar_Click"
                                            CommandArgument='<%# Eval("Id" )%>' AutoPostBack="false">
                                               <ClientSideEvents Click="function (s,e){MostrarLoading();}" />
                                        </dx:ASPxButton>--%>
                                       <%-- <dx:ASPxButton runat="server" ID="btnEliminar" ClientInstanceName="btn1" RenderMode="Link" Text="Eliminar" CausesValidation="false" OnClick="btnEliminarCheque_Click"
                                             CommandArgument='<%# Eval("Id" )%>' AutoPostBack="false">
                                                <ClientSideEvents Click="function(s,e){e.processOnServer =confirm('¿Esta seguro que desea eliminar?');}" />
                                        </dx:ASPxButton>--%>

                                             <a id="DeleteButton" href="javascript:OnDeleteChequeClick('<%# Container.KeyValue.ToString()%>', <%# Container.VisibleIndex.ToString()%>);"> Eliminar</a>
                                        </DataItemTemplate>
                                </dx:GridViewDataColumn>
                                                                                                  
                                       </Columns>
                      
                                    </dx:ASPxGridView>

                                </div>
                            </div>
                        </div>
                    </div>

                </div>

                <!-- Transferencia container -->

                <div class="tab-pane fade" id="transferencia">
                        
                    <div class="card">
                        <%--<div class="card-header">
                            <h6 class="mb-0 fw-bold fs-5">Transferencia</h6>
                        </div>--%>

                        <fieldset><legend class="col-lg-12 border-secondary border-bottom border-width-2"></legend></fieldset>

                        <div class="card-body">
                            <div action="#">
                                <div class="row mb-1">
                                    <label class="col-lg-2 col-form-label fw-semibold pe-1"><span class="text-danger">•</span> Transferencia:</label>
                                    <div class="col-lg-10 p-0">
                                        <dx:ASPxComboBox ID="Drop_Bancos_1" ClientInstanceName="dropBancos1" runat="server" DropDownStyle="DropDownList" ValueType="System.Int32" NullText="[Seleccione Banco]" TextFormatString="{0} ({1})" Width="100%" CssClass="form-control py-0" IncrementalFilteringMode="Contains" Height=39.6px>
                                            <ValidationSettings ValidationGroup="transferencia" Display="Dynamic" ErrorDisplayMode="ImageWithText" ErrorTextPosition="Bottom" SetFocusOnError="true">
                                                <RequiredField IsRequired="true" ErrorText="Campo Obligatorio." />
                                            </ValidationSettings>
                                        </dx:ASPxComboBox>
                                    </div>
                                </div>

                                <div class="row mb-1">
                                    <label class="col-lg-2 col-form-label fw-semibold"><span class="text-danger">•</span> Fecha:</label>
                                    <div class="col-lg-2 p-0">
                                        <dx:ASPxDateEdit ID="dateTransferFecha" ClientInstanceName="dateTransferFecha" CssClass="form-control px-0"  Width="125px" runat="server" EditFormat="Custom" Height=39.6px>
                                            <TimeSectionProperties>
                                                <TimeEditProperties EditFormatString="hh:mm tt" />
                                            </TimeSectionProperties>
                                            <CalendarProperties>
                                                <FastNavProperties DisplayMode="Inline" />
                                            </CalendarProperties>
                                            <ValidationSettings ValidationGroup="transferencia" Display="Dynamic" ErrorDisplayMode="ImageWithText" ErrorTextPosition="Bottom" SetFocusOnError="true">
                                                <RequiredField IsRequired="true" ErrorText="Campo Obligatorio." />
                                            </ValidationSettings>
                                        </dx:ASPxDateEdit>
                                    </div>

                                    <div class="col-lg-1"></div>

                                    <label class="col-lg-2 col-form-label ps-4 fw-semibold"><span class="text-danger">•</span> Número:</label>
                                    <div class="col-lg-2 p-0">
                                        <dx:ASPxSpinEdit ID="seTransferNumero" ClientInstanceName="seTransferNumero" SelectInputTextOnClick="true" SpinButtons-ClientVisible="false" NullText="0" CssClass="form-control altura-textbox" runat="server" >
                                            <ValidationSettings ValidationGroup="transferencia" Display="Dynamic" ErrorDisplayMode="ImageWithText" SetFocusOnError="true" ErrorTextPosition="Bottom">
                                                <RequiredField IsRequired="true" ErrorText="Campo Obligatorio." />
                                            </ValidationSettings>
                                        </dx:ASPxSpinEdit>
                                    </div>
                                </div>

                                <div class="row mt-3">
                                    <label class="col-lg-2 col-form-label fw-semibold"><span class="text-danger">•</span> Importe:</label>
                                    <div class="col-lg-2 p-0">
                                        <dx:ASPxSpinEdit ID="seTransferImporte" ClientInstanceName="seTransferImporte" SelectInputTextOnClick="true" SpinButtons-ClientVisible="false" NullText="0.00" CssClass="form-control altura-textbox" DisplayFormatString="${0:N2}" Width="125px" DecimalPlaces="2" runat="server" >
                                            <ValidationSettings ValidationGroup="transferencia" Display="Dynamic" ErrorDisplayMode="ImageWithText" SetFocusOnError="true" ErrorTextPosition="Bottom">
                                                <RequiredField IsRequired="true" ErrorText="Campo Obligatorio." />
                                            </ValidationSettings>
                                        </dx:ASPxSpinEdit>
                                    </div>
                                </div>


                                <div class="d-flex justify-content-between align-items-center mt-4">
                                    <p class="fw-medium text-danger mb-0" style="font-size:11px">(<span class="fs-5">•</span>) Campos Obligatorios</p>

                                    <dx:ASPxButton ID="BtnTransferAgregar" runat="server"  CssClass="btn btn-secondary fw-semibold px-0 py-1" ClientInstanceName="btn1" AutoPostBack="False" Text="Agregar">
                                     <ClientSideEvents Click="function(s,e) { ValidateTransferenciaOnClick(s,e); }" />
                                    </dx:ASPxButton>
                                    
                                   <%-- <dx:ASPxButton ID="BtnTransferModificar" runat="server" OnClick="btnTransferModificar_Click" CssClass="btn text-white fw-semibold px-0 py-1 ms-3" BackColor="Brown" ClientInstanceName="btn1" AutoPostBack="False" Text="Modificar">
                                        <ClientSideEvents Click="function(s,e){ LoadingPanel.Show(); panel.PerformCallback(); }" />
                                    </dx:ASPxButton>--%>
                                </div>
                                

                                  <div class="row mt-5 align-content-center" style="margin-top: 10px; margin-left:10px;">
                                    <dx:ASPxGridView ID="GrVTransf" OnDataBinding="GrVTransf_DataBinding" runat="server" ClientInstanceName="GrVTransf"  KeyFieldName="NroTransferencia" OnRowDeleting="GrVTransf_RowDeleting">
                                        <ClientSideEvents  />
                                       <Columns>
                                                                                    
                                           <dx:GridViewDataDateColumn FieldName="Fecha" caption="Fecha" />
                                           <dx:GridViewDataTextColumn FieldName="NroTransferencia" caption="Número" />
                                           <dx:GridViewDataTextColumn FieldName="Importe" caption="Importe" />

                                           <dx:GridViewDataColumn Caption="" VisibleIndex="7" Settings-AllowEllipsisInText="False" Width="110">
                                               <DataItemTemplate>
                                                <%--<dx:ASPxButton runat="server" ID="btnEditar" RenderMode="Link" Text="Editar"  OnClick="btnEditar_Click"
                                                    CommandArgument='<%# Eval("Id" )%>' AutoPostBack="false">
                                                       <ClientSideEvents Click="function (s,e){MostrarLoading();}" />
                                                </dx:ASPxButton>--%>
                                              <%--  <dx:ASPxButton runat="server" ID="BtnEliminar" CssClass="btn btn-secondary fw-semibold px-0 py-1" ClientInstanceName="btn1" AutoPostBack="False" Text="Eliminar"  >
                                                        <ClientSideEvents Click="function(s,e){e.processOnServer ==confirm('¿Esta seguro que desea eliminar?'); ValidateTransferenciaEliminarOnClick(s,e); }" />
                                                </dx:ASPxButton>--%>
                                               <a id="DeleteButtonTransf" href="javascript:OnDeleteTransfClick('<%# Container.KeyValue.ToString()%>', <%# Container.VisibleIndex.ToString()%>);"> Eliminar</a>
                                               </DataItemTemplate>
                                           </dx:GridViewDataColumn>

                                       </Columns>
                      
                                    </dx:ASPxGridView>

                                </div>

                            </div>
                        </div>
                    </div>
                    
                </div>

                <!-- Retenciones container -->

                <div class="tab-pane fade" id="retencion">                       

                    <div class="card">
                        <%--<div class="card-header">
                            <h6 class="mb-0 fw-bold fs-5">Retenciones</h6>
                        </div>--%>

                        <fieldset><legend class="col-lg-12 border-secondary border-bottom border-width-2"></legend></fieldset>

                        <div class="card-body">
                            <div action="#">
                                <div class="row mb-1">
                                    <label class="col-lg-2 col-form-label fw-semibold"><span class="text-danger">•</span> Retenciones:</label>
                                    <div class="col-lg-10 p-0">
                                        <dx:ASPxComboBox ID="Drop_Retenciones" ClientInstanceName="dropRetenciones" runat="server" DropDownStyle="DropDownList" ValueType="System.Int32" NullText="[Seleccione Retención]" TextFormatString="{0} ({1})" Width="100%" CssClass="form-control py-0" IncrementalFilteringMode="Contains" Height=39.6px>
                                            <ValidationSettings ValidationGroup="retencion" Display="Dynamic" ErrorDisplayMode="ImageWithText" ErrorTextPosition="Bottom" SetFocusOnError="true">
                                                <RequiredField IsRequired="true" ErrorText="Campo Obligatorio." />
                                            </ValidationSettings>
                                        </dx:ASPxComboBox> 
                                    </div>
                                </div>

                                <div class="row mb-1">
                                    <label class="col-lg-2 col-form-label fw-semibold"><span class="text-danger">•</span> Fecha:</label>
                                    <div class="col-lg-2 p-0">
                                        <dx:ASPxDateEdit ID="dateRetenFecha" ClientInstanceName="dateRetenFecha" CssClass="form-control px-0" Width="125px" runat="server" EditFormat="Custom" Height=39.6px>
                                            <TimeSectionProperties>
                                                <TimeEditProperties EditFormatString="hh:mm tt" />
                                            </TimeSectionProperties>
                                            <CalendarProperties>
                                                <FastNavProperties DisplayMode="Inline" />
                                            </CalendarProperties>
                                            <ValidationSettings ValidationGroup="retencion" Display="Dynamic" ErrorDisplayMode="ImageWithText" ErrorTextPosition="Bottom" SetFocusOnError="true">
                                                <RequiredField IsRequired="true" ErrorText="Campo Obligatorio." />
                                            </ValidationSettings>
                                        </dx:ASPxDateEdit>
                                    </div>

                                    <div class="col-lg-1"></div>

                                    <label class="col-lg-2 col-form-label ps-4 fw-semibold"><span class="text-danger">•</span> Número:</label>
                                    <div class="col-lg-2 p-0">
                                        <dx:ASPxSpinEdit ID="seNumeroReten" ClientInstanceName="seNumeroReten" SelectInputTextOnClick="true" SpinButtons-ClientVisible="false" NullText="0" CssClass="form-control altura-textbox" runat="server" >
                                            <ValidationSettings ValidationGroup="retencion" Display="Dynamic" ErrorDisplayMode="ImageWithText" SetFocusOnError="true" ErrorTextPosition="Bottom">
                                                <RequiredField IsRequired="true" ErrorText="Campo Obligatorio." />
                                            </ValidationSettings>
                                        </dx:ASPxSpinEdit>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <label class="col-lg-2 col-form-label fw-semibold"><span class="text-danger">•</span> Importe:</label>
                                    <div class="col-lg-2 p-0">
                                        <dx:ASPxSpinEdit ID="seRetenImporte" ClientInstanceName="seRetenImporte" SelectInputTextOnClick="true" SpinButtons-ClientVisible="false" NullText="0.00" CssClass="form-control altura-textbox" DisplayFormatString="${0:N2}" Width="125px" DecimalPlaces="2" runat="server" >
                                            <ValidationSettings ValidationGroup="retencion" Display="Dynamic" ErrorDisplayMode="ImageWithText" SetFocusOnError="true" ErrorTextPosition="Bottom">
                                                <RequiredField IsRequired="true" ErrorText="Campo Obligatorio." />
                                            </ValidationSettings>
                                        </dx:ASPxSpinEdit>
                                    </div>
                                </div>


                                <div class="d-flex justify-content-between align-items-center mt-4">
                                    <p class="fw-medium text-danger mb-0" style="font-size:11px">(<span class="fs-5">•</span>) Campos Obligatorios</p>

                                    <dx:ASPxButton ID="BtnRetenAgregar" runat="server"  CssClass="btn btn-secondary fw-semibold px-0 py-1" ClientInstanceName="btn1" AutoPostBack="False" Text="Agregar">
                                       <ClientSideEvents Click="function(s,e) { ValidateRetencionOnClick(s,e); }" />
                                    </dx:ASPxButton>
                                    
                                  <%--  <dx:ASPxButton ID="ASPxBtnRetenEliminar" runat="server" OnClick="btnRetenEliminar_Click" CssClass="btn text-white fw-semibold px-0 py-1 ms-3" BackColor="Brown" ClientInstanceName="btn1" AutoPostBack="False" Text="Eliminar">
                                        <ClientSideEvents Click="function(s,e){ LoadingPanel.Show(); panel.PerformCallback(); }" />
                                    </dx:ASPxButton>--%>
                                </div>
                                

                                 <div class="row mt-5 align-content-center" style="margin-top: 10px; margin-left:10px;">
                                     <dx:ASPxGridView ID="GrVRetencion"  OnDataBinding="GrVRetencion_DataBinding" runat="server" ClientInstanceName="GrVRetencion"  KeyFieldName="Numero" OnRowDeleting="GrVRetencion_RowDeleting"  >
                                       <Columns>
                                                                                    
                                            <dx:GridViewDataTextColumn FieldName="Numero" caption="Número"  />
                                            <dx:GridViewDataDateColumn FieldName="Fecha" caption="Fecha" />
                                           <dx:GridViewDataTextColumn FieldName="Importe" caption="Importe" />
                                           
                                            <dx:GridViewDataColumn Caption="" VisibleIndex="7" Settings-AllowEllipsisInText="False" Width="110">
                                         <DataItemTemplate>
                                        <%--<dx:ASPxButton runat="server" ID="btnEditar" RenderMode="Link" Text="Editar"  OnClick="btnEditar_Click"
                                            CommandArgument='<%# Eval("Id" )%>' AutoPostBack="false">
                                               <ClientSideEvents Click="function (s,e){MostrarLoading();}" />
                                        </dx:ASPxButton>--%>
                                       <%-- <dx:ASPxButton runat="server" ID="BtnEliminarRete" CssClass="btn btn-secondary fw-semibold px-0 py-1" ClientInstanceName="btn1" AutoPostBack="False" Text="Eliminar" >
                                                <ClientSideEvents Click="function(s,e){e.processOnServer ==confirm('¿Esta seguro que desea eliminar?');ValidateRetencionEliminarOnClick(s,e);}" />
                                        </dx:ASPxButton>--%>
                                     <a id="DeleteButtonReten" href="javascript:OnDeleteReteClick('<%# Container.KeyValue.ToString()%>', <%# Container.VisibleIndex.ToString()%>);">Eliminar</a>  
                                       </DataItemTemplate>
                                </dx:GridViewDataColumn>
                                            
                                                                                                                              
                                       </Columns>
                                    </dx:ASPxGridView>

                                </div>
                                
                            </div>
                        </div>
                    </div>
                    
                </div>

                <!-- Tarjeta container -->
                <div class="tab-pane fade ocultar-tarjeta" id="tarjeta">

                    <div class="card">
                        <%--<div class="card-header">
                            <h6 class="mb-0 fw-bold fs-5">Tarjeta</h6>
                        </div>--%>

                        <fieldset><legend class="col-lg-12 border-secondary border-bottom border-width-2"></legend></fieldset>

                        <div class="card-body">
                            <div action="#">
                                <div class="row mb-1 gx-2">
                                    <label class="col-lg-2 col-form-label fw-semibold"><span class="text-danger">•</span> Tarjeta:</label>
                                    <div class="col-lg-10 p-0">                                        
                                        <dx:ASPxComboBox ID="Drop_Tarjeta" ClientInstanceName="dropTarjeta" runat="server" DropDownStyle="DropDownList" ValueType="System.Int32" NullText="[Seleccione Tarjeta]" TextFormatString="{0} ({1})" Width="100%" CssClass="form-control py-0" IncrementalFilteringMode="Contains" Height=39.6px>
                                            <ValidationSettings ValidationGroup="tarjeta" Display="Dynamic" ErrorDisplayMode="ImageWithText" ErrorTextPosition="Bottom" SetFocusOnError="true">
                                                <RequiredField IsRequired="true" ErrorText="Campo Obligatorio." />
                                            </ValidationSettings>
                                        </dx:ASPxComboBox>   
                                        
                                    </div>
                                </div>

                                <div class="row mb-1 gx-2">
                                    <label class="col-lg-2 col-form-label fw-semibold"><span class="text-danger">•</span> Titular:</label>
                                    <div class="col-lg-10 p-0">
                                        <dx:ASPxTextBox ID="txtTitular" ClientInstanceName="txtTitular" CssClass="form-control altura-textbox" NullText="Nombre y Apellido del Titular" AutoCompleteType="Disabled" Width="100%" runat="server">
                                            <ValidationSettings ValidationGroup="tarjeta" Display="Dynamic" ErrorDisplayMode="ImageWithText" ErrorTextPosition="Bottom" SetFocusOnError="true">
                                                <RequiredField IsRequired="true" ErrorText="Campo Obligatorio." />
                                            </ValidationSettings>
                                        </dx:ASPxTextBox>
                                    </div>
                                </div>

                                <div class="row mb-1 gx-2">
                                    <label class="col-lg-2 col-form-label fw-semibold"><span class="text-danger">•</span> Nro Cupón:</label>
                                    <div class="col-lg-2 p-0">
                                        <dx:ASPxTextBox ID="txtNroCupon" ClientInstanceName="txtNroCupon" CssClass="form-control altura-textbox" Width="125px" NullText="0" AutoCompleteType="Disabled" runat="server">
                                            <ValidationSettings ValidationGroup="tarjeta" Display="Dynamic" ErrorDisplayMode="ImageWithText" ErrorTextPosition="Bottom" SetFocusOnError="true">
                                                <RequiredField IsRequired="true" ErrorText="Campo Obligatorio." />
                                            </ValidationSettings>
                                        </dx:ASPxTextBox>
                                    </div>

                                    <label class="col-lg-2 col-form-label fw-semibold ps-4">Nro Lote:</label>
                                    <div class="col-lg-2 p-0">
                                        <dx:ASPxTextBox ID="txtNroLote" ClientInstanceName="txtNroLote" CssClass="form-control altura-textbox" AutoCompleteType="Disabled" runat="server"></dx:ASPxTextBox>
                                    </div>

                                    <label class="col-lg-2 col-form-label fw-semibold ps-3"><span class="text-danger">•</span> Nro Autoriz.:</label>
                                    <div class="col-lg-2 p-0">
                                        <dx:ASPxTextBox ID="txtNroAutorizacion" ClientInstanceName="txtNroAutorizacion" CssClass="form-control altura-textbox" AutoCompleteType="Disabled" runat="server">
                                            <ValidationSettings ValidationGroup="tarjeta" Display="Dynamic" ErrorDisplayMode="ImageWithText" ErrorTextPosition="Bottom" SetFocusOnError="true">
                                                <RequiredField IsRequired="true" ErrorText="Campo Oblig." />
                                            </ValidationSettings>
                                        </dx:ASPxTextBox>
                                    </div>
                                </div>

                                <div class="row mb-1 p-0 gx-2">
                                    <label class="col-lg-2 col-form-label fw-semibold"><span class="invisible">•</span> Pago:</label>
                                    <div class="col-lg-2 p-0">
                                        <dx:ASPxTextBox ID="txtPago" ClientInstanceName="txtPago" CssClass="form-control altura-textbox" Width="125px" NullText="0.00" AutoCompleteType="Disabled" runat="server"></dx:ASPxTextBox>
                                    </div>

                                    <label class="col-lg-2 col-form-label ps-5 fw-semibold"><span class="text-danger">•</span>Cuotas</label>
                                    <div class="col-lg-1 p-0 pe-2">
                                        <dx:ASPxSpinEdit ID="seCuotas" ClientInstanceName="seCuotas" SelectInputTextOnClick="true" Width="50px" SpinButtons-ClientVisible="false" NullText="0" CssClass="form-control altura-textbox" runat="server" >
                                            <ValidationSettings ValidationGroup="tarjeta" Display="Dynamic" ErrorDisplayMode="ImageWithText" SetFocusOnError="true" ErrorTextPosition="Bottom">
                                                <RequiredField IsRequired="true" ErrorText="Campo Obligatorio." />
                                            </ValidationSettings>
                                        </dx:ASPxSpinEdit>
                                    </div>

                                    <label class="col-lg-1 col-form-label ps-3 pe-0 fw-semibold">Coef.</label>
                                    <div class="col-lg-1 p-0 ps-2">
                                        <dx:ASPxSpinEdit ID="seCoeficiente" ClientInstanceName="seCoeficiente" SelectInputTextOnClick="true" SpinButtons-ClientVisible="false" NullText="0" CssClass="form-control altura-textbox" runat="server" >
                                        </dx:ASPxSpinEdit>
                                    </div>

                                    <label class="col-lg-2 col-form-label ps-5 fw-semibold">Interes</label>
                                    <div class="col-lg-1 p-0 ">
                                        <dx:ASPxSpinEdit ID="seInter" ClientInstanceName="seInter" SelectInputTextOnClick="true" SpinButtons-ClientVisible="false" NullText="0" CssClass="form-control altura-textbox" DisplayFormatString="%{0:N2}" DecimalPlaces="1" runat="server" >
                                        </dx:ASPxSpinEdit>
                                    </div>

                                </div>

                                <div class="row mt-3 gx-2">
                                    <label class="col-lg-2 col-form-label fw-semibold pe-0"><span class="text-danger">•</span> Importe Cupón:</label>
                                    <div class="col-lg-2 p-0">
                                        <dx:ASPxSpinEdit ID="seImporteCupon" ClientInstanceName="seImporteCupon" SelectInputTextOnClick="true" SpinButtons-ClientVisible="false" NullText="0.00" CssClass="form-control altura-textbox" DisplayFormatString="${0:N2}" Width="125px" DecimalPlaces="2" runat="server" >
                                            <ValidationSettings ValidationGroup="tarjeta" Display="Dynamic" ErrorDisplayMode="ImageWithText" SetFocusOnError="true" ErrorTextPosition="Bottom">
                                                <RequiredField IsRequired="true" ErrorText="Campo Obligatorio." />
                                            </ValidationSettings>
                                        </dx:ASPxSpinEdit>
                                    </div>
                                </div>


                                <div class="d-flex justify-content-between align-items-center mt-4">
                                    <p class="fw-medium text-danger mb-0" style="font-size:11px">(<span class="fs-5">•</span>) Campos Obligatorios</p>

                                    <dx:ASPxButton ID="BtnAgregarTarjeta" runat="server" CssClass="btn btn-secondary fw-semibold px-0 py-1" ClientInstanceName="btn1" AutoPostBack="False" Text="Agregar">
                                        <ClientSideEvents Click="function(s,e) { ValidateTarjetaOnClick(s,e); }" />
                                    </dx:ASPxButton>
                                    
                                   <%-- <dx:ASPxButton ID="ASPxBtnEliminarTarjeta" runat="server" OnClick="btnEliminarTarjeta_Click" CssClass="btn text-white fw-semibold px-0 py-1 ms-3" BackColor="Brown" ClientInstanceName="btn1" AutoPostBack="False" Text="Eliminar">
                                        <ClientSideEvents Click="function(s,e){ LoadingPanel.Show(); panel.PerformCallback(); }" />
                                    </dx:ASPxButton>--%>                                    
                                </div>
                                

                                 <div class="row mt-5 align-content-center" style="margin-top: 10px; margin-left:10px;">
                                    <dx:ASPxGridView ID="GrVTarjeta"  OnDataBinding="GrVTarjeta_DataBinding" runat="server" ClientInstanceName="GrVTarjeta" KeyFieldName="CodCupon" OnRowDeleting="GrVTarjeta_RowDeleting" visible="true" >
                                        <Columns>
                                           
                                            <dx:GridViewDataTextColumn FieldName="CodCupon" caption="Nro Cupón"  />
                                            <dx:GridViewDataTextColumn FieldName="CodAutorizacion" caption="Nro Autoriz"  />                                            
                                            <dx:GridViewDataTextColumn FieldName="CantCuotas" caption="Cuotas" />
                                            <dx:GridViewDataTextColumn FieldName="Importe" caption= "Importe Cupón" />

                                            <dx:GridViewDataColumn Caption="" VisibleIndex="7" Settings-AllowEllipsisInText="False" Width="110">
                                                <DataItemTemplate>
                                                <%--<dx:ASPxButton runat="server" ID="btnEditar" RenderMode="Link" Text="Editar"  OnClick="btnEditar_Click"
                                                    CommandArgument='<%# Eval("Id" )%>' AutoPostBack="false">
                                                       <ClientSideEvents Click="function (s,e){MostrarLoading();}" />
                                                    </dx:ASPxButton>--%>
                                                <%--<dx:ASPxButton runat="server" ID="BtnTrajeta" RenderMode="Link" CssClass="btn btn-secondary fw-semibold px-0 py-1" ClientInstanceName="btn1" AutoPostBack="False" Text="Eliminar" >
                                                        <ClientSideEvents Click="function(s,e){e.processOnServer ==confirm('¿Esta seguro que desea eliminar?'); ValidateTarjetaEliminarOnClick(s,e); }" />
                                                    </dx:ASPxButton>--%>
                                                    <a id="DeleteButtonTarj" href="javascript:OnDeleteTarjClick('<%# Container.KeyValue.ToString()%>', <%# Container.VisibleIndex.ToString()%>);">Eliminar</a>
                                                </DataItemTemplate>
                                            </dx:GridViewDataColumn>
                                           
                                        </Columns>
                                    </dx:ASPxGridView>
                                </div>
                                
                            </div>
                        </div>
                    </div>                                                                             
                </div>               
            </div>
        </div>


        <!-- Totales container -->

        <div class="col-lg-5"">    
            <div class="navbar navbar-expand-xl bg-secondary navbar-dark rounded-top mt-1">
                <div class="container-fluid">                           
                    <div class="nav-item dropdown">
                        <h5 class="mt-2 mb-1 fs-5" data-bs-toggle="tab"">Totales</h5>
                    </div>                        
                </div>
            </div>

            <!-- Card totales -->
            <dx:ASPxCallbackPanel runat="server" ID="CallbackPanelTotales"   ClientInstanceName="CallbackPanelTotales">
                
                <PanelCollection>
                    <dx:PanelContent ID="PanelContent3" runat="server">
            <div class="tab-content border border-1 rounded-bottom p-1">

                <div class="card">
                    <div class="card-body">
                        <div action="#">

                            <%--<div class="row mb-1">
								<label class="col-lg-3 col-form-label fw-semibold">Paga:</label>
								<div class="col-lg-7">
                                    <dx:ASPxTextBox ID="txtPaga" CssClass="form-control altura-textbox" NullText="0.00" AutoCompleteType="Disabled" runat="server"></dx:ASPxTextBox>
								</div>
							</div>

							<div class="row mb-1">
								<label class="col-lg-3 col-form-label fw-semibold">Recargo/IVA:</label>
								<div class="col-lg-4">
                                    <dx:ASPxTextBox ID="txtRecargo" CssClass="form-control altura-textbox" NullText="0.00" AutoCompleteType="Disabled" runat="server"></dx:ASPxTextBox>
                                </div>
                                <div class="col-lg-3">
                                    <dx:ASPxTextBox ID="TxtRecIVA" CssClass="form-control altura-textbox" NullText="0.00" AutoCompleteType="Disabled" runat="server"></dx:ASPxTextBox>
								</div>
							</div>

                            <div class="row mb-1">
								<label class="col-lg-3 col-form-label fw-semibold">Int. Tarjeta/IVA:</label>
								<div class="col-lg-4">
                                    <dx:ASPxTextBox ID="txtIntTarjeta" CssClass="form-control altura-textbox" NullText="0.00" AutoCompleteType="Disabled" runat="server"></dx:ASPxTextBox>
                                </div>
                                <div class="col-lg-3">
                                    <dx:ASPxTextBox ID="txtIntTarjetaIVA" CssClass="form-control altura-textbox" NullText="0.00" AutoCompleteType="Disabled" runat="server"></dx:ASPxTextBox>
								</div>
							</div>

                            <div class="row mb-1">
								<label class="col-lg-3 col-form-label fw-semibold">Int.Cheque/IVA:</label>
								<div class="col-lg-4">
                                    <dx:ASPxTextBox ID="txtIntCheque" CssClass="form-control altura-textbox" NullText="0.00" AutoCompleteType="Disabled" runat="server"></dx:ASPxTextBox>
                                </div>
                                <div class="col-lg-3">
                                    <dx:ASPxTextBox ID="txtIntChequeIVA" CssClass="form-control altura-textbox" NullText="0.00" AutoCompleteType="Disabled" runat="server"></dx:ASPxTextBox>
								</div>
							</div>

                            <div class="row mb-1">
								<label class="col-lg-3 col-form-label fw-bold">Total Deuda:</label>
								<div class="col-lg-7">
                                    <dx:ASPxTextBox ID="txtTotalDeuda" CssClass="form-control altura-textbox border border-danger border-width-2" NullText="0.00" AutoCompleteType="Disabled" runat="server"></dx:ASPxTextBox>
								</div>
							</div>


                            <fieldset><legend class="col-lg-10 border-danger border-bottom border-width-2"></legend></fieldset>--%>


                            <div class="row mb-1">
								<label class="col-lg-3 col-form-label fw-semibold">Efectivo:</label>
								<div class="col-lg-7">
                                    <dx:ASPxSpinEdit ID="seTotalEfectivo" SpinButtons-ClientVisible="false" ReadOnly="true" NullText="0.00" CssClass="form-control altura-textbox" DisplayFormatString="${0:N2}" DecimalPlaces="2" runat="server" >
                                    </dx:ASPxSpinEdit>
								</div>
							</div>

                            <div class="row mb-1">
								<label class="col-lg-3 col-form-label fw-semibold">Cheque:</label>
								<div class="col-lg-7">
                                    <dx:ASPxSpinEdit ID="seTotalCheque" SpinButtons-ClientVisible="false" ReadOnly="true" NullText="0.00" CssClass="form-control altura-textbox" DisplayFormatString="${0:N2}" DecimalPlaces="2" runat="server" >
                                    </dx:ASPxSpinEdit>
								</div>
							</div>

                            <div class="row mb-1">
								<label class="col-lg-3 col-form-label fw-semibold">Transferencia:</label>
								<div class="col-lg-7">
                                    <dx:ASPxSpinEdit ID="seTotalTransfer" SpinButtons-ClientVisible="false" ReadOnly="true" NullText="0.00" CssClass="form-control altura-textbox" DisplayFormatString="${0:N2}" DecimalPlaces="2" runat="server" >
                                    </dx:ASPxSpinEdit>
								</div>
							</div>

                            <div class="row mb-1">
								<label class="col-lg-3 col-form-label fw-semibold">Retención:</label>
								<div class="col-lg-7">
                                    <dx:ASPxSpinEdit ID="seTotalReten" SpinButtons-ClientVisible="false" ReadOnly="true" NullText="0.00" CssClass="form-control altura-textbox" DisplayFormatString="${0:N2}" DecimalPlaces="2" runat="server" >
                                    </dx:ASPxSpinEdit>
								</div>
							</div>

                            <div class="row mb-1 tab-tarjeta">
								<label class="col-lg-3 col-form-label fw-semibold">Tarjeta:</label>
								<div class="col-lg-7">
                                    <dx:ASPxSpinEdit ID="seTotalTarjeta" SpinButtons-ClientVisible="false" ReadOnly="true" NullText="0.00" CssClass="form-control altura-textbox" DisplayFormatString="${0:N2}" DecimalPlaces="2" runat="server" >
                                    </dx:ASPxSpinEdit>
								</div>
							</div>

                            <fieldset><legend class="col-lg-10 border-danger border-bottom border-width-2 pb-2 mb-3"></legend></fieldset>

                            <div class="row mb-1">
								<label class="col-lg-3 col-form-label fw-bold">Pago Total:</label>
								<div class="col-lg-7">
                                    <dx:ASPxSpinEdit ID="sePagoTotal" ClientInstanceName="sePagoTotal" SpinButtons-ClientVisible="false" ReadOnly="true" NullText="0.00" CssClass="form-control altura-textbox" DisplayFormatString="${0:N2}" DecimalPlaces="2" runat="server" >
                                    </dx:ASPxSpinEdit>
								</div>
							</div>


                            <div class="row mb-1 mt-2">
								<label id="Vuelto" class="col-lg-3 col-form-label fw-bold">Vuelto:</label>
								<div class="col-lg-7">
                                    <dx:ASPxSpinEdit ID="seVuelto" SpinButtons-ClientVisible="false" ReadOnly="true" NullText="0.00" CssClass="form-control altura-textbox" DisplayFormatString="${0:N2}" DecimalPlaces="2" runat="server" >
                                    </dx:ASPxSpinEdit>
								</div>

                                <label class="col-lg-3 col-form-label fw-bold pe-0">Importe a favor:</label>
								<div class="col-lg-1 mt-2">
                                    <asp:CheckBox ID="CheckBoxImporteFavor" runat="server" />
								</div>


                                                               
							</div>

                        </div>
                    </div>
                </div>

            </div>
</dx:PanelContent>
                    </PanelCollection>
                </dx:ASPxCallbackPanel>
            <!-- Botones fuera de Card totales -->

            <div>

                <div class="form-group d-flex justify-content-center mt-2">

                    <div class="d-flex justify-content-end align-items-center mt-4">
                        <a href="#" id="confirmarPagoTotal"></a>
                        <dx:ASPxButton ID="btnConfirmarPagoTotal" CssClass="btn text-white fw-semibold px-0 py-1" BackColor="Brown" runat="server"
                            ClientInstanceName="btn1" AutoPostBack="False" OnClick="btnConfirmarPagoTotal_Click" Text="Confirmar">
                            <ClientSideEvents Click="function(s,e){ CallbackPanelTotales.PerformCallback(); LoadingPanel.Show();  }" />
                            
                        </dx:ASPxButton>                        
                        <dx:ASPxButton ID="btnCancelarTotal" CssClass="btn btn-secondary fw-semibold px-0 py-1 ms-3" runat="server" 
                            ClientInstanceName="btnCancelarTotal" AutoPostBack="False" Text="Cancelar">
                            <ClientSideEvents Click="function(s,e){ LoadingPanel.Show(); RedireccionarAPaginaPrincipal(); }" />
                        </dx:ASPxButton>                        
                        <dx:ASPxButton ID="btnVerCtaCte" CssClass="btn btn-secondary fw-semibold px-0 py-1 ms-3" runat="server" ClientInstanceName="btnVerCtaCte" AutoPostBack="False" Text="Ver Cta/Cte">
                            <ClientSideEvents Click="function(s,e){ LoadingPanel.Show(); panel.PerformCallback(); }" />
                        </dx:ASPxButton> 
                    </div>
                </div>
                <div class="row d-flex justify-content-center mt-3 mb-5">
					<label class="col-lg-4 text-center col-form-label fw-bold">Saldo sin Aplicar:</label>
					<div class="col-lg-4">
                        <dx:ASPxSpinEdit ID="seSaldoSinAplicar" SpinButtons-ClientVisible="false" ReadOnly="true" NullText="0.00" CssClass="form-control altura-textbox" DisplayFormatString="${0:N2}" DecimalPlaces="2" runat="server" >
                        </dx:ASPxSpinEdit>
					</div>
				</div>
            </div>
        </div>        
    </div>
        

    <dx:ASPxCallback ID="AgregarPago_Callback" runat="server" ClientInstanceName="AgregarPago_Callback" OnCallback="Callback_Agregar_Callback">
        <ClientSideEvents CallbackComplete="function(s,e) { 
            GrVCheques.PerformCallback();
            GrVTransf.PerformCallback();
            GrVRetencion.PerformCallback();
            GrVTarjeta.PerformCallback();
            
            CallbackPanelTotales.PerformCallback(); LoadingPanel.Hide(); }" />
      
    </dx:ASPxCallback>

        
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="footer" runat="server">
</asp:Content>
