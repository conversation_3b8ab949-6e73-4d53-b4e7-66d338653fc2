﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 20/10/2023 15:17:33
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class FuentesTiposRepository : NHibernateRepository<Domain.FuentesTipos>, IFuentesTiposRepository
    {
        public FuentesTiposRepository(ISession session) : base(session)
        {
        }

        public virtual IList<Domain.FuentesTipos> GetAll()
        {
            return session.CreateQuery(string.Format("from FuentesTipos")).List<Domain.FuentesTipos>();
        }

        public virtual Domain.FuentesTipos ExisteCodigo(string stringCodigo)
        {
            string hql = "from FuentesTipos where  upper(Codigo)=:stringCodigo  and Activo=true";
            return session.CreateQuery(hql)

          .SetParameter("stringCodigo", stringCodigo.ToUpper())
          .UniqueResult<Domain.FuentesTipos>();
        }
        public virtual bool ExisteNombre(string Descripcion)
        {
            string hql = "select count(*) from FuentesTipos where upper(Descripcion) = :Descripcion and Activo = true";
            long count = session.CreateQuery(hql)
                .SetParameter("Descripcion", Descripcion.ToUpper())
                .UniqueResult<long>();

            return count > 0;
        }

        public virtual Domain.FuentesTipos GetByKey(int _Id)
        {
            return session.Get<Domain.FuentesTipos>(_Id);
        }

        ICollection<FuentesTipos> IFuentesTiposRepository.GetAll()
        {
            throw new NotImplementedException();
        }
    }
}
