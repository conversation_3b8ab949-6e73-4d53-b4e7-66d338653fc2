<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="DomiciliosTipos" table="DomiciliosTipos">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Descripcion" type="String">
      <column name="Descripcion" not-null="false" length="250" sql-type="nvarchar" />
    </property>
    <property name="Georeferenciado" type="Boolean">
      <column name="Georeferenciado" not-null="false" sql-type="bit" />
    </property>
    <property name="Codigo" type="Int32">
      <column name="Codigo" not-null="false" precision="10" scale="0" sql-type="int" />
    </property>
    <set name="ContratoDomicilios" inverse="true" generic="true">
      <key>
        <column name="IdDomiciliosTipos" />
      </key>
      <one-to-many class="ContratoDomicilios" />
    </set>
  </class>
</hibernate-mapping>