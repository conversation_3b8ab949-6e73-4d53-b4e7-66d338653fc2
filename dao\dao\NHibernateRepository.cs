﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 12/5/2023 11:08:46
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Collections.Generic;
using NHibernate;
using Domain;

namespace DAO
{
    public partial class NHibernateRepository<T> : IRepository<T>
    {
        protected ISession session;

        public NHibernateRepository(ISession session)
        {

            if (session == null)
            {
                throw new ArgumentNullException("session");
            }
            this.session = session;
        }

        public virtual void Add(T entity)
        {

            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }

            if (!session.Transaction.IsActive)
            {
                using (ITransaction transaction = session.BeginTransaction())
				        {
                    session.SaveOrUpdate(entity);
                    transaction.Commit();
				        }
            }
            else
                session.SaveOrUpdate(entity);
        }

       
        public virtual void Remove(T entity)
        {

            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }

            if (!session.Transaction.IsActive)
            {
                using (ITransaction transaction = session.BeginTransaction())
				        {
                    session.Delete(entity);
                    transaction.Commit();
				        }
            }
            else
                session.Delete(entity);
        }
	}
}
