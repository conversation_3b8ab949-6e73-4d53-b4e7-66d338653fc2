﻿using Common.DDL;
using DAO;
using Domain;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Epas
{
    public class FuentesService
    {
        //Trae todo
        public static IList<Domain.Fuentes> GetAll()
        {
            IList<Domain.Fuentes> listaAux;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                listaAux = (IList<Domain.Fuentes>)new FuentesRepository(sess).GetAll();
                sess.Close();
                sess.Dispose();
            }
            return (listaAux);



        }

        public static List<ItemCombo> GetByFuentes(bool Activo) {
            IList<Fuentes> listaAux;
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                listaAux = (IList<Fuentes>) new FuentesRepository(sess).GetByFuentes(Activo);
                sess.Close();
                sess.Dispose();
            }

            ItemCombo item;
            ListaCombo lista = new ListaCombo();
            foreach(Fuentes p in listaAux) {

                item = new ItemCombo();
                item.ID = p.Id.ToString();
                item.Descripcion = p.Descripcion;
                lista.Add(item);
            }
            return (lista);
        }

        public static bool ExisteCodigo(string codigo)
        {

            Domain.Fuentes p;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {

                p = (Domain.Fuentes)new FuentesRepository(sess).ExisteCodigo(codigo);
                sess.Close();
                sess.Dispose();
                return p != null;

            }

        }

        public static bool ExisteNombre(string Descripcion)
        {
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                return new FuentesRepository(sess).ExisteNombre(Descripcion);
            }
        }


        //obtener por id
        public static Domain.Fuentes GetById(int id)
        {

            Domain.Fuentes p;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {

                p = (Domain.Fuentes)new FuentesRepository(sess).GetByKey(id);
                if(p!=null)
                {
                    NHibernateUtil.Initialize(p.NroDeFuente);
                    NHibernateUtil.Initialize(p.SubFuentes);

                }
                sess.Close();
                sess.Dispose();
                return p;
            }

        }

        //agregar y actualizar
        public static void SaveOrUpdate(Domain.Fuentes p)
        {

            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {

                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {

                    try
                    {

                        new FuentesRepository(sess).Add(p);

                        tx.Commit();
                    }
                    catch (Exception e)
                    {

                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {

                        sess.Close();
                        sess.Dispose();
                    }

                }

            }

        }

        //borrar
        public static void Delete(Domain.Fuentes p)
        {
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new FuentesRepository(sess).Remove(p);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }

                }

            }

        }

    }
}

