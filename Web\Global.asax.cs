﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Web;
using System.Web.Security;
using System.Web.SessionState;

namespace Web
{
    public class Global : System.Web.HttpApplication
    {
        public static string ApplicationPath = "";
        protected void Application_Start(object sender, EventArgs e)
        {
          
        }

        protected void Session_Start(object sender, EventArgs e)
        {
         
            CultureInfo myCIintl = Thread.CurrentThread.CurrentCulture;
            Application.Add("culture", myCIintl);

            CultureInfo nueva = new CultureInfo("es-AR");
            nueva.DateTimeFormat.ShortDatePattern = "dd/MM/yyyy";

            nueva.NumberFormat.CurrencyDecimalSeparator = ",";

            nueva.NumberFormat.CurrencyGroupSeparator = ".";

            nueva.NumberFormat.NumberDecimalSeparator = ",";

            nueva.NumberFormat.NumberGroupSeparator = ".";

            Thread.CurrentThread.CurrentUICulture = nueva;
            Thread.CurrentThread.CurrentCulture = nueva;


        }

        protected void Application_BeginRequest(object sender, EventArgs e)
        {

            if (HttpContext.Current.Request.ApplicationPath != "/")
            {

                ApplicationPath = HttpContext.Current.Request.ApplicationPath;
            }
        }
    }
}