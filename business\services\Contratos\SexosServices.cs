﻿using Common.DDL;
using DAO;
using Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Contratos {
    public class SexosServices {

        public static IList<Sexos> GetAll() {
            IList<Sexos> listaAux;
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                listaAux = (IList<Sexos>) new SexosRepository(sess).GetAll();
                sess.Close();
                sess.Dispose();
            }

            return (listaAux);
        }

        public static List<ItemCombo> GetBySexos(bool Activo) {
            IList<Sexos> listaAux;
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                listaAux = (IList<Sexos>) new SexosRepository(sess).GetBySexos(Activo);
                sess.Close();
                sess.Dispose();
            }

            ItemCombo item;
            ListaCombo lista = new ListaCombo();
            foreach(Sexos p in listaAux) {

                item = new ItemCombo();
                item.ID = p.Id.ToString();
                item.Descripcion = p.Descripcion;
                lista.Add(item);
            }
            return (lista);
        }

        public static Sexos getById(int id) {
            Sexos u;
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                u = new SexosRepository(sess).GetByKey(id);

                if(u != null) {
                    //NHibernateUtil.Initialize();

                }
                sess.Close();
                sess.Dispose();
                return u;
            }
        }

        public static void Delete(Domain.Sexos u) {
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                using(NHibernate.ITransaction tx = sess.BeginTransaction()) {
                    try {
                        new SexosRepository(sess).Remove(u);
                        tx.Commit();
                    } catch(Exception e) {
                        tx.Rollback();
                        throw e;
                    } finally {
                        sess.Close();
                        sess.Dispose();
                    }
                }

            }
        }

    }
}
