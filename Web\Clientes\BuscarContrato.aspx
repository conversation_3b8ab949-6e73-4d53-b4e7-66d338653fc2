﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="BuscarContrato.aspx.cs" Inherits="Web.Clientes.BuscarContrato" %>

<%@ Register Assembly="DevExpress.Web.v18.1" Namespace="DevExpress.Web" TagPrefix="dx" %>


<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">



</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

    <div class="page-header page-header-light shadow">
        <div class="page-header-content d-lg-flex border-top">
            <div class="d-flex">
                <div class="breadcrumb py-2">
                    <a href="<%= Web.Util.Helpers.GetApplicationPath() %>/Index.aspx" class="breadcrumb-item"><i class="icon-home2 mr-2"></i>Inicio</a>
                    <a href="<%= Web.Util.Helpers.GetApplicationPath() %>/Clientes/BuscarContrato.aspx" class="breadcrumb-item"><i class="icon-display me-2"></i>Solicitante</a>
                    <span class="breadcrumb-item active">
                        <asp:Literal ID="ltr_nombreBreadcrumb" runat="server">Buscar</asp:Literal>
                    </span>
                </div>
                <a href="#" class="header-elements-toggle text-default d-md-none"><i class="icon-more"></i></a>
            </div>
        </div>

        <div class="row gx-1" runat="server">

            <div class="col-lg-12">

                <div class="navbar navbar-expand-xl bg-secondary navbar-dark rounded-top mt-1">
                    <div class="container-fluid">                           
                        <div class="nav-item dropdown">
                            <h5 class="mt-2 mb-1 fs-5" data-bs-toggle="tab">Solicitante</h5>
                        </div>                        
                    </div>
                </div>

                <!-- Card Cliente -->

                <div class="tab-content border border-1 rounded-bottom p-1">

                    <div class="card">
                        <div class="row">
                            <div class="col-lg-8 card-body">
                                <div action="#" class="ms-3">

                                    <div class="row mb-1">
                                        <label class="col-lg-1 col-form-label fw-semibold btn-buscar">Buscar</label>
                                        <div class="col-lg-2 p-0">
                                            <dx:ASPxComboBox ID="cmbBuscarSolicitante" NullText="Seleccione Búsqueda" CssClass="form-control altura-textbox" runat="server" EnableCallbackMode="true" DropDownStyle="DropDown" >
                                                <Items>
                                                    <dx:ListEditItem Text="N° de Solicitante" Value="0" />
                                                    <dx:ListEditItem Text="DNI / CUIT" Value="1" />
                                                    <dx:ListEditItem Text="Nombre / Razon Social" Value="2" />
                                                </Items>
                                                <ValidationSettings ValidationGroup="buscarSolicitante" Display="Dynamic" ErrorDisplayMode="ImageWithText" ErrorTextPosition="Bottom" SetFocusOnError="true">
                                                    <RequiredField IsRequired="true" ErrorText="Campo Obligatorio" />
                                                </ValidationSettings>
                                            </dx:ASPxComboBox>
                                        </div>

                                        <div class="col-lg-2 pe-1 form-control-feedback form-control-feedback-start flex-grow-1">
                                            <dx:ASPxTextBox ID="txtBarraBusqueda" ClientInstanceName="txtBarraBusqueda" CssClass="form-control altura-textbox" NullText="Ingrese Nombre y/o Apellido" AutoCompleteType="Disabled" runat="server">
                                                <ValidationSettings ValidationGroup="buscarSolicitante" Display="Dynamic" ErrorDisplayMode="ImageWithText" ErrorTextPosition="Bottom" SetFocusOnError="true">
                                                </ValidationSettings>
                                            </dx:ASPxTextBox>
							            </div>

                                        <div class="col-lg-1 form-control-feedback form-control-feedback-start flex-grow-1 me-5">
                                            <dx:ASPxButton ID="Btn_BuscarSolicitante" runat="server" OnClick="Btn_BuscarSolicitante_Click" CssClass="btn btn-secondary px-0 py-1" ClientInstanceName="Btn_BuscarSolicitante" Text="🔎">
                                            </dx:ASPxButton>                                    
                                        </div>
                                    </div>                             

                                </div>
                            </div>
                            <div class="col-lg-4 card-body">

                                <div class="row">
                                    <div class="col-lg-1 form-control-feedback form-control-feedback-start flex-grow-1 d-flex justify-content-end me-5">
                                        <dx:ASPxHyperLink ID="ASPxHyperLink1" runat="server" CssClass="fw-medium rounded-2 text-white bg-info border border-secondary text-decoration-none p-2" Cursor="pointer" Text="Nuevo 📄" Font-Size="Small" Theme="Moderno" NavigateUrl="~/Clientes/AddEditContrato.aspx">
                                            <ClientSideEvents Click="function(s,e){MostrarLoading();}" />
                                        </dx:ASPxHyperLink>                                    
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-12 card-body">
                                <div class="row mt-2 align-content-center d-flex justify-content-center">
                                    <dx:ASPxGridView ID="GrVBuscarSolicitante" CssClass="fw-medium" OnDataBinding="GrVBuscarSolicitante_DataBinding" EnableRowsCache="false" EnableViewState="false" KeyFieldName="Id" Width="100%" ClientInstanceName="GrVBuscarSolicitante" runat="server">
                                        <SettingsPager PageSize="20" AlwaysShowPager="true"></SettingsPager>
                                        <Columns>

                                            <dx:GridViewDataColumn Caption="N° de </br> Solic." HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center" Width="6%" >
                                                <DataItemTemplate>
                                                    <dx:ASPxLabel ID="lblNroSolicitante" CssClass="fw-semibold" runat="server" Font-Size="Small" Theme="Moderno" Text='<%# String.Format( "{0}", Eval("NroContrato")) %>'>
                                                    </dx:ASPxLabel>
                                                </DataItemTemplate>
                                            </dx:GridViewDataColumn>

                                            <dx:GridViewDataColumn Caption="Nombre" HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center">
                                                <DataItemTemplate>
                                                    <dx:ASPxLabel ID="lblNombre" CssClass="fw-semibold" runat="server" Font-Size="Small" Theme="Moderno" Text='<%# String.Format( "{0}", Eval("Nombre")) %>'>
                                                    </dx:ASPxLabel>
                                                </DataItemTemplate>
                                            </dx:GridViewDataColumn>

                                            <dx:GridViewDataColumn Caption="Razón </br> Social" HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center">
                                                <DataItemTemplate>
                                                    <dx:ASPxLabel ID="lblRazonSocial" CssClass="fw-semibold" runat="server" Font-Size="Small" Theme="Moderno" Text='<%# String.Format( "{0}", Eval("RazonSocial")) %>'>
                                                    </dx:ASPxLabel>
                                                </DataItemTemplate>
                                            </dx:GridViewDataColumn>

                                            <dx:GridViewDataColumn Caption="Tipo y </br> N° de Doc." HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center">
                                                <DataItemTemplate>
                                                    <dx:ASPxLabel ID="lblTipoNroDoc" CssClass="fw-semibold" runat="server" Font-Size="Small" Theme="Moderno" Text='<%# String.Format( "{0} {1}", Eval("DocTipos.Abreviatura"), Eval("IdentificacionTributaria")) %>'>
                                                    </dx:ASPxLabel>
                                                </DataItemTemplate>
                                            </dx:GridViewDataColumn>

                                            <dx:GridViewDataColumn Caption="IVA" HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center">
                                                <DataItemTemplate>
                                                    <dx:ASPxLabel ID="lblIVA" CssClass="fw-semibold" runat="server" Font-Size="Small" Theme="Moderno" Text='<%# String.Format( "{0}", Eval("IVACondiciones.Descripcion")) %>'>
                                                    </dx:ASPxLabel>
                                                </DataItemTemplate>
                                            </dx:GridViewDataColumn>

                                            <dx:GridViewDataColumn Caption="Tipo y </br> N° de Tel." HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center">
                                                <DataItemTemplate>
                                                    <dx:ASPxLabel ID="lblTelefono" CssClass="fw-semibold" runat="server" Font-Size="Small" Theme="Moderno" Text='<%# String.Format( "{0} {1}", Eval("TelefonoTipo_IdTelefonoTipo.Tipo"), Eval("Telefono")) %>'>
                                                    </dx:ASPxLabel>
                                                </DataItemTemplate>
                                            </dx:GridViewDataColumn>

                                            <dx:GridViewDataColumn Caption="E-mail" HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center">
                                                <DataItemTemplate>
                                                    <dx:ASPxLabel ID="lblEmail" CssClass="fw-semibold" runat="server" Font-Size="Small" Theme="Moderno" Text='<%# String.Format( "{0}", Eval("Email")) %>'>
                                                    </dx:ASPxLabel>
                                                </DataItemTemplate>
                                            </dx:GridViewDataColumn>

                                            <dx:GridViewDataTextColumn Caption="Activo" HeaderStyle-HorizontalAlign="Center" Visible="true" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="true" CellStyle-HorizontalAlign="Center" Width="8%">
                                                <Settings AllowAutoFilter="false" />
                                                <DataItemTemplate>
                                                    <dx:ASPxImage ID="imgAlta" runat="server" Visible='<%# !Convert.ToBoolean(Eval("Baja")) %>' ShowLoadingImage="true" ImageUrl="~/App_Themes/Tema1/img/check.png" Width="30px"></dx:ASPxImage>
                                                    <dx:ASPxImage ID="imgBaja" runat="server" Visible='<%# Eval("Baja")  %>' ShowLoadingImage="true" ImageUrl="~/App_Themes/Tema1/img/no.png" Width="30px"></dx:ASPxImage>
                                                </DataItemTemplate>

                                                <DataItemTemplate>
                                                    <dx:ASPxImage ID="imgAlta" runat="server" Visible='<%# !Convert.ToBoolean(Eval("Baja")) %>' ShowLoadingImage="true" ImageUrl="~/App_Themes/Tema1/img/check.png" Width="30px"></dx:ASPxImage>
                                                    <dx:ASPxImage ID="imgBaja" runat="server" Visible='<%# Eval("Baja")  %>' ShowLoadingImage="true" ImageUrl="~/App_Themes/Tema1/img/no.png" Width="30px"></dx:ASPxImage>
                                                </DataItemTemplate>
                                            </dx:GridViewDataTextColumn>

                                            <dx:GridViewDataColumn Caption="Datos de <br/> Solicitante" HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center" Width="9%">
                                                <DataItemTemplate>
                                                    <dx:ASPxButton ID="btnEditar" runat="server" ClientInstanceName="btnEditar" RenderMode="Link" Text="Editar" OnClick="btnEditar_Click" CssClass="fw-semibold text-decoration-none text-secondary" Font-Size="Small" Theme="Moderno" CommandArgument='<%# Eval("Id")%>' AutoPostBack="false">
                                                        <ClientSideEvents Click="function (s,e){MostrarLoading();}" />
                                                    </dx:ASPxButton>
                                                </DataItemTemplate>
                                            </dx:GridViewDataColumn>

                                            <dx:GridViewDataColumn Caption="Protocolo" HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center" Width="9%">
                                                <DataItemTemplate>
                                                    <dx:ASPxButton ID="btnVerProtocolo" runat="server" ClientInstanceName="btnVerProtocolo" RenderMode="Link" Text="Ver Protoc." OnClick="btnVerProtocolo_Click" CssClass="fw-semibold text-decoration-none text-secondary" Font-Size="Small" Theme="Moderno" CommandArgument='<%# Eval("Id")%>' AutoPostBack="false">
                                                        <ClientSideEvents Click="function (s,e){MostrarLoading();}" />
                                                    </dx:ASPxButton>
                                                </DataItemTemplate>
                                            </dx:GridViewDataColumn>

                                            <dx:GridViewDataColumn Caption="Crear" HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center" Width="9%">
                                                <DataItemTemplate>
                                                    <dx:ASPxButton ID="btnNuevoProtocolo" runat="server" ClientInstanceName="btnNuevoProtocolo" RenderMode="Link" Text="Nuevo Protoc." OnClick="btnNuevoProtocolo_Click" CssClass="fw-semibold text-decoration-none text-secondary" Font-Size="Small" Theme="Moderno" CommandArgument='<%# Eval("Id")%>' AutoPostBack="false">
                                                        <ClientSideEvents Click="function (s,e){MostrarLoading();}" />
                                                    </dx:ASPxButton>
                                                </DataItemTemplate>
                                            </dx:GridViewDataColumn>

                                            <dx:GridViewDataColumn Caption="#" HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center" Width="8%">
                                                <DataItemTemplate>

                                                    <dx:ASPxButton ID="btnEliminar" runat="server" CssClass="fw-semibold text-secondary text-decoration-none" ClientInstanceName="btnEliminar" RenderMode="Link" Text="Eliminar" CausesValidation="false" OnClick="btnEliminar_Click"
                                                        CommandArgument='<%# Eval("Id")%>' AutoPostBack="false">
                                                        <ClientSideEvents Click="function(s,e){e.processOnServer =confirm('¿Esta seguro que desea eliminar?');}" />
                                                    </dx:ASPxButton>
                                                </DataItemTemplate>
                                            </dx:GridViewDataColumn>

                                        </Columns>

                                        <SettingsDataSecurity AllowInsert="false" />
                                        <EditFormLayoutProperties>
                                            <SettingsAdaptivity AdaptivityMode="SingleColumnWindowLimit" SwitchToSingleColumnAtWindowInnerWidth="700" />
                                        </EditFormLayoutProperties>
                                        <SettingsPopup>
                                            <EditForm Width="600">
                                                <SettingsAdaptivity Mode="OnWindowInnerWidth" SwitchAtWindowInnerWidth="768" />
                                            </EditForm>
                                        </SettingsPopup>
                                    </dx:ASPxGridView>

                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

</asp:Content>

<asp:Content ID="Content3" ContentPlaceHolderID="footer" runat="server">

</asp:Content>
