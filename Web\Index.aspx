﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" Async="true" CodeBehind="Index.aspx.cs" Inherits="Web.Index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>

<asp:Content ID="Content3" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Page content -->
    <div class="page-content pt-0">

        <!-- Main content -->
        <div class="content-wrapper">

            <!-- Content area -->
            <div class="content">

                <!-- Basic card -->
                <div class="card">
                    <div class="card-header header-elements-inline">
                        <h5 class="card-title">Bienvenido
                            <asp:Label runat="server" ID="lbl_usuario"></asp:Label>
                        </h5>
                        <div class="header-elements">
                            <div class="list-icons">
                                <a class="list-icons-item" data-action="collapse"></a>
                                <a class="list-icons-item" data-action="remove"></a>
                            </div>
                        </div>
                    </div>

                    <div>
                    </div>


                    <div class="card-body">

                        <h6 class="font-weight-semibold">Sistema de Gestión de Laboratorio</h6>
                        <p class="mb-3">Para consultas y/o sugerencias, escribir a <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    </div>
                </div>
                <!-- /basic card -->
                <!-- Quick stats boxes -->
            </div>
        </div>
    </div>

 
    <!-- Botonera -->
        

                

    <!-- /page content -->
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="footer" runat="server">
</asp:Content>
