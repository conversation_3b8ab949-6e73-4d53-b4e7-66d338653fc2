﻿using Business.Services.Epas;
using Business.Services.Provincias;
using DevExpress.Web;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web.EPAS.Lista
{
    public partial class TiposDeFuentes : System.Web.UI.Page
    {
        protected void Page_Load(object sender,EventArgs e)
        {
            try
            {

                CargarDatosEnGv_TipoFuentes();
                if (!IsPostBack)
                {

                }

            }
            catch (Exception ex)
            {
                string g = ex.Message;
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('" + g + "', 'error');", true);
            }
        }

        private void CargarDatosEnGv_TipoFuentes()
        {
            try
            {
                List<Domain.FuentesTipos > tipofuentes = (List<Domain.FuentesTipos>)
                Business.Services.Epas.FuentesTiposService.GetAll();
                gv_TipodeFuente.DataSource = tipofuentes;
                gv_TipodeFuente.DataBind();
            }
            catch (Exception ex)
            {
                string g = ex.Message;
            }
        }

        protected void btnEditar_Click(object sender, EventArgs e)
        {

            try
            {
                ASPxButton btn = (ASPxButton)sender;
                if (btn != null)
                {
                    GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer)btn.NamingContainer;
                    if (c != null)
                    {
                        var ind = DataBinder.Eval(c.DataItem, "Id");

                        if (ind != null)
                        {
                            Domain.FuentesTipos FT = FuentesTiposService.GetById(Convert.ToInt32(ind));

                            if(FT.Activo == false)
                            {
                                string message = string.Format("El Tipo de Fuente {0} no se puede editar ya que esta anulada.",FT.Descripcion);
                                ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('" + message + "')",true);

                            } else
                            {
                                Response.Redirect(Global.ApplicationPath + "/EPAS/AddEdit/AddEditTiposDeFuentes.aspx?id=" + Util.Helpers.Encrypt(ind.ToString()), false);
                            }

                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al tratar de editar un elemento, consulte al administrador del sistema', 'error');", true);
            }
        }

        protected void btnAnularActivar_Click(object sender, EventArgs e)
        {

            try
            {
                ASPxButton btn = (ASPxButton)sender;
                GridViewDataItemTemplateContainer f = (GridViewDataItemTemplateContainer)btn.NamingContainer;
                var ind = DataBinder.Eval(f.DataItem, "Id");

                Domain.FuentesTipos tipofuente = FuentesTiposService.GetById(Convert.ToInt32(ind));
                tipofuente.Activo = !tipofuente.Activo; 

                FuentesTiposService.SaveOrUpdate(tipofuente);
                CargarDatosEnGv_TipoFuentes();

            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al tratar de eliminar un elemento, consulte al administrador del sistema', 'error');", true);

            }
        }

    }
}