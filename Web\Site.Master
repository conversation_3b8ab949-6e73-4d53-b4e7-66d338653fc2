﻿<%@ Master Language="C#" AutoEventWireup="true" CodeBehind="Site.master.cs" Inherits="Web.Site" %>

<%@ Register Assembly="DevExpress.Web.v18.1" Namespace="DevExpress.Web" TagPrefix="dx" %>


<!DOCTYPE html>
<html lang="es">

<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">


    <link rel="icon" type="image/png" sizes="32x32" href="App_Themes/Tema1/img/favicon - EPAS (1).jpg">

    <!-- Global Stylesheets -->
    <link href="App_Themes/Tema1/assets/css/bootstrap.min.css" rel="stylesheet" />

    <link href="https://fonts.googleapis.com/css?family=Roboto:400,300,100,500,700,900" rel="stylesheet" type="text/css">
    <link href="App_Themes/Tema1/assets/css/icons/fontawesome/styles.min.css" rel="stylesheet" />
    <link href="App_Themes/Tema1/assets/css/icons/phosphor/styles.min.css" rel="stylesheet" />
    <link href="App_Themes/Tema1/assets/css/icons/icomoon/styles.min.css" rel="stylesheet" />
    <link href="App_Themes/Tema1/assets/css/all.min.css" rel="stylesheet" />
    <link href="App_Themes/Tema1/assets/css/AgregarCliente/AgregarCliente.css" rel="stylesheet" />
    <link href="App_Themes/Tema1/assets/css/SiteMaster.css" rel="stylesheet" />
        
    <!-- Carga de Pagos CSS files -->
    <link href="App_Themes/Tema1/assets/css/css_CargaPagos/CargaDePagos.css" rel="stylesheet" />

    <!-- Sweet Alerts CSS files -->
    <script src="/App_Themes/Tema1/sweetalerts/extra_sweetalert.js"></script>
    <script src="/App_Themes/Tema1/sweetalerts/sweet_alert.min.js"></script>
    <link href="App_Themes/Tema1/sweetalerts/sweetalert.min.css" rel="stylesheet" />

    <!-- Core JS files -->
    <script src="/App_Themes/Tema1/assets/js/jquery.min.js"></script>
    <script src="/App_Themes/Tema1/assets/js/bootstrap.bundle.min.js"></script>

    <!-- Theme JS files -->
    <script src="/App_Themes/Tema1/assets/js/js_CargaPagos/prism.min.js"></script>
    <script src="/App_Themes/Tema1/assets/js/app.js"></script>

    <!-- Sweet Alerts JS files -->

    <title>Si.Ge.Lab.</title>
    <asp:ContentPlaceHolder ID="head" runat="server">
    </asp:ContentPlaceHolder>

</head>
<body runat="server" id="body">
    <form id="form2" runat="server" enctype="multipart/form-data">
        <!-- Main navbar -->

        <div class="navbar nav-nav navbar-expand-md navbar-dark fw-semibold border-bottom" style="background-color:#247294; height:70px">
            <div class="navbar rounded mx-3">
                <a href="/Index.aspx" class="" onclick="MostrarLoading()">
                    <img class="w-56px" src="/App_Themes/Tema1/img/Navbar-Img_-_EPAS.jpg" alt="" />
                </a>
            </div>

            <div class="d-md-none">
                <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbar-mobile">
                    <i class="fa-regular fa-house"></i>
                </button>
            </div>

            <div class="navbar-collapse collapse order-2 order-xl-1" id="navbar-demo1-mobile">

                <ul id="menuDinamico" runat="server" clientidmode="Static" class="navbar-nav mt-1">
                </ul>

                <div class="navbar-brand flex-1 flex-lg-0 w-80px"></div>
                <div class="navbar-brand flex-1 flex-lg-0 w-80px"></div>

                <ul class="navbar-nav">
                    <li class="nav-item ps-5 ms-5">
                        <asp:LinkButton ID="lnk_salir" PostBackUrl="Logout.aspx" CssClass="navbar-nav-link btn-salir text-white fw-semibold ms-5 w-80px" runat="server">
                            <i class="icon-lock2"></i>Salir</asp:LinkButton>
                    </li>                   
                </ul>
            </div>
        </div>
        <!-- /main navbar -->



        <asp:ContentPlaceHolder ID="ContentPlaceHolder1" runat="server">

        </asp:ContentPlaceHolder>

      


        <!-- Footer -->
        <div class="navbar navbar-expand-lg vw-100" style="background-color:#247294; height:60px">
            <div class="text-center d-lg-none w-100">
                <button type="button" class="navbar-toggler dropdown-toggle" data-toggle="collapse" data-target="#navbar-footer">
                    <i class="icon-unfold mr-2"></i>                  
                </button>
            </div>

            <div class="navbar-collapse collapse d-flex justify-content-center" id="navbar-footer">
                <span class="text-white p-3"><a class="text-white fw-semibold" target="_blank" href="https://www.epas.gov.ar/">© EPAS</a> - <script>new Date().getFullYear()>2010&&document.write(new Date().getFullYear());</script> | Todos los Derechos Reservados.  
                </span>

                <asp:ContentPlaceHolder ID="footer" runat="server" />
            </div>
        </div>
        <!-- /footer -->


        <dx:ASPxLoadingPanel Theme="Moderno" ID="ASPxLoadingPanel2" Modal="true" ForeColor="#106488" ClientInstanceName="LoadingPanel"
            runat="server" Text="Por favor espere...">
        </dx:ASPxLoadingPanel>
        <dx:ASPxCallback ID="ASPxCallback2" ClientIDMode="Static" runat="server">
            <ClientSideEvents CallbackComplete="function(s, e) { LoadingPanel.Hide(); }" />
        </dx:ASPxCallback>
        <script>
            function CloseLoading() {
                LoadingPanel.Hide();
            }

            function MostrarLoading() {
                LoadingPanel.Show();
            }


            function showMessage(Mensaje, tipo) {
                CloseLoading();
                Swal.fire({
                    title: '',
                    text: Mensaje,
                    type: tipo,
                    confirmButtonText: 'Continuar'
                });

            }
            
        </script>
    </form>
</body>
</html>
