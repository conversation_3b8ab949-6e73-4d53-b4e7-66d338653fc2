//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 3/11/2023 09:44:13
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class ContratosRepository : NHibernateRepository<Domain.Contratos>, IContratosRepository
    {
        public ContratosRepository(ISession session) : base(session)
        {
        }

        public virtual IList<Domain.Contratos> GetAll()
        {
            return session.CreateQuery(string.Format("from Contratos")).List<Domain.Contratos>();
        }
        public IList<Domain.Contratos> GetByIdentificacionTributaria(string IdentificacionTributaria)
        {
            string hql = "from Contratos WHERE IdentificacionTributaria= :IdentificacionTributaria";

            return session.CreateQuery(hql).SetParameter("IdentificacionTributaria", IdentificacionTributaria).List<Domain.Contratos>();
        }
        //public IList<Domain.Contratos> GetByRazonSocial(string RazonSocial)
        //{
        //    string hql = "from Contratos WHERE RazonSocial like :RazonSocial";

        //    return session.CreateQuery(hql).SetParameter("RazonSocial", "%" + RazonSocial + "%").SetMaxResults(100).List<Domain.Contratos>();
        //}
        public IList<Domain.Contratos> GetByRazonSocialONombre(string consulta)
        {
            string hql = "from Contratos WHERE RazonSocial like :consulta OR Nombre like :consulta";

            return session.CreateQuery(hql).SetParameter("consulta", "%" + consulta + "%").SetMaxResults(100).List<Domain.Contratos>();
        }

        //public IList<Domain.Contratos> GetByNombre(string Nombre) {
        //    string hql = "from Contratos WHERE Nombre like :Nombre ";

        //    return session.CreateQuery(hql).SetParameter("Nombre","%" + Nombre + "%").SetMaxResults(100).List<Domain.Contratos>();
        //}

        public IList<Domain.Contratos> GetByNroContrato(int NroContrato)
        {
            string hql = "from Contratos WHERE NroContrato= :NroContrato";

            return session.CreateQuery(hql)
                .SetParameter("NroContrato", NroContrato).
                List<Domain.Contratos>();
        }

        public virtual Domain.Contratos GetByKey(int _Id)
        {
            return session.Get<Domain.Contratos>(_Id);
        }

        public virtual IList<Domain.Contratos> verificarContrato(string contrato) {
            return session.CreateQuery(string.Format("from Contratos WHERE NroContrato='" + contrato + "'")).List<Domain.Contratos>();
        }
    }
}
