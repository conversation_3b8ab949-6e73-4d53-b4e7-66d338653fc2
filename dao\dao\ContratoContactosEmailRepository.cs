//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 3/11/2023 09:44:13
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO {
    public partial class ContratoContactosEmailRepository : NHibernateRepository<Domain.ContratoContactosEmail>, IContratoContactosEmailRepository {
        public ContratoContactosEmailRepository(ISession session) : base(session) {
        }

        public virtual IList<Domain.ContratoContactosEmail> GetAll() {
            return session.CreateQuery(string.Format("from ContratoContactosEmail")).List<Domain.ContratoContactosEmail>();
        }

        public virtual Domain.ContratoContactosEmail GetByContratoContacto(int idContratoContacto) {
            return session.CreateQuery(string.Format("from ContratoContactosEmail where ContratoContactos.Id=:idContratoContactos"))
                .SetParameter("idContratoContactos",idContratoContacto)
                .UniqueResult<Domain.ContratoContactosEmail>();
        }

        public virtual Domain.ContratoContactosEmail GetByKey(int _Id) {
            return session.Get<Domain.ContratoContactosEmail>(_Id);
        }
    }
}
