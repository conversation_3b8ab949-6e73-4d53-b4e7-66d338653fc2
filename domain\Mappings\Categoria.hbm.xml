﻿<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="Categoria" table="Categoria">
    <id name="Id" type="Int32">
      <column name="id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Codigo" type="String">
      <column name="codigo" not-null="false" length="50" sql-type="nvarchar" />
    </property>
    <property name="Nombre" type="String">
      <column name="nombre" not-null="false" length="150" sql-type="nvarchar" />
    </property>
    <property name="UsaHS" type="Boolean">
      <column name="usaHS" default="0" not-null="false" sql-type="bit" />
    </property>
  </class>
</hibernate-mapping>