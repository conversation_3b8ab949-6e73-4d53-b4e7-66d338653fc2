//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 3/11/2023 09:44:13
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class LocalidadesRepository : NHibernateRepository<Domain.Localidades>, ILocalidadesRepository
    {
        public LocalidadesRepository(ISession session) : base(session)
        {
        }

       
      

        public virtual ICollection<Domain.Localidades> GetAll()
        {
            return session.CreateQuery(string.Format("from Localidades ORDER BY Nombre asc ")).List<Domain.Localidades>();
        }
        public virtual ICollection<Domain.Localidades> GetAll_local(bool Activo)
        {
            string hql = "from Localidades WHERE Activo= :Activo ORDER BY Nombre asc";

            IQuery q = session.CreateQuery(hql);
            q.SetParameter("Activo", Activo);

            return q.List<Domain.Localidades>();
        }

        public virtual ICollection<Domain.Localidades> GetByProvincia(int idProvincia) {
            return session.CreateQuery(string.Format("from Localidades where Provincias.Id=:idProvincia ORDER BY Nombre asc")).SetParameter("idProvincia",idProvincia).List<Domain.Localidades>();
        }

        public virtual Domain.Localidades ExisteCodigo(string codigo)
        {
            string hql = "from Localidades where  (upper(Codigo)=:codigo) and Activo=true";
            return session.CreateQuery(hql)
          .SetParameter("codigo", codigo.ToUpper())
          .UniqueResult<Domain.Localidades>();
        }
        public virtual Domain.Localidades GetByKey(int _Id)
        {
            return session.Get<Domain.Localidades>(_Id);
        }
    }
}
