﻿using Common.DDL;
using DAO;
using Domain;
using NHibernate;
using NHibernate.Mapping;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Caja
{
    public class Pagoservice
    {
        public static void SaveOrUpdateComprobante(Comprobantes comprobantes, List<Cheques> ListaCheque, List<Transferencias> Listatransferencias, Efectivo efectivo, List<ComprobantesRetenciones> ListaRetenciones, List<CuponesTarjetas> ListaCuponesTarjetas)
        {

            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new ComprobantesRepository(sess).Add(comprobantes);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }

            }


            foreach (Transferencias transferencia in Listatransferencias)
            {
                transferencia.Comprobantes = comprobantes;
                TrasferenciaService.SaveOrUpdate(transferencia);

            }

            foreach (Cheques cheques in ListaCheque)
            {
                cheques.Comprobantes = comprobantes;
                ChequeServices.SaveOrUpdate(cheques);

            }

            if (efectivo != null)
            {
                efectivo.Comprobantes = comprobantes;
                EfectivoServices.SaveOrUpdate(efectivo);
            }


            foreach (ComprobantesRetenciones retencion in ListaRetenciones)
            {
                retencion.Comprobantes = comprobantes;
                ComprobantesRetencionesServices.SaveOrUpdate(retencion);
            }

            foreach (CuponesTarjetas cuponesTarjeta in ListaCuponesTarjetas)
            {
                cuponesTarjeta.Comprobantes = comprobantes;
                CuponesTarjetasServices.SaveOrUpdate(cuponesTarjeta);
            }







        }


        //public static void Delete(Domain.Comprobantes c)
        //{
        //    using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
        //    {
        //        using (NHibernate.ITransaction tx = sess.BeginTransaction())
        //        {
        //            try
        //            {
        //                new ComprobantesRepository(sess).Remove(c);
        //                tx.Commit();
        //            }
        //            catch (Exception e)
        //            {
        //                tx.Rollback();
        //                throw e;
        //            }
        //            finally
        //            {
        //                sess.Close();
        //                sess.Dispose();
        //            }
        //        }

        //    }

        //}
    }

}

