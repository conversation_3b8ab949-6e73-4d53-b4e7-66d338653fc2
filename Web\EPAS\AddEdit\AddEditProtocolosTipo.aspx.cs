﻿using System;
using Business.Services.Epas;
using Business.Services.Provincias;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Business.Provincias;
using Domain;

namespace Web.EPAS.AddEdit
{
    public partial class AddEditProtocolosTipo : System.Web.UI.Page
    {
        protected void Page_Load(object sender,EventArgs e)
        {
            try
            {

                txtNumero.ClientEnabled = true;
                TxtID.Visible = false;
                btnVolverEditado.Visible = false;
                if (!IsPostBack)
                {
                    if (Request.QueryString["id"] != null)
                    {

                        int idProto = Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["id"].ToString()));
                        TxtID.Text = idProto.ToString();
                        btnVolverEditado.Visible = false;
                        CargarProto(idProto);

                    }
                    else
                    {
                        TxtID.Text = "0";
                        TxtID.Visible = false;
                        CB_Activo.Checked = true;
                        btnVolverEditado.Visible = false;
                    }
                }
            }

            catch
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1",
                    "showMessage('Error al cargar la página, consulte al administrador del sistema', 'error');", true);
            }
        }

        private void CargarProto(int idProto)
        {

            try
            {
                Domain.PlantillaProtocolos Proto = PlantillaProtocolosService.GetById(idProto);

                if (Proto != null)
                {
                    txtNumero.Text = Proto.Numero.ToString();
                    txtDescripcion.Text = Proto.Descripcion.ToString();

                    CB_Activo.Checked = Convert.ToBoolean(Proto.Activo);
                }

            }
            catch (Exception ex)
            {
            }

        }

        protected void btnGrabar_Click(object sender,EventArgs e) {


            try
            {
                PlantillaProtocolos dep;

                //Verifica que se haya ingresado un codigo y nombre
                if (!string.IsNullOrEmpty(txtNumero.Text) && !string.IsNullOrEmpty(txtDescripcion.Text))
                {
                    if (Request.QueryString["id"] != null)
                    {
                        // Se está editando un departamento existente.

                        int protoId = Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["id"]));
                        //Obtengo el objeto departamento                        
                        dep = PlantillaProtocolosService.GetById(protoId);
                        if (dep != null)
                        {
                            //El código si es edición, no se puede modificar, sólo actualizo
                            if (dep.Numero.ToString().Equals(txtNumero.Text.Trim().ToUpper()))
                            {
                                dep.Descripcion = txtDescripcion.Text.Trim().ToUpper();
                                dep.Activo = CB_Activo.Checked;
                                PlantillaProtocolosService.SaveOrUpdate(dep);

                                ScriptManager.RegisterStartupScript(this, this.GetType(), "message3", "showMessage('Fue editado correctamente.', 'success');", true);

                                txtNumero.ClientEnabled = false;
                                btnGrabar.ClientEnabled = false;
                                btnVolver.Visible = false;
                                btnVolverEditado.Visible = true;
                            }
                        }
                        //  ScriptManager.RegisterStartupScript(this, this.GetType(), "message1", "showMessage('El departamento ya existe en la base de datos.', 'error');", true);                                             
                    }
                    else
                    {
                        // Se está creando un nuevo departamento.
                        // Verificar si ya existe en la lista local, solo se verifica el código
                        if (PlantillaProtocolosService.ExisteCodigo(Convert.ToInt32(txtNumero.Text)) || PlantillaProtocolosService.ExisteNombre(txtDescripcion.Text.Trim()))
                        {
                            ScriptManager.RegisterStartupScript(this, this.GetType(), "message2", "showMessage('Ya existe un tipo de protocolo con ese código o nombre, no se puede guardar.', 'error');", true);
                        }
                        else
                        {
                            //No existe el código, puedo guardar en la base
                            dep = new PlantillaProtocolos();
                            dep.Numero = Convert.ToInt32(txtNumero.Text);
                            dep.Descripcion = txtDescripcion.Text.Trim().ToUpper();
                            dep.Activo = CB_Activo.Checked;

                            PlantillaProtocolosService.SaveOrUpdate(dep);

                            ScriptManager.RegisterStartupScript(this, this.GetType(), "message3", "showMessage('Agregado correctamente.', 'success');", true);
                            txtNumero.ClientEnabled = false;
                            btnGrabar.ClientEnabled = false;
                            btnVolver.Visible = false;
                            btnVolverEditado.Visible = true;
                        }

                    }



                }
                else
                {
                    ScriptManager.RegisterStartupScript(this, this.GetType(), "message4", "showMessage('Busque un departamento.', 'error');", true);
                }
            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "message5", "showMessage('Error al agregar Departamento, intente de nuevo.', 'error');", true);
            }
        
        

        }
    }
}