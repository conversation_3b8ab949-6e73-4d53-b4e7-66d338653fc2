<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="ComprobantesRetenciones" table="ComprobantesRetenciones">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Importe" type="Decimal">
      <column name="Importe" not-null="true" precision="19" scale="4" sql-type="money" />
    </property>
    <property name="Fecha" type="DateTime">
      <column name="Fecha" not-null="true" sql-type="datetime" />
    </property>
    <property name="Numero" type="Int32">
      <column name="Numero" not-null="true" precision="10" scale="0" sql-type="int" />
    </property>
    <many-to-one name="Retenciones" class="Retenciones">
      <column name="IdRetencion" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <many-to-one name="Comprobantes" class="Comprobantes">
      <column name="IdComprobante" not-null="true" precision="10" scale="0" sql-type="int" />
    </many-to-one>
  </class>
</hibernate-mapping>