﻿<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="ComprobantesCabeceras" table="ComprobantesCabeceras">
    <id name="IdComprobante" type="Int32">
      <column name="IdComprobante" not-null="true" precision="10" scale="0" sql-type="int" unique="true" />
      <generator class="identity" />
    </id>
    <property name="Cliente" type="String">
      <column name="Cliente" not-null="true" length="500" sql-type="nvarchar" />
    </property>
    <property name="Domicilio" type="String">
      <column name="Domicilio" not-null="true" length="500" sql-type="nvarchar" />
    </property>
    <property name="Localidad" type="String">
      <column name="Localidad" not-null="true" length="500" sql-type="nvarchar" />
    </property>
    <property name="Iva" type="String">
      <column name="Iva" not-null="true" length="50" sql-type="nvarchar" />
    </property>
    <property name="Cuit" type="String">
      <column name="Cuit" not-null="true" length="50" sql-type="nvarchar" />
    </property>
    <property name="NroCliente" type="String">
      <column name="NroCliente" not-null="true" length="100" sql-type="nvarchar" />
    </property>
    <property name="Vuelto" type="Decimal">
      <column name="Vuelto" not-null="true" precision="19" scale="4" sql-type="money" />
    </property>
    <property name="EtiquetaIvaRI" type="String">
      <column name="EtiquetaIvaRI" not-null="false" length="50" sql-type="nvarchar" />
    </property>
    <property name="EtiquetaIvaRNI" type="String">
      <column name="EtiquetaIvaRNI" not-null="false" length="50" sql-type="nvarchar" />
    </property>
    <property name="ImporteMonedaDePago" type="Decimal">
      <column name="ImporteMonedaDePago" not-null="false" precision="19" scale="4" sql-type="money" />
    </property>
    <property name="ValorMonedaDePago" type="Decimal">
      <column name="ValorMonedaDePago" not-null="false" precision="19" scale="4" sql-type="money" />
    </property>
    <property name="Zona" type="String">
      <column name="Zona" not-null="true" length="50" sql-type="nvarchar" />
    </property>
    <property name="IdIVACondicion" type="Int32">
      <column name="IdIVACondicion" not-null="true" precision="10" scale="0" sql-type="int" />
    </property>
    <property name="IdMonedaDePago" type="Int32">
      <column name="IdMonedaDePago" not-null="false" precision="10" scale="0" sql-type="int" />
    </property>
    <one-to-one name="Comprobantes" class="Comprobantes" constrained="true" />
    <set name="ComprobantesItems" inverse="true" generic="true">
      <key>
        <column name="IdComprobante" />
      </key>
      <one-to-many class="ComprobantesItems" />
    </set>
  </class>
</hibernate-mapping>