//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 25/9/2023 14:39:43
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.ComprobantesRetenciones, Domain in the schema.
    /// </summary>
    public partial class ComprobantesRetenciones {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for ComprobantesRetenciones constructor in the schema.
        /// </summary>
        public ComprobantesRetenciones()
        {
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Importe in the schema.
        /// </summary>
        public virtual decimal Importe
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Fecha in the schema.
        /// </summary>
        public virtual System.DateTime Fecha
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Numero in the schema.
        /// </summary>
        public virtual int Numero
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Retenciones in the schema.
        /// </summary>
        public virtual Retenciones Retenciones
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Comprobantes in the schema.
        /// </summary>
        public virtual Comprobantes Comprobantes
        {
            get;
            set;
        }
    }

}
