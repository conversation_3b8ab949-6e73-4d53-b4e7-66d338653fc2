﻿using Common.DDL;
using DAO;
using Domain;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Epas
{
    public class AnalisisTipoService
    {
        //Trae todo
        public static IList<Domain.TipoAnalisis> GetAll()
        {
            IList<Domain.TipoAnalisis> listaAux;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                listaAux = (IList<Domain.TipoAnalisis>)new TipoAnalisisRepository(sess).GetAll();
                sess.Close();
                sess.Dispose();
            }
            return (listaAux);

        }

        public static List<ItemCombo> GetByAnalisisTipo(bool Activo) {
            IList<TipoAnalisis> listaAux;
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                listaAux = (IList<TipoAnalisis>) new TipoAnalisisRepository(sess).GetByAnalisisTipo(Activo);
                sess.Close();
                sess.Dispose();
            }

            ItemCombo item;
            ListaCombo lista = new ListaCombo();
            foreach(TipoAnalisis p in listaAux) {

                item = new ItemCombo();
                item.ID = p.Id.ToString();
                item.Descripcion = p.Descripcion;
                lista.Add(item);
            }
            return (lista);
        }

        //obtener por id
        public static Domain.TipoAnalisis GetById(int id)
        {

            Domain.TipoAnalisis a;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {

                a = (Domain.TipoAnalisis)new TipoAnalisisRepository(sess).GetByKey(id);
                sess.Close();
                sess.Dispose();
                return a;
            }

        }

        public static bool ExisteCodigo(string codigo)
        {

            Domain.TipoAnalisis p;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {

                p = (Domain.TipoAnalisis)new TipoAnalisisRepository(sess).ExisteCodigo(codigo);
                sess.Close();
                sess.Dispose();
                return p != null;

            }

        }


        public static bool ExisteNombre(string Descripcion)
        {
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                return new TipoAnalisisRepository(sess).ExisteNombre(Descripcion);
            }
        }


        //agregar y actualizar
        public static void SaveOrUpdate(Domain.TipoAnalisis a)
        {

            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {

                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {

                    try
                    {

                        new TipoAnalisisRepository(sess).Add(a);

                        tx.Commit();
                    }
                    catch (Exception e)
                    {

                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {

                        sess.Close();
                        sess.Dispose();
                    }

                }

            }

        }

        //borrar
        public static void Delete(Domain.TipoAnalisis a)
        {
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new TipoAnalisisRepository(sess).Remove(a);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }

                }

            }

        }

    }
}
