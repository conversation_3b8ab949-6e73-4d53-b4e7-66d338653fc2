﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="UploadedFilesContainer_imagenes.ascx.cs" Inherits="Web.Util.UploadedFilesContainer_imagenes" %>
<%@ Register Assembly="DevExpress.Web.v18.1" Namespace="DevExpress.Web" TagPrefix="dx" %>
<script runat="server">
    
    public int Width { get; set; }
    public int Height { get; set; }
    public int NameColumnWidth { get; set; }
    public int SizeColumnWidth { get; set; }
    public string HeaderText { get; set; }
    public bool UseExtendedPopup { get; set; }
    protected void Page_Load(object sender, EventArgs e) {
        FilesRoundPanel_imagenes.Width = Width;
        FilesRoundPanel_imagenes.Height = Height;
        FilesRoundPanel_imagenes.HeaderText = HeaderText;
    }
    protected string GetOptionsString() {
        return "'" + GetStyleAttributeValue(NameColumnWidth) + "', '" 
            + GetStyleAttributeValue(SizeColumnWidth) + "', " 
            +  UseExtendedPopup.ToString().ToLower();
    }
    protected string GetStyleAttributeValue(int width) {
        return width > 0 ? string.Format("width: {0}px; max-width: {0}px", width) : string.Empty;
    }
 </script>
<script type="text/javascript">
    DXUploadedFilesContainer_imagenes.ApplySettings(<%= GetOptionsString() %>);
</script>

<dx:ASPxRoundPanel ID="FilesRoundPanel_imagenes" ClientInstanceName="FilesRoundPanel_imagenes" runat="server">
    <PanelCollection>
        <dx:PanelContent runat="server">
            <table id="uploadedFilesContainer_imagenes" class="uploadedFilesContainer">
                <tbody></tbody>
            </table>
        </dx:PanelContent>
    </PanelCollection>
</dx:ASPxRoundPanel>
