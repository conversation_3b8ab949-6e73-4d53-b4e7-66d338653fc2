﻿using Business.Services.Usuarios;
using Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web
{
    public partial class CheckPwd : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                lblMsg.Text = "";

            }
        }

        protected void btnGuardarContraseña_Click(object sender, EventArgs e)
        {
            try
            {
                String pwdEncrypt = Request.QueryString["id"];
                if (pwdEncrypt != null)
                {
                    Usuario u = UsuarioService.ConfirmarPwd(pwdEncrypt);

                    if (u == null)
                    {
                        lblError.Visible = true;
                        lblError.Text = "Link caducado, vuelva a iniciar el proceso";
                    }
                    else
                    {
                        if (txtPasword.Text.Equals(txtRePasword.Text) && txtPasword.Text.Trim() != "")
                        {
                            u.Password = txtPasword.Text;
                            u.Confirmacion = "";
                            UsuarioService.SaveOrUpdate(u);
                            lblMsg.Text = "Se ha actualizado su contraseña, ya puede ingresar al sistema";
                            lblMsg.Visible = true;
                            lblError.Visible = false;
                            btnLogin.Visible = true;
                            btnGuardarContraseña.Enabled = false;
                        }
                        else
                        {
                            lblError.Text = "Escriba una contraseña válida, igual en ambos campos";
                            lblError.Visible = true;
                            lblMsg.Visible = false;
                        }
                    }

                }
            }catch (Exception ex)
            {
                lblError.Text = ex.Message;
                lblError.Visible = true;
            }
        }

        protected void btnLogin_Click(object sender, EventArgs e)
        {
            Response.Redirect(Global.ApplicationPath + "/Login.aspx");
        }
    }
}