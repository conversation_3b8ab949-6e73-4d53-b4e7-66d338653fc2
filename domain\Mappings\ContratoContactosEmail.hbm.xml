<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="ContratoContactosEmail" table="ContratoContactos_Email">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Email" type="String">
      <column name="Email" not-null="false" length="50" sql-type="nvarchar" />
    </property>
    <property name="Observacion" type="String">
      <column name="Observacion" not-null="false" length="255" sql-type="nvarchar" />
    </property>
    <many-to-one name="ContratoContactos" class="ContratoContactos">
      <column name="IdContratoContacto" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
  </class>
</hibernate-mapping>