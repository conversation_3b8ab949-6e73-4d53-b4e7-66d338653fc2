﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 3/7/2023 15:50:57
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class CallesRepository : NHibernateRepository<Domain.Calles>, ICallesRepository
    {
        public CallesRepository(ISession session) : base(session)
        {
        }
        public virtual ICollection<Domain.Calles> GetByLocalidades(int idLocalidades)
        {
            return session.CreateQuery(string.Format("from Calles where Localidades.Id=:idLocalidades ORDER BY Nombre asc")).SetParameter("idLocalidades", idLocalidades).List<Domain.Calles>();
        }

        public virtual ICollection<Domain.Calles> GetAll()
        {
            return session.CreateQuery(string.Format("from Calles")).List<Domain.Calles>();
        }

        public virtual Domain.Calles GetByKey(int _Id)
        {
            return session.Get<Domain.Calles>(_Id);
        }
    }
}
