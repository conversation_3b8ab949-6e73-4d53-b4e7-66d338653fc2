﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="ResetPwd.aspx.cs" Inherits="Web.ResetPwd" %>

<%@ Register Assembly="DevExpress.Web.v18.1" Namespace="DevExpress.Web" TagPrefix="dx" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">

    <meta charset="utf-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
	<title>Recuperar contraseña - Si.Ge.Lab.</title>

    <link rel="icon" href="App_Themes/Tema1/img/favicon - EPAS (1).jpg"/>

	   <!-- Global stylesheets -->
  
    <link href="https://fonts.googleapis.com/css?family=Roboto:400,300,100,500,700,900" rel="stylesheet" type="text/css" />
    <link href="App_Themes/Tema1/assets/css/icons/fontawesome/styles.min.css" rel="stylesheet" />
    <link href="App_Themes/Tema1/assets/css/icons/phosphor/styles.min.css" rel="stylesheet" />
    <link href="App_Themes/Tema1/assets/css/icons/icomoon/styles.min.css" rel="stylesheet" />
    <link href="App_Themes/Tema1/assets/css/all.min.css" rel="stylesheet" />
    <!-- /Global Stylesheets -->

    <!-- Sweet Alerts CSS files -->
    <link href="App_Themes/Tema1/assets/css/Sweet_Alert/sweetalert2.min.css" rel="stylesheet" />
    <!-- /Sweet Alerts CSS files -->

    <!-- Core JS files -->
    <script src="/App_Themes/Tema1/assets/js/jquery.min.js"></script>
    <script src="/App_Themes/Tema1/assets/js/bootstrap.bundle.min.js"></script>
    <!-- /Core JS files -->

    <!-- Theme JS files -->
    <script src="/App_Themes/Tema1/assets/js/app.js"></script>
    <!-- /Theme JS files -->

    <!-- Sweet Alerts JS files -->    
    <script src="/App_Themes/Tema1/sweetalerts/sweet_alert.min.js"></script>
    <!-- /Sweet Alerts JS files -->


    <script>
        function runClientValidator(validationGroup) {
            Page_ClientValidate(validationGroup);
            if (Page_IsValid) {
                return true;
            }
            else {
                return false;
            }
        }

    </script>
 
</head>
<body class="bg-dark">
    
	<!-- Page content -->
	<div class="page-content">

		<!-- Main content -->
		<div class="content-wrapper">

			<!-- Content area -->
			<div class="content d-flex justify-content-center align-items-center">

				<!-- Login card -->
				<form class="login-form"  runat="server">
					<div class="card mb-0">
						<div class="card-body">
							<div class="text-center mb-3">
                                <img src="App_Themes/Tema1/img/Navbar-Img_-_EPAS.jpg" width="200" />
								<h5 class="mb-4">Recuperar Contraseña</h5>								
                                <p class="">Escriba el Email registrado con su cuenta</p>							
							</div>
                           
                            <div class="form-group form-control-feedback form-control-feedback-start mb-1">
						        <dx:ASPxTextBox ID="txtEmail" CssClass="form-control" AutoCompleteType="Disabled" NullText="<EMAIL>" runat="server"></dx:ASPxTextBox>						        
								<div class="form-control-feedback-icon mt-1">
									<i class="ph-envelope"></i>
								</div>
							
                                <asp:RequiredFieldValidator runat="server" ID="RequiredFieldValidator1" EnableClientScript="true" ControlToValidate="txtEmail" Display="Dynamic" 
                                ErrorMessage="El campo email es requerido." ValidationGroup="Formulario" ForeColor="" >
                                    <asp:RegularExpressionValidator ID="revMail" ValidationGroup="Formulario" ControlToValidate="txtEmail" ForeColor="" runat="server" ErrorMessage='Escriba un email válido' ValidationExpression="^([\w-\.]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([\w-]+\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$" Display="Dynamic" EnableClientScript="true" />
                                </asp:RequiredFieldValidator>       
							</div>
						
                            <div class="form-group d-flex align-items-center">
                                <dx:ASPxLabel ID="lblError" Visible="false" runat="server" Text="" CssClass="alert alert-danger"></dx:ASPxLabel>
                                <dx:ASPxLabel ID="lblMsg" Visible="false" runat="server" Text="" CssClass="alert alert-success"></dx:ASPxLabel>
                            </div>							
                            
							<div class="form-group form-group-feedback-left text-center my-4">
                                <dx:ASPxButton ID="btnRestablecer" CssClass="py-1 rounded-2" BackColor="SteelBlue" Theme="Material" runat="server" Text="Reestablecer" OnClick="btnRestablecer_Click" >
                                    <ClientSideEvents Click="function(s,e){if(runClientValidator('Formulario'))LoadingPanel.Show();}" />
                                </dx:ASPxButton>								
							</div>

                            <label class="form-group form-group-feedback d-flex justify-content-end text-secondary-emphasis fw-semibold">
                                <asp:HyperLink ID="volver" CssClass="text-secondary-emphasis" runat="server" NavigateUrl="~/Login.aspx" Text="Volver" />
                            </label>
							
						</div>
					</div>
				</form>
				<!-- /login card -->

			</div>
			<!-- /content area -->

		</div>
		<!-- /main content -->

	</div>
	<!-- /page content -->
      <dx:ASPxCallback ID="ASPxCallback2" ClientIDMode="Static" runat="server">
            <ClientSideEvents CallbackComplete="function(s, e) { LoadingPanel.Hide(); }" />
        </dx:ASPxCallback>
    <dx:ASPxLoadingPanel Theme="Moderno" ID="ASPxLoadingPanel2" Modal="true" ForeColor="#106488"
         ClientInstanceName="LoadingPanel"
            runat="server" Text="Por favor espere...">
        </dx:ASPxLoadingPanel>
      
</body>
</html>
