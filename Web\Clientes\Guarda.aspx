﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Guarda.aspx.cs" Inherits="Web.Clientes.Guarda" %>
<%@ Register Assembly="DevExpress.Web.v18.1" Namespace="DevExpress.Web" TagPrefix="dx" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
     <div class="row gx-3">

     <div class="card col-lg-12">

         <div class="row gx-1">
         <div class="form-group d-flex justify-content-end align-items-center my-3 pe-5">
         <dx:ASPxButton ID="btnVolver" runat="server" CssClass="btn btn-secondary fw-semibold px-0 py-1" OnClick="btnVolver_Click"  AutoPostBack="true" CausesValidation="false" Text="Cargar Otro">
            
         </dx:ASPxButton>

         <dx:ASPxButton ID="btnLista" runat="server" OnClick="btnLista_Click" CssClass="btn text-white fw-semibold px-0 py-1 ms-3" BackColor="Brown"  AutoPostBack="true" Text="Ir a la lista">
          
         </dx:ASPxButton>
     </div>
             </div>
         </div>
         </div>
    

</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="footer" runat="server">
</asp:Content>
