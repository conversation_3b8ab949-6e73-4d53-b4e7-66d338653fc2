﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Common.DDL;
using DAO;
using Domain;

namespace Business.Services.Caja
{
    public class EfectivoServices
    {
            public static IList<Efectivo> GetAll()
            {
                IList<Efectivo> listaAux;
                using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
                {
                    listaAux = (IList<Efectivo>)new EfectivoRepository(sess).GetAll();
                    sess.Close();
                    sess.Dispose();
                }

                return (listaAux);
            }
        
        public static Efectivo getById(int id)
        {
            Efectivo u;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                u = new EfectivoRepository(sess).GetByKey(id);

                if (u != null)
                {
                    //NHibernateUtil.Initialize();

                }
                sess.Close();
                sess.Dispose();
                return u;
            }
        }
        public static void SaveOrUpdate(Efectivo ef)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new EfectivoRepository(sess).Add(ef);

                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }
            }
        }

        public static void Delete(Domain.Efectivo ef)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new EfectivoRepository(sess).Remove(ef);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }

            }
        }
    }
}
