﻿using Common.DDL;
using DAO;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Epas
{
    public class DeterminacionesService
    {
        //Trae todo
        public static IList<Domain.Determinaciones> GetAll()
        {
            IList<Domain.Determinaciones> listaAux;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                listaAux = (IList<Domain.Determinaciones>)new DeterminacionesRepository(sess).GetAll();
                sess.Close();
                sess.Dispose();
            }
            return (listaAux);
        }

        public static IList<Domain.Determinaciones> GetByDeterminacion(int Id)
        {
            IList<Domain.Determinaciones> listaAux;
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                listaAux = (IList<Domain.Determinaciones>) new DeterminacionesRepository(sess).GetByDeterminacion(Id);
                sess.Close();
                sess.Dispose();
            }

            ItemCombo item;
            ListaCombo lista = new ListaCombo();
            foreach(Domain.Determinaciones p in listaAux)
            {

                item = new ItemCombo();
                item.ID = p.Id.ToString();
                item.Descripcion = p.Descripcion;
                lista.Add(item);
            }
            return (listaAux);
        }

        //obtener por id
        public static Domain.Determinaciones GetById(int id)
        {

            Domain.Determinaciones p;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {

                p = (Domain.Determinaciones)new DeterminacionesRepository(sess).GetByKey(id);
                sess.Close();
                sess.Dispose();
                return p;
            }

        }

        public static ICollection<Domain.Determinaciones> GetByDeterminacionActiva(bool Activo)
        {
            ICollection<Domain.Determinaciones> determinacionesActivas;
            using(ISession sess = NHibernateSessionProvider.GetSession())
            {
                determinacionesActivas = new DeterminacionesRepository(sess).GetByDeterminacionActiva(true)
                                        .Where(pp => pp.Activo == Activo)
                                        .ToList();
            }
            return determinacionesActivas;
        }

        public static bool Existedeterminacion(int NroDeterminacion)
        {

            Domain.Determinaciones p;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {

                p = (Domain.Determinaciones)new DeterminacionesRepository(sess).Existedeterminacion(NroDeterminacion);
                sess.Close();
                sess.Dispose();
                return p != null;

            }

        }


        public static bool ExisteNombre(string Descripcion)
        {
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                return new DeterminacionesRepository(sess).ExisteNombre(Descripcion);
            }
        }

        //agregar y actualizar

        public static void SaveOrUpdate(Domain.Determinaciones p)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            using (NHibernate.ITransaction tx = sess.BeginTransaction())
            {
                try
                {
                    new DeterminacionesRepository(sess).Add(p);
                    tx.Commit();
                }
                catch (Exception e)
                {
                    tx.Rollback();
                    throw new Exception("Error al intentar guardar o actualizar la determinación.", e);
                }
            }
        }

        //public static void SaveOrUpdate(Domain.Determinaciones p)
        //{

        //    using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
        //    {

        //        using (NHibernate.ITransaction tx = sess.BeginTransaction())
        //        {

        //            try
        //            {

        //                new DeterminacionesRepository(sess).Add(p);

        //                tx.Commit();
        //            }
        //            catch (Exception e)
        //            {

        //                tx.Rollback();
        //                throw e;
        //            }
        //            finally
        //            {

        //                sess.Close();
        //                sess.Dispose();
        //            }

        //        }

        //    }

        //}

        //si existe
        public static bool ExisteCodigo(string stringNroDeterminaciones, string stringDescripcion)
        {

            Domain.Determinaciones p;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {

                p = (Domain.Determinaciones)new DeterminacionesRepository(sess).ExisteCodigo(stringNroDeterminaciones, stringDescripcion);
                sess.Close();
                sess.Dispose();
                if (p != null)
                {
                    return true;
                }
                else
                {
                    return false;
                }

            }

        }

        //borrar
        public static void Delete(Domain.Determinaciones p)
        {
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new DeterminacionesRepository(sess).Remove(p);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }

                }

            }

        }
    }
}
