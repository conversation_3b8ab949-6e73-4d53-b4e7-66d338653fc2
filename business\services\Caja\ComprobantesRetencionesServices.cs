﻿using DAO;
using Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Caja
{
public class ComprobantesRetencionesServices
    {
            public static IList<ComprobantesRetenciones> GetAll()
            {
                IList<ComprobantesRetenciones> listaAux;
                using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
                {
                    listaAux = (IList<ComprobantesRetenciones>)new ComprobantesRetencionesRepository(sess).GetAll();
                    sess.Close();
                    sess.Dispose();
                }

                return (listaAux);
            }
            public static ComprobantesRetenciones getById(int id)
        {
            ComprobantesRetenciones u;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                u = new ComprobantesRetencionesRepository(sess).GetByKey(id);

                if (u != null)
                {
                    //NHibernateUtil.Initialize();

                }
                sess.Close();
                sess.Dispose();
                return u;
            }
        }
        public static void SaveOrUpdate(ComprobantesRetenciones cr)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new ComprobantesRetencionesRepository(sess).Add(cr);

                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }
            }
        }
        public static void Delete(Domain.ComprobantesRetenciones u)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new ComprobantesRetencionesRepository(sess).Remove(u);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }

            }
        }
    }
}
