//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 28/11/2023 16:48:49
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class DeterminacionesRepository : NHibernateRepository<Domain.Determinaciones>, IDeterminacionesRepository
    {
        public DeterminacionesRepository(ISession session) : base(session)
        {
        }

        public virtual ICollection<Domain.Determinaciones> GetAll()
        {
            return session.CreateQuery(string.Format("from Determinaciones")).List<Domain.Determinaciones>();
        }

        public virtual Domain.Determinaciones ExisteCodigo(string stringNroDeterminaciones, string stringDescripcion)
        {
            string hql = "from Determinaciones where (upper(NroDeterminacion)=:stringNroDeterminaciones or upper(Descripcion)=:stringDescripcion)";
            return session.CreateQuery(hql)
            .SetParameter("stringDescripcion", stringDescripcion.ToUpper())
          .SetParameter("stringNroDeterminaciones", stringNroDeterminaciones.ToUpper())
          .UniqueResult<Domain.Determinaciones>();
        }

        public virtual Domain.Determinaciones Existedeterminacion(int NroDeterminacion)
        {
            string hql = "from Determinaciones where  upper(NroDeterminacion)=:NroDeterminacion  and Activo=true";
            return session.CreateQuery(hql)

          .SetParameter("NroDeterminacion", NroDeterminacion.ToString())
          .UniqueResult<Domain.Determinaciones>();
        }

        public virtual bool ExisteNombre(string Descripcion)
        {
            string hql = "select count(*) from Determinaciones where upper(Descripcion) = :Descripcion and Activo = true";
            long count = session.CreateQuery(hql)
                .SetParameter("Descripcion", Descripcion.ToUpper())
                .UniqueResult<long>();

            return count > 0;
        }

        public virtual Domain.Determinaciones GetByKey(int _Id)
        {
            return session.Get<Domain.Determinaciones>(_Id);
        }

        public virtual ICollection<Domain.Determinaciones> GetByDeterminacion(int Id)
        {
            string hql = "from Determinaciones WHERE Id = :Id";

            IQuery q = session.CreateQuery(hql);
            q.SetParameter("Id",Id);

            return q.List<Domain.Determinaciones>();

        }

        public virtual ICollection<Domain.Determinaciones> GetByDeterminacionActiva(bool Activo)
        {
            string hql = "from Determinaciones where Activo = :Activo Order BY NroDeterminacion asc";

            IQuery q = session.CreateQuery(hql);
            q.SetParameter("Activo",Activo);
            return q.List<Domain.Determinaciones>();
        }
    }
}
