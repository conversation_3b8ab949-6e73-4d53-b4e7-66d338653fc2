<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="Transferencias" table="Transferencias">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Descripcion" type="String">
      <column name="Descripcion" not-null="false" sql-type="nvarchar" />
    </property>
    <property name="Importe" type="Decimal">
      <column name="Importe" not-null="true" precision="19" scale="4" sql-type="money" />
    </property>
    <property name="Fecha" type="DateTime">
      <column name="Fecha" not-null="false" sql-type="datetime" />
    </property>
    <property name="NroTransferencia" type="String">
      <column name="NroTransferencia" not-null="false" length="200" sql-type="nvarchar" />
    </property>
    <many-to-one name="Comprobantes" class="Comprobantes">
      <column name="IdComprobante" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <many-to-one name="Recibos" class="Recibos">
      <column name="IdRecibo" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
  </class>
</hibernate-mapping>