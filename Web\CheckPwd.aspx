﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="CheckPwd.aspx.cs" Inherits="Web.CheckPwd" %>

<%@ Register Assembly="DevExpress.Web.v18.1" Namespace="DevExpress.Web" TagPrefix="dx" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">

    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <title>Cambiar contraseña - Si.Ge.Lab.</title>
    <link rel="icon" href="App_Themes/Tema1/img/favicon.png"/>

    <!-- Global stylesheets -->
  
    <link href="https://fonts.googleapis.com/css?family=Roboto:400,300,100,500,700,900" rel="stylesheet" type="text/css" />
    <link href="App_Themes/Tema1/assets/css/icons/fontawesome/styles.min.css" rel="stylesheet" />
    <link href="App_Themes/Tema1/assets/css/icons/phosphor/styles.min.css" rel="stylesheet" />
    <link href="App_Themes/Tema1/assets/css/icons/icomoon/styles.min.css" rel="stylesheet" />
    <link href="App_Themes/Tema1/assets/css/all.min.css" rel="stylesheet" />
    <!-- /Global Stylesheets -->

    <!-- Sweet Alerts CSS files -->
    <link href="App_Themes/Tema1/assets/css/Sweet_Alert/sweetalert2.min.css" rel="stylesheet" />
    <!-- /Sweet Alerts CSS files -->

    <!-- Core JS files -->
    <script src="/App_Themes/Tema1/assets/js/jquery.min.js"></script>
    <script src="/App_Themes/Tema1/assets/js/bootstrap.bundle.min.js"></script>
    <!-- /Core JS files -->

    <!-- Theme JS files -->
    <script src="/App_Themes/Tema1/assets/js/app.js"></script>
    <!-- /Theme JS files -->

    <!-- Sweet Alerts JS files -->    
    <script src="/App_Themes/Tema1/sweetalerts/sweet_alert.min.js"></script>
    <!-- /Sweet Alerts JS files -->

    <script>
        function runClientValidator(validationGroup) {
            Page_ClientValidate(validationGroup);
            if (Page_IsValid) {
                return true;
            }
            else {
                return false;
            }
        }
    </script>
</head>
<body class="bg-dark">

    <!-- Page content -->
    <div class="page-content">

        <!-- Main content -->
        <div class="content-wrapper">

            <!-- Content area -->
            <div class="content d-flex justify-content-center align-items-center">

                <!-- Login card -->
                <form class="login-form" runat="server">
                    <div class="card mb-0">
                        <div class="card-body">
                            <div class="text-center mb-4">
                                <img src="App_Themes/Tema1/img/DitycLogo.jpg" width="150" class="mb-2" />
                                <h5 class="mb-0">Cambiar contraseña</h5>                                
                            </div>

                            <div class="form-group form-group-feedback form-group-feedback-center mb-3">

                                <label for="txtPasword" class="form-label mb-1">
                                    <asp:Literal ID="lPassword" runat="server" Text='Escriba la nueva contraseña:' />
                                </label>
                                <dx:ASPxTextBox autocomplete="off" ID="txtPasword" cssClass="input-block-level form-control"  Password="true" runat="server"></dx:ASPxTextBox>

                                <asp:RequiredFieldValidator ValidationGroup="Formulario" CssClass="badge badge-danger" EnableClientScript="true" ID="rfvPasword" runat="server" ControlToValidate="txtPasword" ErrorMessage='Campo obligatorio' />
                                <br />
                                <label for="txtRePasword" class="mb-1">
                                    <asp:Literal ID="lRePassword" runat="server" Text='Reescriba la contraseña:' />
                                </label>
                                <dx:ASPxTextBox autocomplete="off" ID="txtRePasword" cssClass="input-block-level form-control" Password="true" runat="server"></dx:ASPxTextBox>

                                <br />
                                <asp:CompareValidator ID="cvPassword" ValidationGroup="Formulario" CssClass="badge badge-danger" runat="server" ControlToCompare="txtPasword" ControlToValidate="txtRePasword"
                                    ErrorMessage='Ambas contraseñas deben ser iguales' />
                            </div>

                            <div class="form-group d-flex align-items-center">
                                <dx:ASPxLabel ID="lblError" Visible="false" runat="server" Text="" CssClass="alert alert-danger"></dx:ASPxLabel>
                                <dx:ASPxLabel ID="lblMsg" Visible="false" runat="server" Text="" CssClass="alert alert-success"></dx:ASPxLabel>
                            </div>

                            <div class="form-group form-group-feedback-left text-center">
                                <dx:ASPxButton ID="btnGuardarContraseña" Theme="Material" CssClass="rounded-2 py-1" HorizontalAlign="Center"
                                    Width="200px" runat="server" Text="Guardar contraseña" OnClick="btnGuardarContraseña_Click">
                                    <ClientSideEvents Click="function(s,e){if(runClientValidator('Formulario'))LoadingPanel.Show();}" />
                                </dx:ASPxButton>
                            </div>

                            <div class="form-group form-group-feedback-left text-center">
                                <dx:ASPxButton ID="btnLogin" Theme="Material" HorizontalAlign="Center" Visible="false"
                                    Width="200px" runat="server" Text="Ir al login" OnClick="btnLogin_Click">
                                    <ClientSideEvents Click="function(s,e){LoadingPanel.Show();}" />
                                </dx:ASPxButton>
                            </div>
                        </div>
                    </div>
                </form>
                <!-- /login card -->
            </div>
            <!-- /content area -->
        </div>
        <!-- /main content -->
    </div>
    <!-- /page content -->
    <dx:ASPxCallback ID="ASPxCallback2" ClientIDMode="Static" runat="server">
        <ClientSideEvents CallbackComplete="function(s, e) { LoadingPanel.Hide(); }" />
    </dx:ASPxCallback>
    <dx:ASPxLoadingPanel Theme="Moderno" ID="ASPxLoadingPanel2" Modal="true" ForeColor="#106488"
        ClientInstanceName="LoadingPanel"
        runat="server" Text="Por favor espere...">
    </dx:ASPxLoadingPanel>
</body>
</html>