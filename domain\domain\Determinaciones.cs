﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 1/12/2023 09:38:57
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.Determinaciones, Domain in the schema.
    /// </summary>
    public partial class Determinaciones {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for Determinaciones constructor in the schema.
        /// </summary>
        public Determinaciones()
        {
            this.ProtocoloDeterminaciones = new HashSet<ProtocoloDeterminaciones>();
            this.Rols = new HashSet<Rol>();
            this.RolDeterminaciones = new HashSet<RolDeterminaciones>();
            this.PlantillaProtocolos = new HashSet<PlantillaProtocolos>();
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for NroDeterminacion in the schema.
        /// </summary>
        public virtual System.Nullable<int> NroDeterminacion
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Descripcion in the schema.
        /// </summary>
        public virtual string Descripcion
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for UnidadPrimaria in the schema.
        /// </summary>
        public virtual string UnidadPrimaria
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for UnidadSecundaria in the schema.
        /// </summary>
        public virtual string UnidadSecundaria
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for FactorConversionPrefijo in the schema.
        /// </summary>
        public virtual System.Nullable<decimal> FactorConversionPrefijo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for FactorConversionSufijo in the schema.
        /// </summary>
        public virtual System.Nullable<decimal> FactorConversionSufijo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ValorReferencia in the schema.
        /// </summary>
        public virtual string ValorReferencia
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for DecimalesMostrar in the schema.
        /// </summary>
        public virtual System.Nullable<int> DecimalesMostrar
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Activo in the schema.
        /// </summary>
        public virtual System.Nullable<bool> Activo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ProtocoloDeterminaciones in the schema.
        /// </summary>
        public virtual ISet<ProtocoloDeterminaciones> ProtocoloDeterminaciones
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Rols in the schema.
        /// </summary>
        public virtual ISet<Rol> Rols
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for RolDeterminaciones in the schema.
        /// </summary>
        public virtual ISet<RolDeterminaciones> RolDeterminaciones
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for PlantillaProtocolos in the schema.
        /// </summary>
        public virtual ISet<PlantillaProtocolos> PlantillaProtocolos
        {
            get;
            set;
        }
    }

}
