﻿using DAO;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Epas
{
   public class SubFuentesService
    {
        //Trae todo
        public static IList<Domain.SubFuentes> GetAll()
        {
            IList<Domain.SubFuentes> listaAux;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                listaAux = (IList<Domain.SubFuentes>)new SubFuentesRepository(sess).GetAll();
                sess.Close();
                sess.Dispose();
            }
            return (listaAux);
        }

        public static IList<Domain.SubFuentes> GetByFuentes(int idFuentes) {

            IList<Domain.SubFuentes> listaAux;
            using(ISession sess = NHibernateSessionProvider.GetSession()) {

                listaAux = (IList<Domain.SubFuentes>) new SubFuentesRepository(sess).GetByFuentes(idFuentes);
                sess.Close();
                sess.Dispose();
            }
            return (listaAux);
        }

        //obtener por id
        public static Domain.SubFuentes GetById(int id)
        {

            Domain.SubFuentes p;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {

                p = (Domain.SubFuentes)new SubFuentesRepository(sess).GetByKey(id);
                sess.Close();
                sess.Dispose();
                return p;
            }

        }

        //agregar y actualizar
        public static void SaveOrUpdate(Domain.SubFuentes p)
        {

            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {

                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {

                    try
                    {

                        new SubFuentesRepository(sess).Add(p);

                        tx.Commit();
                    }
                    catch (Exception e)
                    {

                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {

                        sess.Close();
                        sess.Dispose();
                    }

                }

            }

        }
        //si existe
        public static bool ExisteSubFuentes(string stringCodigo, string stringDescripcion, int idFuente)
        {
            Domain.SubFuentes u;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                u = new SubFuentesRepository(sess).ExisteSubFuentes(stringCodigo, stringDescripcion, idFuente);

                sess.Close();
                sess.Dispose();
                if (u != null)
                {
                    return true;
                }
                else
                {
                    return false;
                }



            }
        }

        //borrar
        public static void Delete(Domain.SubFuentes p)
        {
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new SubFuentesRepository(sess).Remove(p);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }

                }

            }

        }

    }
}
