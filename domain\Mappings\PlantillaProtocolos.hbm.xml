<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="PlantillaProtocolos" table="PlantillaProtocolos">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Numero" type="Int32">
      <column name="Numero" not-null="false" precision="10" scale="0" sql-type="int" />
    </property>
    <property name="Descripcion" type="String">
      <column name="Descripcion" not-null="false" sql-type="nvarchar" />
    </property>
    <property name="Activo" type="Boolean">
      <column name="Activo" default="1" not-null="false" sql-type="bit" />
    </property>
    <set name="Determinaciones" table="PlantillaProtocoloDeterminaciones" generic="true">
      <key>
        <column name="IdProtocoloTipo" not-null="false" precision="10" scale="0" sql-type="int" />
      </key>
      <many-to-many class="Determinaciones" fetch="join">
        <column name="IdDeterminaciones" not-null="false" precision="10" scale="0" sql-type="int" />
      </many-to-many>
    </set>
  </class>
</hibernate-mapping>