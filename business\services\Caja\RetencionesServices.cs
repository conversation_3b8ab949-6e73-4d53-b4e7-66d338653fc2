﻿using Common.DDL;
using DAO;
using Domain;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Caja
{
    public class RetencionesServices
    {
        public static IList<Retenciones> GetAll()
        {
            IList<Retenciones> listaAux;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                listaAux = (IList<Retenciones>)new RetencionesRepository(sess).GetAll();
                sess.Close();
                sess.Dispose();
            }


            return (listaAux);
        }
        

        public static Retenciones getById(int id)
        {
            Retenciones u;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                u = new RetencionesRepository(sess).GetByKey(id);

                if (u != null)
                {
                    NHibernateUtil.Initialize(id);

                }
                sess.Close();
                sess.Dispose();
                return u;
            }
        }
        public static void SaveOrUpdate(Retenciones R)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new RetencionesRepository(sess).Add(R);

                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }
            }
        }
        public static void Delete(Domain.Retenciones R)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new RetencionesRepository(sess).Remove(R);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }

            }
        }

    }
}

