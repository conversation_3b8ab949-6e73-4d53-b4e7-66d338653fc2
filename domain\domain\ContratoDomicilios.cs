//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 6/11/2023 09:33:14
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.ContratoDomicilios, Domain in the schema.
    /// </summary>
    public partial class ContratoDomicilios {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for ContratoDomicilios constructor in the schema.
        /// </summary>
        public ContratoDomicilios()
        {
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for EntreCalles in the schema.
        /// </summary>
        public virtual string EntreCalles
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Altura in the schema.
        /// </summary>
        public virtual string Altura
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Casa in the schema.
        /// </summary>
        public virtual string Casa
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Piso in the schema.
        /// </summary>
        public virtual string Piso
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Dpto in the schema.
        /// </summary>
        public virtual string Dpto
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Manzana in the schema.
        /// </summary>
        public virtual string Manzana
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Lote in the schema.
        /// </summary>
        public virtual string Lote
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for DuplexModulo in the schema.
        /// </summary>
        public virtual string DuplexModulo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Peatonal in the schema.
        /// </summary>
        public virtual string Peatonal
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Tira in the schema.
        /// </summary>
        public virtual string Tira
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Latitud in the schema.
        /// </summary>
        public virtual string Latitud
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Longitud in the schema.
        /// </summary>
        public virtual string Longitud
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for UbicacionNro in the schema.
        /// </summary>
        public virtual string UbicacionNro
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Complemento in the schema.
        /// </summary>
        public virtual string Complemento
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for NroCatastro in the schema.
        /// </summary>
        public virtual string NroCatastro
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Barrio in the schema.
        /// </summary>
        public virtual string Barrio
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Calles in the schema.
        /// </summary>
        public virtual Calles Calles
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for DomiciliosTipos in the schema.
        /// </summary>
        public virtual DomiciliosTipos DomiciliosTipos
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Contratos in the schema.
        /// </summary>
        public virtual Contratos Contratos
        {
            get;
            set;
        }
    }

}
