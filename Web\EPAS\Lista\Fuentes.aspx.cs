﻿using Business.Services.Epas;
using Business.Services.Provincias;
using DevExpress.Web;
using Domain;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Services.Description;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web.EPAS.Lista 
{
    public partial class Fuentes : System.Web.UI.Page
    {
        protected void Page_Load(object sender,EventArgs e)
        {
            try
            {
                CargarDatosEnGv_Fuentes();

                if (!IsPostBack)
                {
                }

            }
            catch (Exception ex)
            {
                string g = ex.Message;
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('" + g + "', 'error');", true);

            }
        }

        private void CargarDatosEnGv_Fuentes()
        {


            try
            {
                List<Domain.Fuentes> fuentes = (List<Domain.Fuentes>) FuentesService.GetAll();
                gv_Fuentes.DataSource = fuentes;
                gv_Fuentes.DataBind();
            }
            catch (Exception ex)
            {
                string g = ex.Message;
            }

        }

        protected void btnEditar_Click(object sender, EventArgs e)
        {

            try
            {
                ASPxButton btn = (ASPxButton)sender;
                if (btn != null)
                {
                    GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer)btn.NamingContainer;
                    if (c != null)
                    {
                        var ind = DataBinder.Eval(c.DataItem, "Id");

                        if (ind != null)
                        {
                            Domain.Fuentes fuente = FuentesService.GetById(Convert.ToInt32(ind));

                            if(fuente.Activo == false)
                            {
                                string message = string.Format("La Fuente {0} no se puede editar ya que está anulada.",fuente.Descripcion);
                                ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('" + message + "')",true);

                            } else
                            {
                                Response.Redirect(Global.ApplicationPath + "/EPAS/AddEdit/AddEditFuentes.aspx?id=" + Util.Helpers.Encrypt(ind.ToString()), false);

                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al tratar de editar un elemento, consulte al administrador del sistema', 'error');", true);
            }
        }

        protected void btnAnularActivar_Click(object sender, EventArgs e)
        {
            try
            {
                ASPxButton btn = (ASPxButton) sender;
                GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer) btn.NamingContainer;
                var ind = DataBinder.Eval(c.DataItem,"Id");

                Domain.Fuentes FU = FuentesService.GetById(Convert.ToInt32(ind));
                FU.Activo = !FU.Activo;

                FuentesService.SaveOrUpdate(FU);
                CargarDatosEnGv_Fuentes();

            } catch(Exception ex)
            {
                ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('Error al tratar de eliminar un elemento, consulte al administrador del sistema', 'error');",true);
            }
        }

        protected void btnActivar_Click(object sender,EventArgs e)
        {

        }
    }
}