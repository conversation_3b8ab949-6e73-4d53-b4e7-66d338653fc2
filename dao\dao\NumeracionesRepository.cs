//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 26/9/2023 15:57:58
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class NumeracionesRepository : NHibernateRepository<Domain.Numeraciones>, INumeracionesRepository
    {
        public NumeracionesRepository(ISession session) : base(session)
        {
        }

        public virtual IList<Domain.Numeraciones> GetAll()
        {
            return session.CreateQuery(string.Format("from Numeraciones")).List<Domain.Numeraciones>();
        }

        public Numeraciones GetByCodigo(string codigo) {
            return session.CreateQuery(string.Format("from Numeraciones where upper(Codigo)=:codigo"))
         .SetParameter("codigo",codigo.Trim().ToString().ToUpper())
         .UniqueResult<Domain.Numeraciones>();
        }

        public virtual Domain.Numeraciones GetByKey(int _Id)
        {
            return session.Get<Domain.Numeraciones>(_Id);
        }
    }
}
