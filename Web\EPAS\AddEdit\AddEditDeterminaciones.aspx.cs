﻿using Business.Provincias;
using Business.Services.Epas;
using Business.Services.Provincias;
using DevExpress.Web;
using Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web.EPAS.AddEdit
{
    public partial class AddEditDeterminaciones : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                btnVolverEditado.Visible = false;
                if (!IsPostBack)
                {
                    //LlenadoCmb_Metodos();

                    if (Request.QueryString["id"] != null)
                    {

                        int iddeter = Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["id"].ToString()));
                        TxtID.Text = iddeter.ToString();
                        btnVolverEditado.Visible = false;
                        CargarDeter(iddeter);

                    }
                    else
                    {
                        TxtID.Text = "0";
                        TxtID.Visible = false;
                        CB_Activo.Checked = true;
                        btnVolverEditado.Visible = false;
                    }


                }
            }
            catch
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1",
                    "showMessage('Error al cargar la página, consulte al administrador del sistema', 'error');", true);
            }

        }
        //protected void LlenadoCmb_Metodos()
        //{

        //    CmbMetodos.DataSource = MetodosdeMedicionService.GetAll();
        //    CmbMetodos.TextField = "Descripcion";
        //    CmbMetodos.ValueField = "Id";
        //    CmbMetodos.DataBind();
        //    CmbMetodos.Items.Insert(0, new ListEditItem());

        //}

        private void CargarDeter(int iddeter)
        {

            try
            {
                Domain.Determinaciones deter = DeterminacionesService.GetById(id: iddeter);

                if (deter != null)
                {
                    Txtcodigo.Text = deter.NroDeterminacion.ToString();
                    Txtdescripcion.Text = deter.Descripcion;
                    TxtValorReferencia.Text = deter.ValorReferencia;
                    TxtUniPrimaria.Text =string.IsNullOrEmpty(deter.UnidadPrimaria)?"":deter.UnidadPrimaria.Trim();
                    //TxtValorReferencia.Text = deter.ValorReferencia.ToString();
                    TxtDecimales.Text = deter.DecimalesMostrar.ToString();
                    //CmbMetodos.Items.FindByValue(deter.Metodos.Id.ToString()).Selected = true;
                    CB_Activo.Checked = Convert.ToBoolean(deter.Activo);
                }

            }
            catch (Exception ex)
            {
            }

        }

        protected void btnGrabar_Click(object sender,EventArgs e) 
        {

            try
            {
                Determinaciones det;
                //Verifica que se haya ingresado un codigo y nombre
                if (!string.IsNullOrEmpty(Txtcodigo.Text) && !string.IsNullOrEmpty(Txtdescripcion.Text))
                {
                    if (Request.QueryString["id"] != null)
                    {
                        // Se está editando un departamento existente.

                        int detId = Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["id"]));
                        //Obtengo el objeto departamento                        
                        det = DeterminacionesService.GetById(detId);
                        if (det != null)
                        {
                            //El código si es edición, no se puede modificar, sólo actualizo.

                            if (det.NroDeterminacion.ToString().Equals(Txtcodigo.Text.Trim()))
                                {
                                det.Descripcion = Txtdescripcion.Text.Trim().ToUpper();
                                det.ValorReferencia = TxtValorReferencia.Text.Trim();
                                det.UnidadPrimaria = TxtUniPrimaria.Text.Trim();
                                det.UnidadSecundaria = TxtUniSecu.Text.Trim();

                                if (!string.IsNullOrEmpty(TxtFactorConversionPrefijo.Text))
                                {
                                    det.FactorConversionPrefijo = Convert.ToDecimal(TxtFactorConversionPrefijo.Text);
                                }
                                else
                                {
                                    det.FactorConversionPrefijo = 1000;
                                }

                                det.ValorReferencia = TxtValorReferencia.Text.Trim();

                                if (!string.IsNullOrEmpty(TxtDecimales.Text))
                                {
                                    det.DecimalesMostrar = Convert.ToInt32(TxtDecimales.Text);
                                }
                                else
                                {
                                    det.DecimalesMostrar = ' ';
                                }

                               
                                det.Activo = CB_Activo.Checked;

                                DeterminacionesService.SaveOrUpdate(det);
                               
                                ScriptManager.RegisterStartupScript(this, this.GetType(), "message3", "showMessage('Fue editado correctamente.', 'success');", true);

                                Txtcodigo.ClientEnabled = false;
                                btnGrabar.ClientEnabled = false;
                                btnVolver.Visible = false;
                                btnVolverEditado.Visible = true;
                            }
                        }
                        //  ScriptManager.RegisterStartupScript(this, this.GetType(), "message1", "showMessage('El departamento ya existe en la base de datos.', 'error');", true);                                             
                    }
                    else
                    {
                        // Se está creando uno nuevo .
                        // Verificar si ya existe en la lista local, solo se verifica el código
                        if (DeterminacionesService.Existedeterminacion(Convert.ToInt32(Txtcodigo.Text)) || DeterminacionesService.ExisteNombre(Txtdescripcion.Text.Trim()))
                        {
                            ScriptManager.RegisterStartupScript(this, this.GetType(), "message2", "showMessage('Ya existe un departamento con ese código o nombre, no se puede guardar.', 'error');", true);
                        }
                        else
                        {
                            //No existe el código, puedo guardar en la base
                            det = new Determinaciones();
                            det.NroDeterminacion = Convert.ToInt32(Txtcodigo.Text);
                            det.Descripcion = Txtdescripcion.Text.Trim().ToUpper();
                            det.ValorReferencia = TxtValorReferencia.Text.Trim();
                            det.UnidadPrimaria = TxtUniPrimaria.Text.Trim();
                            det.UnidadSecundaria = TxtUniSecu.Text.Trim();
                            if (!string.IsNullOrEmpty(TxtFactorConversionPrefijo.Text))
                            {
                                det.FactorConversionPrefijo = Convert.ToDecimal(TxtFactorConversionPrefijo.Text);
                            }
                            else
                            {
                                det.FactorConversionPrefijo = 1000;
                            }

                            if (!string.IsNullOrEmpty(TxtDecimales.Text))
                            {
                                det.DecimalesMostrar = Convert.ToInt32(TxtDecimales.Text);
                            }
                            else
                            {
                                det.DecimalesMostrar = ' ';
                            }
                            det.Activo = CB_Activo.Checked;

                            DeterminacionesService.SaveOrUpdate(det);

                            ScriptManager.RegisterStartupScript(this, this.GetType(), "message3", "showMessage('Agregado correctamente.', 'success');", true);
                            Txtcodigo.ClientEnabled = false;
                            btnGrabar.ClientEnabled = false;
                            btnVolver.Visible = false;
                            btnVolverEditado.Visible = true;

                        }

                    }

                }


                else
                {
                    ScriptManager.RegisterStartupScript(this, this.GetType(), "message4", "showMessage('Busque una Determinaciòn.', 'error');", true);
                }
            }

            catch (Exception ex)
            {
                Console.WriteLine("Error: " + ex.Message);
                Console.WriteLine("StackTrace: " + ex.StackTrace);
                ScriptManager.RegisterStartupScript(this, this.GetType(), "message5", "showMessage('Error al agregar una determinaciòn, intente de nuevo.', 'error');", true);
            }

        }
    }
}