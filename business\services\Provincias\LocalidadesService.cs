﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Collections;
using DAO;
using Domain;
using NHibernate;
using NPOI.Util;
using NPOI.SS.Formula.Functions;

namespace Business.Services.Provincias
    {

    public class LocalidadesService
        {

        public static IList<Domain.Localidades> GetByProvincias(int idProvincia) {

            IList<Domain.Localidades> listaAux;
            using(ISession sess = NHibernateSessionProvider.GetSession()) {

                listaAux = (IList<Domain.Localidades>) new LocalidadesRepository(sess).GetByProvincia(idProvincia);
                foreach (Domain.Localidades l in listaAux)
                {
                    NHibernateUtil.Initialize(l.Zonas);
                    NHibernateUtil.Initialize(l.Departamentos);
                    NHibernateUtil.Initialize(l.Provincias);
                }
                sess.Close();
                sess.Dispose();
            }
            return (listaAux);

        }

        //Trae todo
        public static IList<Domain.Localidades> GetAll()
        {

            IList<Domain.Localidades> listaAux;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {

                listaAux = (IList<Domain.Localidades>)new LocalidadesRepository(sess).GetAll();

                foreach (Domain.Localidades l in listaAux)
                {
                    NHibernateUtil.Initialize(l.Zonas);
                    NHibernateUtil.Initialize(l.Departamentos);
                    NHibernateUtil.Initialize(l.Provincias);
                }
                sess.Close();
                sess.Dispose();

                return (listaAux);
            }

        }

        //obtener por id
        public static Localidades GetById(int id)
        {

            Localidades p;
            using (ISession sess = NHibernateSessionProvider.GetSession())

            {

                p = (Localidades)new LocalidadesRepository(sess).GetByKey(id);
                if (p != null)
                {

                    NHibernateUtil.Initialize(p.Departamentos);
                    NHibernateUtil.Initialize(p.Zonas);
                    NHibernateUtil.Initialize(p.Provincias);

                }
                sess.Close();
            sess.Dispose();
            return p;
        }

    }

        //si existe
        public static bool ExisteCodigo(string codigo)
        {

            Domain.Localidades p;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {

                p = (Domain.Localidades)new LocalidadesRepository(sess).ExisteCodigo(codigo);
                sess.Close();
                sess.Dispose();
                return p != null;

            }

        }



        //agregar y actualizar
        public static void SaveOrUpdate(Domain.Localidades p)
            {

            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
                {

                using(NHibernate.ITransaction tx = sess.BeginTransaction())
                    {

                    try
                        {

                        new LocalidadesRepository(sess).Add(p);

                        tx.Commit();
                    } catch(Exception e)
                    {

                        tx.Rollback();
                        throw e;
                    } finally
                    {

                        sess.Close();
                        sess.Dispose();
                    }

                }

            }

        }

        //borrar
        public static void Delete(Domain.Localidades p)
            {
            using(ISession sess = NHibernateSessionProvider.GetSession())
                {
                using(ITransaction tx = sess.BeginTransaction())
                    {
                    try
                        {
                        new LocalidadesRepository(sess).Remove(p);
                        tx.Commit();
                    } catch(Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    } finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }

                }

            }

        }

    }

}
