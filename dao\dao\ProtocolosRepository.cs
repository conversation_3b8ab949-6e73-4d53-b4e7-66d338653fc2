// Esta capa es el DAO de protocolo, la capa de acceso a datos
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class ProtocolosRepository : NHibernateRepository<Domain.Protocolos>, IProtocolosRepository
    {
        public ProtocolosRepository(ISession session) : base(session)
        {
        }

        /// <summary>
        /// Obtiene todos los protocolos
        /// </summary>
        /// <returns></returns>
        public virtual ICollection<Domain.Protocolos> GetAll()
        {
            return session.CreateQuery(string.Format("from Protocolos")).List<Domain.Protocolos>();
        }

        /// <summary>
        /// Obtiene todos los protocolos por estado
        /// </summary>
        /// <param name="codigoEstado"></param>
        /// <returns></returns>
        public virtual ICollection<Domain.Protocolos> GetByEstado(string codigoEstado)
        {
            return session.CreateQuery(string.Format("from Protocolos where ProtocolosEstados.Codigo=:codigo"))
                .SetParameter("codigo", codigoEstado)
                .List<Domain.Protocolos>();
        }

        public virtual IList<Domain.Protocolos> GetByIdSolicitante(int idSolicitante)
        {
            return session.CreateQuery(string.Format("from Protocolos where Contratos_IdContratoSolicitante.Id=:Id"))
                .SetParameter("Id",idSolicitante)
                .List<Domain.Protocolos>();
        }

        /// <summary>
        /// Obtiene un protocolo
        /// </summary>
        /// <param name="_Id"></param>
        /// <returns></returns>
        public virtual Domain.Protocolos GetByKey(int _Id)
        {
            return session.Get<Domain.Protocolos>(_Id);
        }
    }
}
