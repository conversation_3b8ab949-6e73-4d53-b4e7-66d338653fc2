﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="AddEditDeterminaciones.aspx.cs" Inherits="Web.EPAS.AddEdit.AddEditDeterminaciones" %>

<%@ Register Assembly="DevExpress.Web.v18.1" Namespace="DevExpress.Web" TagPrefix="dx" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">

    <script type="text/javascript">

        function volverButton() {

            if (confirm) {
                Swal.fire({
                    text: "No se guardaron los cambios. Desea volver de todos modos?",
                    showCancelButton: true,
                    confirmButtonText: "Volver"
                }).then((result) => {
                    if (result.isConfirmed) {

                        window.location.href = "/SIGeLab/EPAS/Lista/Determinaciones.aspx";

                    }
                });
            }
        }

        function volverButtonEditado() {

            if (confirm) {

                window.location.href = "/SIGeLab/EPAS/Lista/Determinaciones.aspx";

            }
        }

    </script>

</asp:Content>


<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

    <div class="page-header page-header-light shadow">
        <div class="page-header-content d-lg-flex border-top">
            <div class="d-flex">
                <div class="breadcrumb py-2">
                    <a href="<%= Web.Util.Helpers.GetApplicationPath() %>/Inicio.aspx" class="breadcrumb-item"><i class="icon-home2 mr-2"></i>Inicio</a>
                    <a href="<%= Web.Util.Helpers.GetApplicationPath() %>/EPAS/Lista/Determinaciones.aspx" class="breadcrumb-item"><i class="icon-display me-2"></i>Mantenimiento</a>
                    <a href="<%= Web.Util.Helpers.GetApplicationPath() %>/EPAS/Lista/Determinaciones.aspx" class="breadcrumb-item">Determinaciones</a>
                    <span class="breadcrumb-item active">
                        <asp:Literal ID="ltr_nombreBreadcrumb" runat="server">Agregar/Editar Determinaciones</asp:Literal></span>
                </div>

                <a href="#" class="header-elements-toggle text-default d-md-none"><i class="icon-more"></i></a>
            </div>
        </div>
    </div>

    <div class="row gx-1">

        <div class="col-lg-12"">

            <div class="navbar navbar-expand-xl bg-secondary navbar-dark rounded-top mt-1">
                <div class="container-fluid">                           
                    <div class="nav-item dropdown">
                        <h5 class="mt-2 mb-1 fs-5" data-bs-toggle="tab">Determinaciones - Mantenimiento</h5>
                    </div>                        
                </div>
            </div>

            <!-- Card Cliente -->

            <div class="tab-content border border-1 rounded-bottom p-1">

                <div class="card">
                    <div class="card-body col-lg-5">
                        <div action="#">

                            <div class="row mb-1">
                                <dx:ASPxTextBox ID="TxtID" runat="server" Visible="false" ClientInstanceName="TxtID" CssClass="form-control altura-textbox" AutoCompleteType="Disabled">
                                 </dx:ASPxTextBox>
                                <label class="col-lg-4 col-form-label pe-0 fw-semibold">Determinación N°</label>
                                <div class="col-lg-3 p-0">
                                    <dx:ASPxTextBox ID="Txtcodigo"  CssClass="form-control altura-textbox" AutoCompleteType="Disabled" runat="server">
                                    </dx:ASPxTextBox>
                                </div>
                            </div>

                            <div class="row mb-1">
                                <label class="col-lg-4 col-form-label pe-0 fw-semibold">Descripción</label>
                                <div class="col-lg-6 p-0">
                                    <dx:ASPxTextBox ID="Txtdescripcion"  CssClass="form-control altura-textbox" AutoCompleteType="Disabled" runat="server">
                                    </dx:ASPxTextBox>
                                </div>
                            </div>

                            <div class="row mb-1" >
                                <label class="col-lg-4 col-form-label pe-0 fw-semibold">Unidad</label>
                                <div class="col-lg-6 p-0">
                                    <dx:ASPxTextBox ID="TxtUniPrimaria"  CssClass="form-control altura-textbox" AutoCompleteType="Disabled" runat="server" >
                                    </dx:ASPxTextBox>
                                </div>
                            </div>

                            <div class="row mb-1" style="display:none">
                                <label class="col-lg-4 col-form-label px-0 fw-semibold">Unidad Secundaria</label>
                                <div class="col-lg-6 p-0">
                                    <dx:ASPxTextBox ID="TxtUniSecu"  CssClass="form-control altura-textbox" AutoCompleteType="Disabled" runat="server">
                                    </dx:ASPxTextBox>
                                </div>
                            </div>

                            <div class="row mb-1" style="display:none">
                                <label class="col-lg-3 col-form-label pe-0 fw-semibold">Factor Conversión</label>
                                <div class="col-lg-4 p-0">
                                    <dx:ASPxTextBox ID="TxtFactorConversionPrefijo" ClientInstanceName="txtBarraBusqueda" CssClass="form-control altura-textbox" AutoCompleteType="Disabled" runat="server" >
                                    </dx:ASPxTextBox>
                                </div>
                                <label class="col-lg-2 col-form-label fw-semibold ps-4">x 10 ^</label>
                               
                                <div class="col-lg-4 p-0" style="display:none">
                                    <dx:ASPxTextBox ID="TxtFactorConversionSufijo" ClientInstanceName="txtBarraBusqueda" CssClass="form-control altura-textbox" AutoCompleteType="Disabled" runat="server">
                                    </dx:ASPxTextBox>
                                </div>
                            </div>

                            <div class="row mb-1">
                                <label class="col-lg-4 col-form-label pe-0 fw-semibold">Valor Referencia</label>
                                <div class="col-lg-6 p-0">
                                    <dx:ASPxTextBox ID="TxtValorReferencia" ClientInstanceName="txtBarraBusqueda" CssClass="form-control altura-textbox" AutoCompleteType="Disabled" runat="server">
                                    </dx:ASPxTextBox>
                                </div>
                            </div>

                            <div class="row mb-1">
                                <label class="col-lg-4 col-form-label pe-0 fw-semibold">Decimales a mostrar</label>
                                <div class="col-lg-6 p-0">
                                    <dx:ASPxTextBox ID="TxtDecimales" ClientInstanceName="txtBarraBusqueda" CssClass="form-control altura-textbox" AutoCompleteType="Disabled" runat="server">
                                    </dx:ASPxTextBox>
                                </div>
                            </div>

                             <div class="row mb-1">
                                 <label class="col-lg-2 col-form-label ps-1 pe-0 fw-semibold btn-buscar">Activo</label>
                                 <div class="col-lg-10 p-0">
                                 <dx:ASPxCheckBox ID="CB_Activo" runat="server" ToggleSwitchDisplayMode="Always">
                   
                                     </dx:ASPxCheckBox>

                                     <dx:ASPxLabel ID="lblMsg" runat="server" cssclass="text-danger" ></dx:ASPxLabel>
                                 </div>
                             </div>

                           <%-- <div class="row mb-1">
                                <label class="col-lg-3 col-form-label pe-0 fw-semibold">Método utilizado</label>
                                <div class="col-lg-5 p-0">
                                    <dx:ASPxComboBox ID="CmbMetodos" runat="server" DropDownStyle="DropDownList" NullText="Seleccionar Mètodo" TextFormatString="{0} ({1})" 
                                         Width="100%" CssClass="form-control altura-textbox" IncrementalFilteringMode="Contains" TextField="NombreMetodo" ValueField="NombreMetodo" EnableSynchronization="False">
                                         <ValidationSettings ValidationGroup="confirmarGeneral" Display="Dynamic" ErrorTextPosition="Bottom" ErrorDisplayMode="ImageWithText" SetFocusOnError="true">
                                             <RequiredField IsRequired="true" ErrorText="Campo Obligatorio." />
                                         </ValidationSettings>
                                         <ClientSideEvents SelectedIndexChanged="function(s, e) { OnLocalidadChanged(s); }"/>
                                     </dx:ASPxComboBox> 
                                </div>
                            </div>--%>

							<div class="row mt-3 d-flex justify-content-end">
                                <div class="col-lg-7">
                                    <dx:ASPxButton ID="btnGrabar" runat="server" CssClass="btn btn-secondary fw-semibold px-0 py-1" OnClick="btnGrabar_Click" ClientInstanceName="btnGrabar" AutoPostBack="False" CausesValidation="false" Text="Grabar">
                                        <ClientSideEvents Click="function(s,e) { MostrarLoading(); }" />
                                    </dx:ASPxButton>

                                    <dx:ASPxButton ID="btnVolver" runat="server" CssClass="btn text-white fw-semibold px-0 py-1 ms-3" BackColor="Brown" ClientInstanceName="btnVolver" AutoPostBack="False" CausesValidation="false" Text="Volver" >
                                        <ClientSideEvents Click="function(s,e){ volverButton(); }" />
                                    </dx:ASPxButton>

                                    <dx:ASPxButton ID="btnVolverEditado" runat="server" CssClass="btn text-white fw-semibold px-0 py-1 ms-3" BackColor="Brown" ClientInstanceName="btnVolverEditado" AutoPostBack="False" CausesValidation="false" Text="Volver" >
                                        <ClientSideEvents Click="function(s,e){ volverButtonEditado();}" />
                                    </dx:ASPxButton>
                                </div>
                            </div>                                

                        </div>
                    </div>
                    <div class="card-body col-lg-7"></div>
                </div>

            </div>
        </div>
    </div>

</asp:Content>


<asp:Content ID="Content3" ContentPlaceHolderID="footer" runat="server">
</asp:Content>
