﻿using Domain;
using NHibernate.Mapping;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Web.Config;

namespace Web.EPAS.Lista
{
    public partial class ProtocolosAprobados : System.Web.UI.Page
    {
        protected void Page_Load(object sender,EventArgs e)
        {
            if(!IsPostBack)
            {
                Session["ListaProtocolos"] = new List<Domain.Protocolos>();
            }

            CargarProtocolosAprobados();
        }

        private void CargarProtocolosAprobados()
        {
            try
            {
                List<Domain.Protocolos> protocolos = (List<Domain.Protocolos>) Business.Services.Epas.ProtocolosService.GetAll();
                List<Domain.Protocolos> protocolosAprobados = new List<Domain.Protocolos>();

                if(protocolos != null && protocolos.Count >= 1)
                {
                    foreach(var prot in protocolos)
                    {
                        if(prot.ProtocolosEstados.Codigo == "A")
                        {
                            protocolosAprobados.Add(prot);
                        }
                    }
                }

                if(protocolosAprobados.Count >= 1)
                {
                    gv_ProtocolosAprobados.DataSource = protocolosAprobados;
                    gv_ProtocolosAprobados.DataBind();
                } else
                {
                    gv_ProtocolosAprobados.DataSource = null;
                    // GrVTomador.DataBind();
                }

            } catch(Exception ex)
            {
                ScriptManager.RegisterStartupScript(this,this.GetType(),"ksdfsd4ey1","showMessage('" + ex.Message + "', 'error');",true);
            }
        }
    }
}