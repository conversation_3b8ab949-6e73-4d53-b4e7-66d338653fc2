//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 25/9/2023 14:39:43
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.ComprobantesItems, Domain in the schema.
    /// </summary>
    public partial class ComprobantesItems {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for ComprobantesItems constructor in the schema.
        /// </summary>
        public ComprobantesItems()
        {
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Cantidad in the schema.
        /// </summary>
        public virtual int Cantidad
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for PrecioSinIva in the schema.
        /// </summary>
        public virtual decimal PrecioSinIva
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Iva in the schema.
        /// </summary>
        public virtual decimal Iva
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for SobreTasa in the schema.
        /// </summary>
        public virtual decimal SobreTasa
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Descripcion in the schema.
        /// </summary>
        public virtual string Descripcion
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for IdIVAAlicuota in the schema.
        /// </summary>
        public virtual System.Nullable<decimal> IdIVAAlicuota
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for AlicuotaSobretasa in the schema.
        /// </summary>
        public virtual System.Nullable<decimal> AlicuotaSobretasa
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for DiferenciaIVA in the schema.
        /// </summary>
        public virtual System.Nullable<decimal> DiferenciaIVA
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for DiferenciaSobreTasa in the schema.
        /// </summary>
        public virtual System.Nullable<decimal> DiferenciaSobreTasa
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for IdArticulo in the schema.
        /// </summary>
        public virtual System.Nullable<int> IdArticulo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ComprobantesCabeceras in the schema.
        /// </summary>
        public virtual ComprobantesCabeceras ComprobantesCabeceras
        {
            get;
            set;
        }
    }

}
