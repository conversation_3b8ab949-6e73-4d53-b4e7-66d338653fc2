﻿using Common.DDL;
using DAO;
using Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Contratos {
    public class TelefonoTipoServices {

        public static IList<TelefonoTipo> GetAll() {
            IList<TelefonoTipo> listaAux;
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                listaAux = (IList<TelefonoTipo>) new TelefonoTipoRepository(sess).GetAll();
                sess.Close();
                sess.Dispose();
            }

            return (listaAux);
        }

        public static List<ItemCombo> GetByTipoTel(bool Activo) {
            IList<TelefonoTipo> listaAux;
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                listaAux = (IList<TelefonoTipo>) new TelefonoTipoRepository(sess).GetByTipoTel(Activo);
                sess.Close();
                sess.Dispose();
            }

            ItemCombo item;
            ListaCombo lista = new ListaCombo();
            foreach(TelefonoTipo p in listaAux) {

                item = new ItemCombo();
                item.ID = p.Id.ToString();
                item.Descripcion = p.Tipo;
                lista.Add(item);
            }
            return (lista);
        }

        public static TelefonoTipo getById(int id) {
            TelefonoTipo u;
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                u = new TelefonoTipoRepository(sess).GetByKey(id);

                if(u != null) {
                    //NHibernateUtil.Initialize();

                }
                sess.Close();
                sess.Dispose();
                return u;
            }
        }

        public static void Delete(Domain.TelefonoTipo u) {
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                using(NHibernate.ITransaction tx = sess.BeginTransaction()) {
                    try {
                        new TelefonoTipoRepository(sess).Remove(u);
                        tx.Commit();
                    } catch(Exception e) {
                        tx.Rollback();
                        throw e;
                    } finally {
                        sess.Close();
                        sess.Dispose();
                    }
                }

            }
        }

    }
}
