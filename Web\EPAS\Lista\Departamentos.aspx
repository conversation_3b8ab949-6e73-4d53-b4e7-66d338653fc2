﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Departamentos.aspx.cs" Inherits="Web.EPAS.Lista.Departamentos" %>

<%@ Register Assembly="DevExpress.Web.v18.1" Namespace="DevExpress.Web" TagPrefix="dx" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">

    <script type="text/javascript">

        function OnToolbarItemClick(s, e) {
            if (IsCustomExportToolbarCommand(e.item.name)) {
                e.processOnServer = true;
                e.usePostBack = true;
            } else {

            }
        }
        function IsCustomExportToolbarCommand(command) {
            return command == "CustomExportToXLS" || command == "CustomExportToXLSX";
        }
    </script>

</asp:Content>


<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

    <div class="page-header page-header-light shadow">
        <div class="page-header-content d-lg-flex border-top">
            <div class="d-flex">
                <div class="breadcrumb py-2">
                    <a href="<%= Web.Util.Helpers.GetApplicationPath() %>/Index.aspx" class="breadcrumb-item"><i class="icon-home2 mr-2"></i>Inicio</a>
                    <a href="<%= Web.Util.Helpers.GetApplicationPath() %>/EPAS/Lista/Departamentos.aspx" class="breadcrumb-item"><i class="icon-display me-2"></i>Mantenimiento</a>
                    <span class="breadcrumb-item active">
                        <asp:Literal ID="ltr_nombreBreadcrumb" runat="server">Departamentos</asp:Literal>
                    </span>
                </div>
                <a href="#" class="header-elements-toggle text-default d-md-none"><i class="icon-more"></i></a>
            </div>
        </div>

        <!-- /page header -->

        <div class="row gx-1" runat="server">

            <div class="col-lg-12"">
                <div class="navbar navbar-expand-xl bg-secondary navbar-dark rounded-top mt-1">
                    <div class="container-fluid">                           
                        <div class="nav-item dropdown">
                            <h5 class="mt-2 mb-1 fs-5" data-bs-toggle="tab"">Mantenimiento de Departamentos</h5>
                        </div>                        
                    </div>
                </div>
            </div>

            <!-- GridView Departamentos -->

            <div class="card row gx-1">
                    
                <div class="card-body col-lg-12">
                    <div action="#">
                        <div class="row">

                            <dx:ASPxGridView ID="gv_Dpto" runat="server" CssClass="border border-black rounded" EnableRowsCache="false" ViewStateMode="Disabled" DataSourceForceStandardPaging="false" EnableViewState="false" Font-Size="Small" ClientInstanceName="gv_Dpto" AutoGenerateColumns="False" KeyFieldName="Id">

                                <Toolbars>
                                    <dx:GridViewToolbar ItemAlign="Right" EnableAdaptivity="true">
                                        <Items>
                                            <dx:GridViewToolbarItem Text="Exportar a" Image-IconID="actions_refresh_16x16office2013" DisplayMode="ImageWithText" BeginGroup="true">
                                                <Items>
                                                    <dx:GridViewToolbarItem Command="ExportToPdf" Text="Exportar a PDF" />
                                                    <dx:GridViewToolbarItem Command="ExportToXlsx" Text="Exportar a Excel" />
                                                </Items>
                                            </dx:GridViewToolbarItem>
                                            <dx:GridViewToolbarItem BeginGroup="true">
                                                <Template>
                                                    <dx:ASPxButtonEdit ID="tbToolbarSearch" runat="server" NullText="Buscar..." Height="100%" CssClass="form-control">
                                                        <Buttons>
                                                            <dx:SpinButtonExtended Image-IconID="find_find_16x16gray" />
                                                        </Buttons>
                                                    </dx:ASPxButtonEdit>
                                                </Template>
                                            </dx:GridViewToolbarItem>
                                            <dx:GridViewToolbarItem BeginGroup="true">
                                                <Template>
                                                    <dx:ASPxHyperLink ID="ASPxHyperLink1" runat="server" CssClass="form-control h-100 mt-1 fw-medium rounded-2 text-white bg-info border border-secondary text-decoration-none p-2" Cursor="pointer" Text="Nuevo 📄" Font-Size="Small" Theme="Moderno" NavigateUrl="~/EPAS/AddEdit/AddEditDepartamentos.aspx">
                                                        <ClientSideEvents Click="function(s,e){MostrarLoading();}" />
                                                    </dx:ASPxHyperLink>
                                                </Template>
                                            </dx:GridViewToolbarItem>
                                        </Items>
                                    </dx:GridViewToolbar>
                                </Toolbars>
                                <SettingsSearchPanel CustomEditorID="tbToolbarSearch" />

                                <SettingsExport EnableClientSideExportAPI="true" ExcelExportMode="DataAware" />
                                <ClientSideEvents ToolbarItemClick="OnToolbarItemClick" />
                                <SettingsBehavior AllowFocusedRow="true" FilterRowMode="Auto" AllowDragDrop="false" AllowEllipsisInText="true" />
                                <SettingsPager PageSize="20" AlwaysShowPager="true"></SettingsPager>
                                  
                                <Columns>
                                    <dx:GridViewDataTextColumn FieldName="Id" Visible="false" CellStyle-Border-BorderColor="Black" CellStyle-HorizontalAlign="Center" >   
                                    </dx:GridViewDataTextColumn>

                                    <dx:GridViewDataTextColumn FieldName="Codigo" caption="Código" CellStyle-Border-BorderColor="Black" CellStyle-HorizontalAlign="Center" Width="100">
                                        <Settings AllowAutoFilter="true" />
                                        <HeaderStyle HorizontalAlign="Center" />
                                    </dx:GridViewDataTextColumn>

                                    <dx:GridViewDataTextColumn FieldName="Descripcion" caption="Descripción" CellStyle-Border-BorderColor="Black" CellStyle-HorizontalAlign="Center" >
                                        <Settings AllowAutoFilter="true" />
                                        <HeaderStyle HorizontalAlign="Center" />
                                    </dx:GridViewDataTextColumn>

                                    <dx:GridViewDataTextColumn FieldName="Provincias.Descripcion" caption="Provincia" CellStyle-Border-BorderColor="Black" CellStyle-HorizontalAlign="Center">
                                        <DataItemTemplate>
                                             
                                            <dx:ASPxLabel ID="ASPxLabel1423" runat="server" Text='<%#Eval("Provincias.Descripcion").ToString() %>' />

                                        </DataItemTemplate>
                                        <Settings AllowAutoFilter="true" />
                                        <HeaderStyle HorizontalAlign="Center" />
                                    </dx:GridViewDataTextColumn>

                                    <dx:GridViewDataTextColumn Caption="Activo" FieldName="Activo" Visible="true" HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="true" CellStyle-HorizontalAlign="Center" Width="100" >
                                        <Settings AllowAutoFilter="false" />
                                        <DataItemTemplate>
                                            <dx:ASPxImage ID="ASPxImage1" runat="server" Visible='<%# Eval("Activo")  %>' ShowLoadingImage="true" ImageUrl="~/App_Themes/Tema1/img/check.png" Width="30px"></dx:ASPxImage>
                                            <dx:ASPxImage ID="ASPxImage2" runat="server" Visible='<%# !Convert.ToBoolean(Eval("Activo"))  %>' ShowLoadingImage="true" ImageUrl="~/App_Themes/Tema1/img/no.png" Width="30px"></dx:ASPxImage>                                             
                                        </DataItemTemplate>
                                    </dx:GridViewDataTextColumn>

                                    <dx:GridViewDataTextColumn Caption="" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center" Width="100">
                                        <DataItemTemplate>
                                            <dx:ASPxButton ID="btnEditar" runat="server" RenderMode="Link" Text="Editar" OnClick="btnEditar_Click" CssClass="fw-semibold text-decoration-none text-secondary" Font-Size="Small" Theme="Moderno" CommandArgument='<%# Eval("Id")%>' AutoPostBack="false">
                                                <ClientSideEvents Click="function (s,e){MostrarLoading();}" />
                                            </dx:ASPxButton>
                                        </DataItemTemplate>
                                    </dx:GridViewDataTextColumn>
                                        
                                    <dx:GridViewDataTextColumn Caption="" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center" Width="12%">
                                        <DataItemTemplate>
                                            <dx:ASPxButton ID="btnAnularActivar" runat="server" style="color:indianred" CssClass="fw-semibold text-decoration-none" RenderMode="Link" Text="Anular/Activar" OnClick="btnAnularActivar_Click" CommandArgument='<%# Eval("Id" )%>' AutoPostBack="false">
                                                <ClientSideEvents Click="function(s,e){e.processOnServer ==confirm('¿Desea Anular/Activar?');}" />
                                            </dx:ASPxButton>
                                        </DataItemTemplate>
                                    </dx:GridViewDataTextColumn>

                                </Columns>
                            </dx:ASPxGridView>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


</asp:Content>


<asp:Content ID="Content3" ContentPlaceHolderID="footer" runat="server">
</asp:Content>
