﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="AddEditContrato.aspx.cs" Inherits="Web.Clientes.AddEditContrato" %>

<%@ Register Assembly="DevExpress.Web.v18.1" Namespace="DevExpress.Web" TagPrefix="dx" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">

    <script type="text/javascript">

        function validarGuardarPrincipal(s, e) {
            if (ASPxClientEdit.ValidateGroup('confirmarGeneral')) {
                LoadingPanel.Show();
                return true;
            } else {
                return false;

            }
        }

        function RedireccionarAPaginaPrincipal() {
            window.location.href = "/SIGeLab/Index.aspx";
            LoadingPanel.Show();
        }

        function toggleText(tipoDoc) {
            var docDefault = document.getElementById('<%= nroDocDefault.ClientID %>');
            var dni = document.getElementById('<%= nroDni.ClientID %>');
            var ci = document.getElementById('<%= nroCi.ClientID %>');
            var cuit = document.getElementById('<%= nroCuit.ClientID %>');
            var cuil = document.getElementById('<%= nroCuil.ClientID %>');
            var pasaporte = document.getElementById('<%= nroPasaporte.ClientID %>');

            switch (tipoDoc) {

                case 'DNI':
                    docDefault.style.display = 'none';
                    dni.style.display = 'flex';
                    ci.style.display = 'none';
                    cuit.style.display = 'none';
                    cuil.style.display = 'none';
                    pasaporte.style.display = 'none';

                    break;

                case 'CI':
                    docDefault.style.display = 'none';
                    dni.style.display = 'none';
                    ci.style.display = 'flex';
                    cuit.style.display = 'none';
                    cuil.style.display = 'none';
                    pasaporte.style.display = 'none';

                    break;

                case 'CUIT':
                    docDefault.style.display = 'none';
                    dni.style.display = 'none';
                    ci.style.display = 'none';
                    cuit.style.display = 'flex';
                    cuil.style.display = 'none';
                    pasaporte.style.display = 'none';

                    break;

                case 'CUIL':
                    docDefault.style.display = 'none';
                    dni.style.display = 'none';
                    ci.style.display = 'none';
                    cuit.style.display = 'none';
                    cuil.style.display = 'flex';
                    pasaporte.style.display = 'none';

                    break;

                case 'PAS':
                    docDefault.style.display = 'none';
                    dni.style.display = 'none';
                    ci.style.display = 'none';
                    cuit.style.display = 'none';
                    cuil.style.display = 'none';
                    pasaporte.style.display = 'flex';

                    break;

                default:
                    docDefault.style.display = 'flex';
                    dni.style.display = 'none';
                    ci.style.display = 'none';
                    cuit.style.display = 'none';
                    cuil.style.display = 'none';
                    pasaporte.style.display = 'none';

                    break;
            };
        };

        function toggleTextContacto(tipoDoc) {
            var docDefaultContacto = document.getElementById('<%= nroDocDefaultContacto.ClientID %>');
            var dniContacto = document.getElementById('<%= nroDniContacto.ClientID %>');
            var ciContacto = document.getElementById('<%= nroCiContacto.ClientID %>');
            var cuilContacto = document.getElementById('<%= nroCuilContacto.ClientID %>');
            var cuitContacto = document.getElementById('<%= nroCuitContacto.ClientID %>');
            var pasaporteContacto = document.getElementById('<%= nroPasaporteContacto.ClientID %>');

            switch (tipoDoc) {
                case 'DNI':
                    docDefaultContacto.style.display = 'none';
                    dniContacto.style.display = 'flex';
                    ciContacto.style.display = 'none';
                    cuilContacto.style.display = 'none';
                    cuitContacto.style.display = 'none';
                    pasaporteContacto.style.display = 'none';

                    break;

                case 'CI':
                    docDefaultContacto.style.display = 'none';
                    dniContacto.style.display = 'none';
                    ciContacto.style.display = 'flex';
                    cuilContacto.style.display = 'none';
                    cuitContacto.style.display = 'none';
                    pasaporteContacto.style.display = 'none';

                    break;

                case 'CUIL':
                    docDefaultContacto.style.display = 'none';
                    dniContacto.style.display = 'none';
                    ciContacto.style.display = 'none';
                    cuilContacto.style.display = 'flex';
                    cuitContacto.style.display = 'none';
                    pasaporteContacto.style.display = 'none';

                    break;

                case 'CUIT':
                    docDefaultContacto.style.display = 'none';
                    dniContacto.style.display = 'none';
                    ciContacto.style.display = 'none';
                    cuilContacto.style.display = 'none';
                    cuitContacto.style.display = 'flex';
                    pasaporteContacto.style.display = 'none';

                    break;

                case 'PAS':
                    docDefaultContacto.style.display = 'none';
                    dniContacto.style.display = 'none';
                    ciContacto.style.display = 'none';
                    cuilContacto.style.display = 'none';
                    cuitContacto.style.display = 'none';
                    pasaporteContacto.style.display = 'flex';

                    break;

                default:
                    docDefaultContacto.style.display = 'flex';
                    dniContacto.style.display = 'none';
                    ciContacto.style.display = 'none';
                    cuilContacto.style.display = 'none';
                    cuitContacto.style.display = 'none';
                    pasaporteContacto.style.display = 'none';

                    break;
            };
        };

        document.addEventListener("DOMContentLoaded", function () {
            var docPredefinido = ASPxClientComboBox.GetControlCollection().GetByName("<%= cmbTipoDoc.ClientID %>");

            if (docPredefinido) {

                if (docPredefinido.GetValue() !== null) {

                    docPredefinido.TextChanged.FireEvent(docPredefinido);
                }
            }
        });

        //function EliminarContacto(s, e) {

        //    BtnEliminar_Callback.PerformCallback();
        //    LoadingPanel.Hide();

        //    Swal.fire({
        //        title: 'Esta seguro que desea eliminar este contacto?',
        //        showCancelButton: true,
        //        confirmButtonColor: '#3085d6',
        //        cancelButtonColor: '#d33',
        //        confirmButtonText: 'Si, eliminar.'
        //    }).then((result) => {

        //        if (result.isConfirmed) {


        //            Swal.fire({
        //                title: 'Se eliminó el contacto con éxito!',
        //                confirmButtonColor: '#3085d6',
        //                confirmButtonText: 'OK'
        //            })
        //        };
        //    });
        //}

        function ValidateContactoOnClick(s, e) {
            if (ASPxClientEdit.ValidateGroup('datosContacto')) {
                LoadingPanel.Show();
                BtnAgregar_Callback.PerformCallback('datosContacto');

                txtNombreContacto.SetText();
                txtApellidoContacto.SetText();
                cmbTipoDocContacto.SetSelectedIndex(-1);
                txtDniContacto.SetText();
                txtCuilContacto.SetText();
                txtPasaporteContacto.SetText();
                txtCuitContacto.SetText();
                txtCiContacto.SetText();
                cmbSexoContacto.SetSelectedIndex(-1);
                txtEmailContacto.SetText();
                cmbTelefonoContacto.SetSelectedIndex(-1);
                txtTelefonoContacto.SetText();

                return true;
            } else {
                return false;
            };
        };

        //document.addEventListener('keydown', function (event) {

        //    if (event.ctrlKey && event.key === '1') {
        //        document.getElementById('agregarContacto').click();
        //        document.getElementById('contacto').focus();

        //    } else if (event.ctrlKey && event.key === '0') {
        //        document.getElementById('cancelarContacto').click();
        //        document.getElementById('cancelarContacto').focus();
        //    }

        //});

        function removerFocus() {
            var enlace = document.getElementById('contactoFocus');
            enlace.blur();
        };

        document.addEventListener('keydown', function (event) {
            if (event.key === 'Tab') {
                removerFocus();
            };
        });

        $(document).ready(function () {
            $('#agregarContacto').click(function () {
                $('#camposAgregarContacto').show();
                $('#cancelarContacto').show();
                $('#agregarContacto').hide();
            });

            $('#cancelarContacto').click(function () {
                $('#camposAgregarContacto').hide();
                $('#cancelarContacto').hide();
                $('#agregarContacto').show();
            });
        });

        //function OnBirthdayValidation(s, e) {
        //    var birthday = e.value;
        //    if (!birthday)
        //        return;
        //    var date = new Date();
        //    var msecPerYear = 1000 * 60 * 60 * 24 * 365;
        //    var years = (date.getTime() - 18) / msecPerYear;
        //    if (years < 18) {
        //        e.isValid = false;
        //        e.errorText = "El contacto debe ser mayor a 18 años.";
        //    };
        //};

        function ValidateOnClick(s, e) {
            if (ASPxClientEdit.ValidateEditorsInContainer(null)) {
                LoadingPanel.Show();
                return true;
            } else {
                return false;
            };
        };

        function onLastNameValidation(s, e) {
            var name = e.value;
            if (name == null)
                return;
            if (name.length < 2)
                e.isValid = false;
        };

        function onTelefonoValidation(s, e) {
            var numero = e.value;
            var regexNumero = /^[0-9]{10}$/;
            /*var telefonoCorrecto = document.getElementById('telefonoCorrecto');
            var telefonoIncorrecto = document.getElementById('telefonoIncorrecto');

            if (regexNumero.test(numero)) {
                telefonoCorrecto.style.display = 'block';
                telefonoIncorrecto.style.display = 'none';

            } else {
                telefonoCorrecto.style.display = 'none';
                telefonoIncorrecto.style.display = 'block';
            };

            if (numero === null) {
                telefonoCorrecto.style.display = 'none';
                telefonoIncorrecto.style.display = 'none';
            };*/
        };

        var getProvincias = null;

        function OnProvinciasChanged(prov) {

            if (cmbLocalidades.InCallback()) {

                getProvincias = prov.GetValue().toString();
                LoadingPanel.Hide();
            }
            else {

                cmbLocalidades.PerformCallback(prov.GetValue().toString());
            }
        };

        var getLocalidades = null;
        function OnLocalidadesChanged(loc) {

            if (cmbCalles.InCallback()) {

                getLocalidades = loc.GetValue().toString();
                LoadingPanel.Hide();
            }
            else {

                cmbCalles.PerformCallback(loc.GetValue().toString());
            }
        }

        function OnEndCallback(s, e) {

        };

        //var isResetRequired = false;
        //function onSelectedTipoDocChanged(s, e) {
        //    isResetRequired = true;
        //    GrVContacto.GetEditor("CityId").PerformCallback(s.GetValue());
        //}

    </script>

</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

    <div class="page-header page-header-light shadow">
        <div class="page-header-content d-lg-flex border-top">
            <div class="d-flex">
                <div class="breadcrumb py-2">
                    <a href="<%= Web.Util.Helpers.GetApplicationPath() %>/Index.aspx" class="breadcrumb-item"><i class="icon-home2 mr-2"></i>Inicio</a>
                    <a href="<%= Web.Util.Helpers.GetApplicationPath() %>/Clientes/AddEditContrato.aspx" class="breadcrumb-item"><i class="icon-display me-2"></i>Solicitante</a>
                    <span class="breadcrumb-item active">
                        <asp:Literal ID="ltr_nombreBreadcrumb" runat="server">Agregar/Editar Solicitante</asp:Literal>
                    </span>
                </div>
                <a href="#" class="header-elements-toggle text-default d-md-none"><i class="icon-more"></i></a>
            </div>
        </div>

        <div class="row gx-1" runat="server">

            <div class="col-lg-12 navbar navbar-expand-xl bg-secondary navbar-dark rounded-top mt-1 py-0" id="datosFacturacion">
                <div class="container-fluid">
                    <div class="nav-item dropdown">
                        <span class="breadcrumb-item active mt-2 mb-1 fs-5 fw-medium">
                            <asp:Literal ID="ltr_DatosContrato" runat="server">Nuevo o Editar</asp:Literal>
                        </span>
                    </div>
                </div>
            </div>


            <!-- Datos Titular -->

            <div class="row gx-3">

                <div class="card col-lg-12">

                    <div class="row gx-1">

                        <!-- Campos de Solicitantes -->

                        <div class="card-body col-lg-5">
                            <div action="#">

                                <div class="row mb-1" id="nroSolicitante" runat="server">
                                    <label class="col-lg-3 col-form-label fw-semibold">Solicitante N°</label>
                                    <div class="col-lg-2 ps-0 pe-3">
                                        <dx:ASPxTextBox ID="txtNroContrato" ReadOnly="true" runat="server" NullText="-" ClientInstanceName="txtNroContrato" Width="100%" AutoCompleteType="Disabled" CssClass="form-control altura-textbox">
                                        </dx:ASPxTextBox>
                                    </div>
                                </div>

                                <div class="row mb-1">
                                    <label class="col-lg-3 col-form-label pe-0 fw-semibold btn-buscar">Tipo Solicitante</label>
                                    <div class="col-lg-6 p-0">

                                        <dx:ASPxComboBox ID="cmbTipoSolicitante" ClientInstanceName="cmbIVACondiciones" runat="server" ClientEnabled="true" DropDownStyle="DropDownList" TextFormatString="{0} ({1})" Width="100%" CssClass="form-control altura-textbox" IncrementalFilteringMode="Contains" EnableSynchronization="False">
                                            <ValidationSettings ValidationGroup="confirmarGeneral" Display="Dynamic" ErrorTextPosition="Bottom" ErrorDisplayMode="ImageWithText" SetFocusOnError="true">
                                                <RequiredField IsRequired="true" ErrorText="Campo Obligatorio." />
                                            </ValidationSettings>
                                        </dx:ASPxComboBox>

                                    </div>
                                    <p class="col-lg-1 text-danger fw-medium fs-5 pt-1">•</p>
                                </div>

                                <div class="row mb-1">
                                    <label class="col-lg-3 col-form-label fw-semibold">Nombre</label>
                                    <div class="col-lg-6 p-0">
                                        <dx:ASPxTextBox ID="txtNombre" runat="server" EnableClientSideAPI="True" Width="100%" AutoCompleteType="Disabled" CssClass="form-control altura-textbox" EncodeHtml="false">
                                            <ValidationSettings ValidationGroup="confirmarGeneral" SetFocusOnError="True" ErrorText="El Nombre debe tener al menos <br /> 2 caracteres!" Display="Dynamic" ErrorTextPosition="Bottom">
                                                <RequiredField IsRequired="True" ErrorText="Campo Obligatorio." />
                                            </ValidationSettings>
                                            <ClientSideEvents Validation="onLastNameValidation" />
                                        </dx:ASPxTextBox>
                                    </div>
                                    <p class="col-lg-1 text-danger fw-medium fs-5 pt-1">•</p>
                                </div>

                                <div class="row mb-1">
                                    <label class="col-lg-3 col-form-label pe-0 fw-semibold btn-buscar">Condición IVA</label>
                                    <div class="col-lg-6 p-0">

                                        <dx:ASPxComboBox ID="cmbIVACondiciones" ClientInstanceName="cmbIVACondiciones" runat="server" ClientEnabled="true" DropDownStyle="DropDownList" TextFormatString="{0} ({1})" Width="100%" CssClass="form-control altura-textbox" IncrementalFilteringMode="Contains" EnableSynchronization="False">
                                            <ValidationSettings ValidationGroup="confirmarGeneral" Display="Dynamic" ErrorTextPosition="Bottom" ErrorDisplayMode="ImageWithText" SetFocusOnError="true">
                                                <RequiredField IsRequired="true" ErrorText="Campo Obligatorio." />
                                            </ValidationSettings>
                                        </dx:ASPxComboBox>

                                    </div>
                                    <p class="col-lg-1 text-danger fw-medium fs-5 pt-1">•</p>
                                </div>

                                <div class="row mb-1">
                                    <label class="col-lg-3 col-form-label pe-0 fw-semibold">Doc. Tipo</label>
                                    <div class="col-lg-6 p-0">
                                        <dx:ASPxComboBox ID="cmbTipoDoc" ClientInstanceName="cbmTipoDoc" runat="server" ClientEnabled="true" DropDownStyle="DropDownList" NullText="Seleccionar Documento" TextFormatString="{0} ({1})" Width="100%" CssClass="form-control altura-textbox" IncrementalFilteringMode="Contains" EnableSynchronization="False">
                                            <ValidationSettings ValidationGroup="confirmarGeneral" Display="Dynamic" ErrorTextPosition="Bottom" ErrorDisplayMode="ImageWithText" SetFocusOnError="true">
                                                <RequiredField IsRequired="true" ErrorText="Campo Obligatorio." />
                                            </ValidationSettings>
                                            <ClientSideEvents TextChanged="function(s, e) { toggleText(s.GetSelectedItem().text) }" />
                                        </dx:ASPxComboBox>
                                    </div>
                                    <p class="col-lg-1 text-danger fw-medium fs-5 pt-1">•</p>
                                </div>

                                <!--Número de Doc Default -->

                                <div class="row mb-1" id="nroDocDefault" runat="server">
                                    <label class="col-lg-3 pe-0 col-form-label fw-semibold">Nro.</label>
                                    <div class="col-lg-2 p-0">
                                        <dx:ASPxTextBox ID="txtDocDefault" ClientInstanceName="txtDocDefault" CssClass="rounded-2" NullText="###" Height="41px" Width="130px" ReadOnly="true" Cursor="end" runat="server">
                                            <ValidationSettings ValidationGroup="confirmarGeneral" Display="Dynamic" ErrorTextPosition="Bottom" ErrorDisplayMode="ImageWithText" SetFocusOnError="true">
                                                <RequiredField IsRequired="true" ErrorText="Campo Obligatorio." />
                                            </ValidationSettings>
                                        </dx:ASPxTextBox>
                                    </div>
                                    <p class="col-lg-1 text-danger fw-medium fs-5 pt-1 ms-4"></p>

                                    <div class="col-lg-1 ps-1 form-control-feedback form-control-feedback-start flex-grow-1">
                                        <dx:ASPxButton ID="btnDocDefault" runat="server" Enabled="false" CssClass="btn btn-secondary px-0 py-1 me-0 invisible" Text="🔎">
                                        </dx:ASPxButton>
                                    </div>
                                </div>

                                <!--Número de DNI -->

                                <div class="row mb-1" id="nroDni" runat="server" style="display: none">
                                    <label id="lblDni" class="col-lg-3 pe-0 col-form-label fw-semibold">DNI Nro.</label>
                                    <div class="col-lg-2 p-0">
                                        <dx:ASPxTextBox ID="txtDni" CssClass="rounded-2" Height="41px" Width="130px" AutoCompleteType="Disabled" Cursor="end" runat="server" ClientInstanceName="txtDni">
                                            <MaskSettings Mask="00000000" PromptChar="#" IncludeLiterals="None" ErrorText="Faltan digitos! Complete con cero si hace falta." />
                                            <ValidationSettings ValidationGroup="confirmarGeneral" ValidateOnLeave="false" ErrorDisplayMode="ImageWithText" Display="Dynamic" SetFocusOnError="True" ErrorTextPosition="Bottom">
                                                <RequiredField IsRequired="True" ErrorText="Campo Obligatorio." />
                                            </ValidationSettings>
                                        </dx:ASPxTextBox>
                                        <clientsideevents click="onLastNameValidation" />
                                    </div>
                                    <p class="col-lg-1 text-danger fw-medium fs-5 pt-1 ms-4 ps-3">•</p>

                                    <div class="col-lg-1 ps-1 form-control-feedback form-control-feedback-start flex-grow-1">
                                        <dx:ASPxButton ID="btnBuscarDni" runat="server" CssClass="btn btn-secondary px-0 py-1 me-0 invisible" ClientInstanceName="btnBuscarDni" AutoPostBack="False" CausesValidation="false" Text="🔎">
                                            <ClientSideEvents Click="function(s,e){ValidateOnClick(s,e);}" />
                                        </dx:ASPxButton>
                                    </div>
                                </div>

                                <!--Número de CI Contacto -->

                                <div class="row mb-1" id="nroCi" runat="server" style="display: none">
                                    <label class="col-lg-3 pe-0 col-form-label fw-semibold">CI Nro.</label>
                                    <div class="col-lg-2 p-0">
                                        <dx:ASPxTextBox ID="txtCi" CssClass="rounded-2" Height="41px" Width="130px" Cursor="end" AutoCompleteType="Disabled" runat="server" ClientInstanceName="txtCi">
                                            <MaskSettings Mask="00000000" PromptChar="#" IncludeLiterals="None" ErrorText="Faltan digitos!" />

                                            <ValidationSettings ValidationGroup="datosContacto" ErrorDisplayMode="ImageWithText" Display="Dynamic" SetFocusOnError="True" ErrorTextPosition="Bottom">
                                                <RequiredField IsRequired="True" ErrorText="Campo Obligatorio." />
                                            </ValidationSettings>
                                        </dx:ASPxTextBox>
                                    </div>
                                    <p class="col-lg-1 text-danger fw-medium fs-5 pt-1 ms-4 ps-3">•</p>

                                    <div class="col-lg-1 ps-1 form-control-feedback form-control-feedback-start flex-grow-1">
                                        <dx:ASPxButton ID="btnBuscarCi" CssClass="btn btn-secondary px-0 py-1 me-0 invisible" AutoPostBack="False" ClientInstanceName="btnBuscarCi" CausesValidation="false" runat="server" Text="🔎">
                                            <ClientSideEvents Click="function(s,e){ValidateOnClick(s,e);}" />
                                        </dx:ASPxButton>
                                    </div>
                                </div>

                                <!--Número de CUIT -->

                                <div class="row mb-1" id="nroCuit" runat="server" style="display: none">
                                    <label class="col-lg-3 pe-0 col-form-label fw-semibold">CUIT Nro.</label>
                                    <div class="col-lg-2 p-0">
                                        <dx:ASPxTextBox ID="txtCuit" CssClass="rounded-2 py-1" Width="130px" Cursor="end" AutoCompleteType="Disabled" ClientInstanceName="txtCuit" runat="server">
                                            <MaskSettings Mask="00-00000000-0" PromptChar="#" IncludeLiterals="None" ErrorText="Faltan digitos! Debe completar todos los caracteres." />
                                            <ValidationSettings ValidationGroup="confirmarGeneral" Display="Dynamic" SetFocusOnError="True" ErrorTextPosition="Bottom">
                                                <RequiredField IsRequired="True" ErrorText="Campo Obligatorio." />
                                            </ValidationSettings>
                                        </dx:ASPxTextBox>
                                    </div>
                                    <p class="col-lg-1 text-danger fw-medium fs-5 pt-1 ms-4 ps-3">•</p>

                                    <div class="col-lg-1 ps-1 form-control-feedback form-control-feedback-start flex-grow-1">
                                        <dx:ASPxButton ID="btnBuscarCuit" runat="server" CssClass="btn btn-secondary px-0 py-1 me-0 invisible" ClientInstanceName="btnBuscarCuit" AutoPostBack="False" CausesValidation="false" Text="🔎">
                                            <ClientSideEvents Click="function(s,e){ValidateOnClick(s,e);}" />
                                        </dx:ASPxButton>
                                    </div>
                                </div>

                                <!--Número de CUIL -->

                                <div class="row mb-1" id="nroCuil" runat="server" style="display: none">
                                    <label class="col-lg-3 pe-0 col-form-label fw-semibold">CUIL Nro.</label>
                                    <div class="col-lg-2 p-0">
                                        <dx:ASPxTextBox ID="txtCuil" CssClass="rounded-2" Height="41px" Width="130px" Cursor="end" AutoCompleteType="Disabled" runat="server" ClientInstanceName="txtCuil">
                                            <MaskSettings Mask="00-00000000-0" PromptChar="#" IncludeLiterals="None" ErrorText="Faltan digitos! Debe completar todos los caracteres." />
                                            <ValidationSettings ValidationGroup="confirmarGeneral" ErrorDisplayMode="ImageWithText" Display="Dynamic" SetFocusOnError="True" ErrorTextPosition="Bottom">
                                                <RequiredField IsRequired="True" ErrorText="Campo Obligatorio." />
                                            </ValidationSettings>
                                        </dx:ASPxTextBox>
                                        <clientsideevents click="function(s,e){ValidateOnClick(s,e);}" />
                                    </div>
                                    <p class="col-lg-1 text-danger fw-medium fs-5 pt-1 ms-4 ps-3">•</p>

                                    <div class="col-lg-1 ps-1 form-control-feedback form-control-feedback-start flex-grow-1">
                                        <dx:ASPxButton ID="btnBuscarCuil" runat="server" CssClass="btn btn-secondary px-0 py-1 me-0 invisible" ClientInstanceName="btnBuscarCuil" AutoPostBack="False" CausesValidation="false" Text="🔎">
                                            <ClientSideEvents Click="function(s,e){ValidateOnClick(s,e);}" />
                                        </dx:ASPxButton>
                                    </div>
                                </div>

                                <!--Número de Pasaporte -->

                                <div class="row mb-1" id="nroPasaporte" runat="server" style="display: none">
                                    <label class="col-lg-3 pe-0 col-form-label fw-semibold">Pasaporte Nro.</label>
                                    <div class="col-lg-2 p-0">
                                        <dx:ASPxTextBox ID="txtPasaporte" CssClass="rounded-2" NullText="AAA-000000" Height="41px" Width="130px" Cursor="end" AutoCompleteType="Disabled" runat="server" ClientInstanceName="txtPasaporte">
                                            <ValidationSettings ValidationGroup="confirmarGeneral" ErrorDisplayMode="ImageWithText" Display="Dynamic" SetFocusOnError="True" ErrorTextPosition="Bottom">
                                                <RequiredField IsRequired="True" ErrorText="Campo Obligatorio." />
                                            </ValidationSettings>
                                        </dx:ASPxTextBox>
                                        <clientsideevents click="function(s,e){ValidateOnClick(s,e);}" />
                                    </div>
                                    <p class="col-lg-1 text-danger fw-medium fs-5 pt-1 ms-4 ps-3">•</p>

                                    <div class="col-lg-1 ps-1 form-control-feedback form-control-feedback-start flex-grow-1">
                                        <dx:ASPxButton ID="btnBuscarPasaporte" runat="server" CssClass="btn btn-secondary px-0 py-1 me-0 invisible" ClientInstanceName="btnBuscarPasaporte" AutoPostBack="False" CausesValidation="false" Text="🔎">
                                            <ClientSideEvents Click="function(s,e){ValidateOnClick(s,e);}" />
                                        </dx:ASPxButton>
                                    </div>
                                </div>

                                <%--<div class="row mb-1">
                                    <label class="col-lg-3 pe-0 col-form-label fw-semibold">Resultados</label>
                                    <div class="col-lg-6 p-0">                                        
                                        <dx:ASPxComboBox ID="Drop_Resultados" runat="server" DropDownStyle="DropDownList" ValueType="System.Int32" TextFormatString="{0} ({1})" Width="100%" CssClass="form-control altura-textbox" IncrementalFilteringMode="Contains">
                                        </dx:ASPxComboBox>
                                    </div>                                
                                </div>--%>
                            </div>

                            <div class="row mb-1">
                                <label class="col-lg-3 col-form-label fw-semibold">Es Muestreador</label>
                                <div class="col-lg-1 px-0">
                                    <dx:ASPxCheckBox ID="chbEsTomador" CssClass="mt-1" Theme="Moderno" runat="server" ClientInstanceName="chbEsTomador">
                                    </dx:ASPxCheckBox>
                                </div>
                            </div>

                            <div class="col-lg-4 mt-5 fw-semibold" id="agregarContacto">
                                <a id="contactoFocus" href="#contacto" onfocus="removerFocus()" class="cursor-pointer text-secondary"><span class="border-warning border-bottom border-width-2">Agregar Contacto</span><span class="fs-xs fw-semibold text-black invisible">(1)</span></a>
                            </div>
                            <div class="col-lg-4 px-0 mt-5 fw-semibold" id="cancelarContacto" style="display: none">
                                <a href="#" class="cursor-pointer text-warning"><span class="border-secondary border-bottom border-width-2">Cancelar Contacto</span><span class="fs-xs fw-semibold text-black invisible">(0)</span></a>
                            </div>

                        </div>

                        <div class="card-body col-lg-7">
                            <div action="#">

                                <div class="row mb-1">
                                    <label class="col-lg-2 col-form-label fw-semibold">Razón Social</label>
                                    <div class="col-lg-6 p-0">
                                        <dx:ASPxTextBox ID="txtRazonSocial" runat="server" EnableClientSideAPI="True" Width="100%" AutoCompleteType="Disabled" CssClass="form-control altura-textbox" EncodeHtml="false">
                                            <ValidationSettings ValidationGroup="confirmarGeneral" SetFocusOnError="True" ErrorText="La Razón Social debe tener <br /> al menos 2 caracteres!" Display="Dynamic" ErrorTextPosition="Bottom">
                                                <RequiredField IsRequired="True" ErrorText="Campo Obligatorio." />
                                            </ValidationSettings>
                                            <ClientSideEvents Validation="onLastNameValidation" />
                                        </dx:ASPxTextBox>
                                    </div>
                                    <p class="col-lg-1 text-danger fw-medium fs-5 pt-1">•</p>
                                </div>

                                <div class="row mb-1">
                                    <label class="col-lg-2 col-form-label fw-semibold">E-mail</label>
                                    <div class="col-lg-6 p-0">
                                        <dx:ASPxTextBox ID="txtEmail" CssClass="form-control altura-textbox" AutoCompleteType="Disabled" NullText="<EMAIL>" Width="100%" runat="server">
                                            <ValidationSettings ValidationGroup="confirmarGeneral" Display="Dynamic" ErrorTextPosition="Bottom">
                                                <RequiredField IsRequired="True" ErrorText="Campo Obligatorio." />
                                                <RegularExpression ValidationExpression="\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*"
                                                    ErrorText="E-mail inválido." />
                                            </ValidationSettings>
                                        </dx:ASPxTextBox>
                                    </div>
                                    <p class="col-lg-1 w-32px text-danger fw-medium fs-5 pt-1 pe-0">•</p>
                                </div>

                                <div class="row mb-1">
                                    <label class="col-lg-2 col-form-label fw-semibold">E-mail Alter.</label>
                                    <div class="col-lg-6 p-0">
                                        <dx:ASPxTextBox ID="txtEmailAlternativo" AutoCompleteType="Disabled" CssClass="form-control altura-textbox" NullText="<EMAIL>" Width="100%" runat="server">
                                            <ValidationSettings Display="Dynamic" ErrorTextPosition="Bottom">
                                                <RegularExpression ValidationExpression="\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*" ErrorText="E-mail inválido." />
                                            </ValidationSettings>
                                        </dx:ASPxTextBox>
                                    </div>
                                </div>

                                <div class="row mb-1">
                                    <label class="col-lg-2 col-form-label pe-0 fw-semibold btn-buscar">Teléfono</label>
                                    <div class="col-lg-2 p-0">
                                        <dx:ASPxComboBox ID="cmbTelefono" ClientInstanceName="cmbTelefono" runat="server" DropDownStyle="DropDownList" 
                                            TextFormatString="{0} ({1})" Width="125px" CssClass="form-control altura-textbox" IncrementalFilteringMode="Contains" 
                                            EnableSynchronization="False">
                                              <ValidationSettings ValidationGroup="datosContacto" Display="Dynamic" ErrorTextPosition="Bottom" ErrorDisplayMode="ImageWithText" SetFocusOnError="true">
  </ValidationSettings>
                                        </dx:ASPxComboBox>
                                    </div>
                                       <p class="col-lg-1 text-danger fw-medium fs-5 pt-1"></p>

                                    <label class="col-lg-1 col-form-label pe-0 fw-semibold btn-buscar">Nro.</label>
                                    <div class="col-lg-2 p-0">
                                        <dx:ASPxTextBox ID="txtTelefono" CssClass="rounded-2" Height="41px" Width="100%" Cursor="end" runat="server" AutoCompleteType="Disabled"
                                            MaxLength="11"
                                            ClientInstanceName="txtTelefono">
                                          
                                              <ValidationSettings Display="Dynamic" SetFocusOnError="True" ErrorTextPosition="Right">
  </ValidationSettings>
  <ClientSideEvents Validation="onTelefonoValidation" />
                                        </dx:ASPxTextBox>
                                    </div>

                                </div>

                                <div class="row mb-1">
                                    <label class="col-lg-2 col-form-label pe-0 fw-semibold btn-buscar">Tel. Alternativo</label>
                                    <div class="col-lg-2 p-0">
                                        <dx:ASPxComboBox ID="cmbTelefonoAlternativo" ClientInstanceName="cmbTelefonoAlternativo" runat="server" DropDownStyle="DropDownList" 
                                            TextFormatString="{0} ({1})" Width="125px" CssClass="form-control altura-textbox" IncrementalFilteringMode="Contains" 
                                            EnableSynchronization="False">
                                            <ValidationSettings ValidationGroup="datosContacto" Display="Dynamic" ErrorTextPosition="Bottom" ErrorDisplayMode="ImageWithText" SetFocusOnError="true">
                                            </ValidationSettings>
                                        </dx:ASPxComboBox>
                                    </div>
                                    <p class="col-lg-1 text-danger fw-medium fs-5 pt-1"></p>

                                    <label class="col-lg-1 col-form-label pe-0 fw-semibold btn-buscar">Nro.</label>
                                    <div class="col-lg-2 p-0">
                                        <dx:ASPxTextBox ID="txtTelefonoAlternativo" CssClass="rounded-2" Height="41px" AutoCompleteType="Disabled" MaxLength="11" Width="100%" 
                                            Cursor="end" ClientInstanceName="txtTelefonoAlternativo" runat="server">
                                           
                                                                                      <ValidationSettings Display="Dynamic" SetFocusOnError="True" ErrorTextPosition="Right">
</ValidationSettings>
<ClientSideEvents Validation="onTelefonoValidation" />
                                        </dx:ASPxTextBox>
                                    </div>

                                  <%--  <div class="col-lg-3 ps-1 pt-2 text-primary-emphasis fw-semibold" style="display: none" id="telefonoCorrecto">
                                        <p class="cursor-pointer">✔</p>
                                    </div>
                                    <div class="col-lg-3 ps-1 pt-2 text-primary-emphasis fw-semibold" style="display: none" id="telefonoIncorrecto">
                                        <p class="cursor-pointer text-danger">❌ Númer incompleto.</p>
                                    </div>--%>
                                </div>
                            </div>

                        </div>






                        <div class="row gx-0" id="camposAgregarContacto" style="display: none">

                            <div class="col-lg-12 navbar navbar-expand-xl bg-info text-white rounded-top py-0">
                                <div class="container-fluid">
                                    <div class="nav-item dropdown">
                                        <span class="breadcrumb-item active mt-2 mb-1 fs-5 fw-medium">
                                            <asp:Literal ID="ltrContacto" runat="server">Nuevo o Editar</asp:Literal>
                                        </span>
                                        <%--<a href="#" class="navbar-nav-link bg-info my-0 py-0" data-bs-toggle="tab" id="contacto"></a>--%>
                                    </div>
                                </div>
                            </div>


                            <!-- Datos Titular -->

                            <div class="row gx-3">

                                <div class="card col-lg-12 tab-content">

                                    <div class="row gx-1">

                                        <!-- Campos de Agregar Contacto en contrato Empresa -->

                                        <div class="card-body col-lg-5 tab-pane">
                                            <div action="#">

                                                <div class="row mb-1">
                                                    <label class="col-lg-3 col-form-label fw-semibold">Nombre/s</label>
                                                    <div class="col-lg-6 p-0">
                                                        <dx:ASPxTextBox ID="txtNombreContacto" ClientInstanceName="txtNombreContacto" runat="server" Width="100%" AutoCompleteType="Disabled" CssClass="form-control altura-textbox" EncodeHtml="false">
                                                            <ValidationSettings SetFocusOnError="True" ValidationGroup="datosContacto" ErrorText="El Nombre debe tener al menos <br /> 2 caracteres!" Display="Dynamic" ErrorTextPosition="Bottom">
                                                                <RequiredField IsRequired="True" ErrorText="Campo Obligatorio." />
                                                            </ValidationSettings>
                                                            <ClientSideEvents Validation="onLastNameValidation" />
                                                        </dx:ASPxTextBox>
                                                    </div>
                                                    <p class="col-lg-1 text-danger fw-medium fs-5 pt-1">•</p>
                                                </div>

                                                <div class="row mb-1">
                                                    <label class="col-lg-3 col-form-label fw-semibold">Apellido/s</label>
                                                    <div class="col-lg-6 p-0">
                                                        <dx:ASPxTextBox ID="txtApellidoContacto" ClientInstanceName="txtApellidoContacto" runat="server" EnableClientSideAPI="True" Width="100%" AutoCompleteType="Disabled" CssClass="form-control altura-textbox" EncodeHtml="false">
                                                            <ValidationSettings SetFocusOnError="True" ValidationGroup="datosContacto" ErrorText="El apellido debe tener <br /> al menos 2 caracteres!" Display="Dynamic" ErrorTextPosition="Bottom">
                                                                <RequiredField IsRequired="True" ErrorText="Campo Obligatorio." />
                                                            </ValidationSettings>
                                                            <ClientSideEvents Validation="onLastNameValidation" />
                                                        </dx:ASPxTextBox>
                                                    </div>
                                                    <p class="col-lg-1 text-danger fw-medium fs-5 pt-1">•</p>
                                                </div>

                                                <div class="row mb-1">
                                                    <label class="col-lg-3 col-form-label pe-0 fw-semibold">Tipo Doc.</label>
                                                    <div class="col-lg-6 p-0">

                                                        <dx:ASPxComboBox ID="cmbTipoDocContacto" ClientInstanceName="cmbTipoDocContacto" runat="server" DropDownStyle="DropDownList" ValueType="System.Int32" NullText="Seleccionar Documento" TextFormatString="{0} ({1})" Width="100%" CssClass="form-control altura-textbox" IncrementalFilteringMode="Contains" EnableSynchronization="False">
                                                            <ValidationSettings ValidationGroup="datosContacto" Display="Dynamic" ErrorTextPosition="Bottom" ErrorDisplayMode="ImageWithText" SetFocusOnError="true">
                                                                <RequiredField IsRequired="true" ErrorText="Campo Obligatorio." />
                                                            </ValidationSettings>
                                                            <ClientSideEvents TextChanged="function(s,e) { toggleTextContacto(s.GetSelectedItem().text); }" />
                                                        </dx:ASPxComboBox>

                                                    </div>
                                                    <p class="col-lg-1 text-danger fw-medium fs-5 pt-1">•</p>
                                                </div>

                                                <!--Número de Doc Default Contacto -->

                                                <div class="row mb-1" id="nroDocDefaultContacto" runat="server">
                                                    <label class="col-lg-3 pe-0 col-form-label fw-semibold">Nro.</label>
                                                    <div class="col-lg-2 p-0">
                                                        <dx:ASPxTextBox ID="txtDocDefaultContacto" CssClass="rounded-2" ReadOnly="true" NullText="###" Height="41px" Width="120px" Cursor="end" runat="server">
                                                            <ValidationSettings ValidationGroup="datosContacto" ErrorDisplayMode="ImageWithText" Display="Dynamic" SetFocusOnError="True" ErrorTextPosition="Bottom">
                                                                <RequiredField IsRequired="True" ErrorText="Campo Obligatorio." />
                                                            </ValidationSettings>
                                                        </dx:ASPxTextBox>
                                                    </div>
                                                    <p class="col-lg-1 text-danger fw-medium fs-5 pt-1 ms-4"></p>

                                                    <div class="col-lg-1 ps-1 form-control-feedback form-control-feedback-start flex-grow-1">
                                                        <dx:ASPxButton ID="btnDocDefaultContacto" runat="server" Enabled="false" CssClass="btn btn-secondary px-0 py-1 me-0 invisible" Text="🔎">
                                                        </dx:ASPxButton>
                                                    </div>
                                                </div>

                                                <!--Número de DNI Contacto -->

                                                <div class="row mb-1" id="nroDniContacto" runat="server" style="display: none">
                                                    <label class="col-lg-3 pe-0 col-form-label fw-semibold">DNI Nro.</label>
                                                    <div class="col-lg-2 p-0">
                                                        <dx:ASPxTextBox ID="txtDniContacto" CssClass="rounded-2" Height="41px" Width="120px" Cursor="end" AutoCompleteType="Disabled" runat="server" ClientInstanceName="txtDniContacto">
                                                            <MaskSettings Mask="00000000" PromptChar="#" IncludeLiterals="None" ErrorText="Faltan digitos!" />

                                                            <ValidationSettings ValidationGroup="datosContacto" ErrorDisplayMode="ImageWithText" Display="Dynamic" SetFocusOnError="True" ErrorTextPosition="Bottom">
                                                                <RequiredField IsRequired="True" ErrorText="Campo Obligatorio." />
                                                            </ValidationSettings>
                                                        </dx:ASPxTextBox>
                                                    </div>
                                                    <p class="col-lg-1 text-danger fw-medium fs-5 pt-1 ms-4 ps-3">•</p>

                                                    <div class="col-lg-1 ps-1 form-control-feedback form-control-feedback-start flex-grow-1">
                                                        <dx:ASPxButton ID="btnBuscarDniContacto" CssClass="btn btn-info px-0 py-1 me-0 invisible" AutoPostBack="False" ClientInstanceName="btnBuscarDniContacto" CausesValidation="false" runat="server" Text="🔎">
                                                            <ClientSideEvents Click="function(s,e){ValidateOnClick(s,e);}" />
                                                        </dx:ASPxButton>
                                                    </div>
                                                </div>

                                                <!--Número de CI Contacto -->

                                                <div class="row mb-1" id="nroCiContacto" runat="server" style="display: none">
                                                    <label class="col-lg-3 pe-0 col-form-label fw-semibold">CI Nro.</label>
                                                    <div class="col-lg-2 p-0">
                                                        <dx:ASPxTextBox ID="txtCiContacto" CssClass="rounded-2" Height="41px" Width="120px" Cursor="end" AutoCompleteType="Disabled" runat="server" ClientInstanceName="txtCiContacto">
                                                            <MaskSettings Mask="00000000" PromptChar="#" IncludeLiterals="None" ErrorText="Faltan digitos!" />

                                                            <ValidationSettings ValidationGroup="datosContacto" ErrorDisplayMode="ImageWithText" Display="Dynamic" SetFocusOnError="True" ErrorTextPosition="Bottom">
                                                                <RequiredField IsRequired="True" ErrorText="Campo Obligatorio." />
                                                            </ValidationSettings>
                                                        </dx:ASPxTextBox>
                                                    </div>
                                                    <p class="col-lg-1 text-danger fw-medium fs-5 pt-1 ms-4 ps-3">•</p>

                                                    <div class="col-lg-1 ps-1 form-control-feedback form-control-feedback-start flex-grow-1">
                                                        <dx:ASPxButton ID="btnBuscarCiContacto" CssClass="btn btn-info px-0 py-1 me-0 invisible" AutoPostBack="False" ClientInstanceName="btnBuscarCiContacto" CausesValidation="false" runat="server" Text="🔎">
                                                            <ClientSideEvents Click="function(s,e){ValidateOnClick(s,e);}" />
                                                        </dx:ASPxButton>
                                                    </div>
                                                </div>

                                                <!--Número de CUIL Contacto -->

                                                <div class="row mb-1" id="nroCuilContacto" runat="server" style="display: none">
                                                    <label class="col-lg-3 pe-0 col-form-label fw-semibold">CUIL Nro.</label>
                                                    <div class="col-lg-2 p-0">
                                                        <dx:ASPxTextBox ID="txtCuilContacto" CssClass="rounded-2" Height="41px" Width="120px" Cursor="end" AutoCompleteType="Disabled" runat="server" ClientInstanceName="txtCuilContacto">
                                                            <MaskSettings Mask="00-00000000-0" PromptChar="#" IncludeLiterals="None" ErrorText="Faltan digitos! Debe completar todos los caracteres." />
                                                            <ValidationSettings ValidationGroup="datosContactos" ErrorDisplayMode="ImageWithText" Display="Dynamic" SetFocusOnError="True" ErrorTextPosition="Bottom">
                                                                <RequiredField IsRequired="True" ErrorText="Campo Obligatorio." />
                                                            </ValidationSettings>
                                                        </dx:ASPxTextBox>
                                                        <clientsideevents click="function(s,e){ValidateOnClick(s,e);}" />
                                                    </div>
                                                    <p class="col-lg-1 text-danger fw-medium fs-5 pt-1 ms-4 ps-3">•</p>

                                                    <div class="col-lg-1 ps-1 form-control-feedback form-control-feedback-start flex-grow-1">
                                                        <dx:ASPxButton ID="btnBuscarCuilContacto" runat="server" CssClass="btn btn-info px-0 py-1 me-0 invisible" ClientInstanceName="btnBuscarCuilContacto" AutoPostBack="False" CausesValidation="false" Text="🔎">
                                                            <ClientSideEvents Click="function(s,e){ValidateOnClick(s,e);}" />
                                                        </dx:ASPxButton>
                                                    </div>
                                                </div>

                                                <!--Número de CUIT Contacto -->

                                                <div class="row mb-1" id="nroCuitContacto" runat="server" style="display: none">
                                                    <label class="col-lg-3 pe-0 col-form-label fw-semibold">CUIT Nro.</label>
                                                    <div class="col-lg-2 p-0">
                                                        <dx:ASPxTextBox ID="txtCuitContacto" CssClass="rounded-2 py-1" Width="120px" Cursor="end" AutoCompleteType="Disabled" ClientInstanceName="txtCuitContacto" runat="server">
                                                            <MaskSettings Mask="00-00000000-0" PromptChar="#" IncludeLiterals="None" ErrorText="Faltan digitos! Debe completar todos los caracteres." />
                                                            <ValidationSettings ValidationGroup="confirmarGeneral" Display="Dynamic" SetFocusOnError="True" ErrorTextPosition="Bottom">
                                                                <RequiredField IsRequired="True" ErrorText="Campo Obligatorio." />
                                                            </ValidationSettings>
                                                        </dx:ASPxTextBox>
                                                    </div>
                                                    <p class="col-lg-1 text-danger fw-medium fs-5 pt-1 ms-4 ps-3">•</p>

                                                    <div class="col-lg-1 ps-1 form-control-feedback form-control-feedback-start flex-grow-1">
                                                        <dx:ASPxButton ID="btnBuscarCuitContacto" runat="server" CssClass="btn btn-info px-0 py-1 me-0 invisible" ClientInstanceName="btnBuscarCuitContacto" AutoPostBack="False" CausesValidation="false" Text="🔎">
                                                            <ClientSideEvents Click="function(s,e){ValidateOnClick(s,e);}" />
                                                        </dx:ASPxButton>
                                                    </div>
                                                </div>

                                                <!--Número de Pasaporte Contacto -->

                                                <div class="row mb-1" id="nroPasaporteContacto" runat="server" style="display: none">
                                                    <label class="col-lg-3 pe-0 col-form-label fw-semibold">Pasaporte Nro.</label>
                                                    <div class="col-lg-2 p-0">
                                                        <dx:ASPxTextBox ID="txtPasaporteContacto" CssClass="rounded-2" NullText="AAA-000000" Height="41px" Width="120px" Cursor="end" AutoCompleteType="Disabled" runat="server" ClientInstanceName="txtPasaporteContacto">
                                                            <ValidationSettings ValidationGroup="datosContacto" ErrorDisplayMode="ImageWithText" Display="Dynamic" SetFocusOnError="True" ErrorTextPosition="Bottom">
                                                                <RequiredField IsRequired="True" ErrorText="Campo Obligatorio." />
                                                            </ValidationSettings>
                                                        </dx:ASPxTextBox>
                                                        <clientsideevents click="function(s,e){ValidateOnClick(s,e);}" />
                                                    </div>
                                                    <p class="col-lg-1 text-danger fw-medium fs-5 pt-1 ms-4 ps-3">•</p>

                                                    <div class="col-lg-1 ps-1 form-control-feedback form-control-feedback-start flex-grow-1">
                                                        <dx:ASPxButton ID="btnBuscarPasaporteContacto" runat="server" CssClass="btn btn-info px-0 py-1 me-0 invisible" ClientInstanceName="btnBuscarPasaporteContacto" AutoPostBack="False" CausesValidation="false" Text="🔎">
                                                            <ClientSideEvents Click="function(s,e){ValidateOnClick(s,e);}" />
                                                        </dx:ASPxButton>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>

                                        <div class="card-body col-lg-7">
                                            <div action="#">

                                                <div class="row mb-1">
                                                    <label class="col-lg-2 col-form-label fw-semibold">Fecha de Nac.</label>
                                                    <div class="col-lg-2 px-0">
                                                        <dx:ASPxDateEdit ID="dateFechaNacContacto" ClientInstanceName="dateFechaNacContacto" CssClass="form-control altura-textbox px-0" Width="125px" runat="server" EditFormat="Custom">
                                                            <TimeSectionProperties>
                                                                <TimeEditProperties EditFormatString="hh:mm tt" />
                                                            </TimeSectionProperties>
                                                            <CalendarProperties>
                                                                <FastNavProperties DisplayMode="Inline" />
                                                            </CalendarProperties>
                                                            <%--<ClientSideEvents Validation="OnBirthdayValidation" />--%>
                                                            <ValidationSettings ValidationGroup="datosContacto" ErrorDisplayMode="ImageWithText" Display="Dynamic" SetFocusOnError="true" ErrorTextPosition="Bottom">
                                                                <RequiredField IsRequired="true" ErrorText="Campo Obligatorio." />
                                                            </ValidationSettings>
                                                        </dx:ASPxDateEdit>
                                                    </div>

                                                    <p class="col-lg-1 text-danger fw-medium fs-5 pt-1 ps-3">•</p>

                                                    <label class="col-lg-1 col-form-label ps-2 pe-0 fw-semibold">Sexo</label>
                                                    <div class="col-lg-2 p-0">

                                                        <dx:ASPxComboBox ID="cmbSexoContacto" ClientInstanceName="cmbSexoContacto" runat="server" DropDownStyle="DropDownList" ValueType="System.Int32" NullText="Seleccionar Sexo" TextFormatString="{0} ({1})" Width="113px" CssClass="form-control altura-textbox" IncrementalFilteringMode="Contains" EnableSynchronization="False">
                                                            <ValidationSettings ValidationGroup="datosContacto" Display="Dynamic" ErrorTextPosition="Bottom" ErrorDisplayMode="ImageWithText" SetFocusOnError="true">
                                                                <RequiredField IsRequired="true" ErrorText="Campo Obligatorio." />
                                                            </ValidationSettings>
                                                        </dx:ASPxComboBox>

                                                    </div>
                                                    <p class="col-lg-1 text-danger fw-medium fs-5 pt-1">•</p>
                                                </div>

                                                <div class="row mb-1">
                                                    <label class="col-lg-2 col-form-label fw-semibold">E-mail</label>
                                                    <div class="col-lg-6 p-0">
                                                        <dx:ASPxTextBox ID="txtEmailContacto" ClientInstanceName="txtEmailContacto" CssClass="form-control altura-textbox" AutoCompleteType="Disabled" NullText="<EMAIL>" Width="100%" runat="server">
                                                            <ValidationSettings ValidationGroup="datosContacto" Display="Dynamic" ErrorTextPosition="Bottom">
                                                                <RequiredField IsRequired="True" ErrorText="Campo Obligatorio." />
                                                                <RegularExpression ValidationExpression="\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*" ErrorText="Formato de E-mail inválido." />
                                                            </ValidationSettings>
                                                        </dx:ASPxTextBox>
                                                    </div>
                                                    <p class="col-lg-1 w-32px text-danger fw-medium fs-5 pt-1 pe-0">•</p>
                                                </div>

                                                <div class="row mb-1">
                                                    <label class="col-lg-2 col-form-label pe-0 fw-semibold btn-buscar">Teléfono</label>
                                                    <div class="col-lg-2 p-0">
                                                        <dx:ASPxComboBox ID="cmbTelefonoContacto" ClientInstanceName="cmbTelefonoContacto" runat="server" DropDownStyle="DropDownList" TextFormatString="{0} ({1})" Width="125px" CssClass="form-control altura-textbox" IncrementalFilteringMode="Contains" EnableSynchronization="False">
                                                            <ValidationSettings ValidationGroup="datosContacto" Display="Dynamic" ErrorTextPosition="Bottom" ErrorDisplayMode="ImageWithText" SetFocusOnError="true">
                                                                <RequiredField IsRequired="true" ErrorText="Campo Obligatorio." />
                                                            </ValidationSettings>
                                                        </dx:ASPxComboBox>
                                                    </div>
                                                    <p class="col-lg-1 text-danger fw-medium fs-5 pt-1 ps-3">•</p>

                                                    <label class="col-lg-1 col-form-label pe-0 fw-semibold btn-buscar">Nro.</label>
                                                    <div class="col-lg-2 p-0">
                                                        <dx:ASPxTextBox ID="txtTelefonoContacto" CssClass="rounded-2" Height="41px" Width="100%" Cursor="end" runat="server" ClientInstanceName="txtTelefonoContacto">
                                                            <MaskSettings Mask="000 - 0000000" PromptChar="#" IncludeLiterals="None" ErrorText="Faltan dígitos." />
                                                            <ValidationSettings ValidationGroup="datosContacto" Display="Dynamic" SetFocusOnError="True" ErrorTextPosition="Bottom">
                                                            </ValidationSettings>
                                                        </dx:ASPxTextBox>
                                                    </div>
                                                    <p class="col-lg-1 w-32px text-danger fw-medium fs-5 pt-1">•</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group d-flex justify-content-end align-items-center mb-3 pe-2">
                                        <dx:ASPxButton ID="btnAgregarContactoAContrato" runat="server" CssClass="btn btn-secondary fw-semibold px-0 py-1" ClientInstanceName="btnAgregarContactoAContrato" CausesValidation="false" AutoPostBack="False" Text="Agregar">
                                            <ClientSideEvents Click="function(s, e) { ValidateContactoOnClick(s, e); }" />
                                        </dx:ASPxButton>
                                    </div>

                                </div>


                                <dx:ASPxCallbackPanel runat="server" ID="CallbackEliminarContacto" ClientInstanceName="CallbackEliminarContacto">

                                    <PanelCollection>
                                        <dx:PanelContent ID="PanelContent3" runat="server">

                                            <div class="row mt-2 align-content-center d-flex justify-content-center">
                                                <dx:ASPxGridView ID="GrVContacto" CssClass="fw-normal" OnDataBinding="GrVContacto_DataBinding" KeyFieldName="Id" runat="server" Visible="true" OnRowValidating="GrVContacto_RowValidating" ClientInstanceName="GrVContacto" OnCellEditorInitialize="GrVContacto_CellEditorInitialize">
                                                    <SettingsEditing Mode="Inline" />
                                                    <SettingsPager PageSize="20" AlwaysShowPager="true"></SettingsPager>
                                                    <Columns>

                                                        <dx:GridViewDataTextColumn FieldName="Nombre" Caption="Nombre" HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" CellStyle-HorizontalAlign="Center" />
                                                        <dx:GridViewDataTextColumn FieldName="Apellido" Caption="Apellido" HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" CellStyle-HorizontalAlign="Center" />

                                                        <dx:GridViewDataColumn Caption="Tipo y </br> N° de Doc." HeaderStyle-HorizontalAlign="Center" Settings-AllowEllipsisInText="true" CellStyle-Border-BorderColor="Black" CellStyle-HorizontalAlign="Center">
                                                            <DataItemTemplate>
                                                                <asp:Literal runat="server" ID="ltlTelefono" Text='<%# string.Format( "{0} </br> {1}", Eval("DocumentosTipo"), Eval("NumeroDoc")) %>'></asp:Literal>
                                                            </DataItemTemplate>
                                                        </dx:GridViewDataColumn>
                                                        <dx:GridViewDataDateColumn FieldName="FechaNac" Caption="Fecha de Nac." HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" CellStyle-HorizontalAlign="Center" />
                                                        <dx:GridViewDataTextColumn FieldName="SexoDescripcion" Caption="Sexo" HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" CellStyle-HorizontalAlign="Center" />
                                                        <dx:GridViewDataColumn Caption="E-mail" HeaderStyle-HorizontalAlign="Center" Settings-AllowEllipsisInText="true" CellStyle-Border-BorderColor="Black" CellStyle-HorizontalAlign="Center">
                                                            <DataItemTemplate>
                                                                <asp:Literal runat="server" ID="ltlEmail" Text='<%# Eval("Email") %>'></asp:Literal>
                                                            </DataItemTemplate>
                                                        </dx:GridViewDataColumn>
                                                        <dx:GridViewDataColumn Caption="Tipo y </br> N° de Tel." HeaderStyle-HorizontalAlign="Center" Settings-AllowEllipsisInText="true" CellStyle-Border-BorderColor="Black" CellStyle-HorizontalAlign="Center">
                                                            <DataItemTemplate>
                                                                <asp:Literal runat="server" ID="ltlTelefono" Text='<%# string.Format( "{0} </br> {1}", Eval("TelefonoTipoDescripcion"), Eval("Tel")) %>'></asp:Literal>
                                                            </DataItemTemplate>
                                                        </dx:GridViewDataColumn>

                                                        <dx:GridViewCommandColumn HeaderStyle-HorizontalAlign="Center" ShowNewButtonInHeader="false" CellStyle-Font-Bold="true" CellStyle-Border-BorderColor="Black" ShowEditButton="true" VisibleIndex="7" />

                                                        <dx:GridViewDataColumn Caption="" ReadOnly="true" HeaderStyle-HorizontalAlign="Center" HeaderStyle-CssClass="text-secondary" Settings-AllowEllipsisInText="true" CellStyle-Border-BorderColor="Black" CellStyle-HorizontalAlign="Center">
                                                            <DataItemTemplate>
                                                                <dx:ASPxButton runat="server" ID="btnEliminarContacto" CssClass="fw-semibold text-decoration-none text-secondary" RenderMode="Link" Text="Eliminar" OnClick="btnEliminarContacto_Click" CommandArgument='<%# Eval("Id" )%>' AutoPostBack="false" CausesValidation="false">
                                                                    <ClientSideEvents Click="function(s,e){e.processOnServer ==confirm('¿Desea eliminar?');}" />
                                                                </dx:ASPxButton>

                                                                <%--<dx:ASPxButton ID="btnEliminarContacto" ClientInstanceName="btnEliminarContacto" AutoPostBack="false" Text="Eliminar" CausesValidation="false" runat="server" RenderMode="Link" CommandArgument='<%# Eval("Id" )%>' CssClass="fw-semibold text-secondary text-decoration-none" OnClick="btnEliminarContacto_Click">
                                                        <ClientSideEvents Click="function(s,e){ e.processOnServer = EliminarContacto(s,e); } " />
                                                    </dx:ASPxButton>--%>
                                                            </DataItemTemplate>
                                                        </dx:GridViewDataColumn>

                                                    </Columns>
                                                    <SettingsDataSecurity AllowInsert="false" />
                                                    <EditFormLayoutProperties>
                                                        <SettingsAdaptivity AdaptivityMode="SingleColumnWindowLimit" SwitchToSingleColumnAtWindowInnerWidth="700" />
                                                    </EditFormLayoutProperties>
                                                    <SettingsPopup>
                                                        <EditForm Width="600">
                                                            <SettingsAdaptivity Mode="OnWindowInnerWidth" SwitchAtWindowInnerWidth="768" />
                                                        </EditForm>
                                                    </SettingsPopup>

                                                </dx:ASPxGridView>

                                            </div>
                                        </dx:PanelContent>
                                    </PanelCollection>
                                </dx:ASPxCallbackPanel>
                            </div>
                        </div>






                        <!-- Aquí terminan Campos Empresa-->


                        <!-- Domicilio -->

                        <div class="row gx-0">

                            <div class="col-lg-12 navbar navbar-expand-xl bg-secondary navbar-dark rounded-top py-0">
                                <div class="container-fluid">
                                    <div class="nav-item dropdown">
                                        <span class="breadcrumb-item active mt-2 mb-1 fs-5 fw-medium">
                                            <asp:Literal ID="ltrDomicilio" runat="server">Nuevo o Editar</asp:Literal>
                                        </span>
                                    </div>
                                </div>
                            </div>


                            <!-- Datos Domicilio -->

                            <div class="row gx-1">

                                <div class="card col-lg-12">

                                    <div class="row gx-0">

                                        <div class="card-body col-lg-5">
                                            <div action="#">

                                                <div class="row mb-1">
                                                    <label class="col-lg-2 col-form-label pe-0 fw-semibold">Provincia</label>
                                                    <div class="col-lg-9 p-0">

                                                        <dx:ASPxComboBox ID="cmbProvincias" ClientInstanceName="cmbProvincias" runat="server" DropDownStyle="DropDownList" 
                                                            NullText="Seleccionar Provincia" TextFormatString="{0} ({1})" Width="100%" CssClass="form-control altura-textbox"
                                                            IncrementalFilteringMode="Contains" TextField="NombreProvincias" ValueField="NombreProvincias" EnableSynchronization="False">
                                                            <ValidationSettings ValidationGroup="confirmarGeneral" Display="Dynamic" ErrorTextPosition="Bottom" ErrorDisplayMode="ImageWithText" SetFocusOnError="true">
                                                                <RequiredField IsRequired="true" ErrorText="Campo Obligatorio." />
                                                            </ValidationSettings>
                                                            <ClientSideEvents SelectedIndexChanged="function(s, e) { OnProvinciasChanged(s); }" />
                                                        </dx:ASPxComboBox>
                                                    </div>
                                                    <p class="col-lg-1 text-danger fw-medium fs-5 pt-1">•</p>
                                                </div>


                                                <div class="row mb-1">
                                                    <label class="col-lg-2 col-form-label pe-0 fw-semibold">Localidad</label>
                                                    <div class="col-lg-9 p-0">
                                                        <dx:ASPxComboBox ID="cmbLocalidades" runat="server" DropDownStyle="DropDownList" NullText="Seleccionar Localidad" TextFormatString="{0} ({1})" Width="100%" CssClass="form-control altura-textbox" IncrementalFilteringMode="Contains" ClientInstanceName="cmbLocalidades" OnCallback="Drop_Localidades_Callback" TextField="NombreLocalidades" ValueField="NombreLocalidades" EnableSynchronization="False">
                                                            <ValidationSettings ValidationGroup="confirmarGeneral" Display="Dynamic" ErrorTextPosition="Bottom" ErrorDisplayMode="ImageWithText" SetFocusOnError="true">
                                                                <RequiredField IsRequired="true" ErrorText="Campo Obligatorio." />
                                                            </ValidationSettings>
                                                            <ClientSideEvents EndCallback=" OnEndCallback" SelectedIndexChanged="function(s, e) { OnLocalidadesChanged(s); }" />
                                                        </dx:ASPxComboBox>
                                                    </div>
                                                    <p class="col-lg-1 text-danger fw-medium fs-5 pt-1">•</p>
                                                </div>

                                                <div class="row mb-1">
                                                    <label class="col-lg-2 col-form-label fw-semibold">Calle</label>
                                                    <div class="col-lg-9 p-0">
                                                        <dx:ASPxComboBox ID="cmbCalles" ClientInstanceName="cmbCalles" runat="server" DropDownStyle="DropDownList" NullText="Seleccionar Calle" TextFormatString="{0} ({1})" Width="100%" CssClass="form-control altura-textbox" IncrementalFilteringMode="Contains" TextField="NombreCalles" ValueField="NombreCalles" OnCallback="Drop_Calles_Callback" EnableSynchronization="False">
                                                            <ValidationSettings ValidationGroup="confirmarGeneral" Display="Dynamic" ErrorTextPosition="Bottom" ErrorDisplayMode="ImageWithText" SetFocusOnError="true">
                                                                <RequiredField IsRequired="true" ErrorText="Campo Obligatorio." />
                                                            </ValidationSettings>
                                                            <ClientSideEvents EndCallback=" OnEndCallback" />
                                                        </dx:ASPxComboBox>
                                                    </div>
                                                    <p class="col-lg-1 text-danger fw-medium fs-5 pt-1">•</p>
                                                </div>

                                                <div class="row mb-1">
                                                    <label class="col-lg-2 pe-0 col-form-label fw-semibold">Altura</label>
                                                    <div class="col-lg-2 p-0">
                                                        <dx:ASPxTextBox ID="txtAltura" CssClass="form-control altura-textbox" NullText="-" AutoCompleteType="Disabled" runat="server">
                                                        </dx:ASPxTextBox>
                                                    </div>

                                                    <label class="col-lg-1 col-form-label ps-1 pe-0 ms-3 fw-semibold">Casa</label>
                                                    <div class="col-lg-2 p-0">
                                                        <dx:ASPxTextBox ID="txtCasaNro" CssClass="form-control altura-textbox" NullText="-" AutoCompleteType="Disabled" runat="server">
                                                        </dx:ASPxTextBox>
                                                    </div>

                                                    <label class="col-lg-1 col-form-label ps-1 pe-0 ms-3 fw-semibold">Piso</label>
                                                    <div class="col-lg-2 p-0">
                                                        <dx:ASPxTextBox ID="txtPisoNro" CssClass="form-control altura-textbox" NullText="-" AutoCompleteType="Disabled" runat="server">
                                                        </dx:ASPxTextBox>
                                                    </div>
                                                </div>

                                                <div class="row mb-1">
                                                    <label class="col-lg-2 pe-0 col-form-label fw-semibold">Dpto</label>
                                                    <div class="col-lg-2 p-0">
                                                        <dx:ASPxTextBox ID="txtDpto" CssClass="form-control altura-textbox" NullText="-" AutoCompleteType="Disabled" runat="server">
                                                        </dx:ASPxTextBox>
                                                    </div>

                                                    <label class="col-lg-1 col-form-label ps-1 pe-0 ms-3 fw-semibold">Mza</label>
                                                    <div class="col-lg-2 p-0">
                                                        <dx:ASPxTextBox ID="txtManzana" CssClass="form-control altura-textbox" NullText="-" AutoCompleteType="Disabled" runat="server">
                                                        </dx:ASPxTextBox>
                                                    </div>

                                                    <label class="col-lg-1 col-form-label ps-1 pe-0 ms-3 fw-semibold">Lote</label>
                                                    <div class="col-lg-2 p-0">
                                                        <dx:ASPxTextBox ID="txtLote" CssClass="form-control altura-textbox" NullText="-" AutoCompleteType="Disabled" runat="server">
                                                        </dx:ASPxTextBox>
                                                    </div>
                                                </div>

                                                <div class="row mb-1">
                                                    <label class="col-lg-2 pe-0 col-form-label fw-semibold">Dx/Md</label>
                                                    <div class="col-lg-2 p-0">
                                                        <dx:ASPxTextBox ID="txtDxMd" CssClass="form-control altura-textbox" NullText="-" AutoCompleteType="Disabled" runat="server">
                                                        </dx:ASPxTextBox>
                                                    </div>

                                                    <label class="col-lg-1 col-form-label ps-1 pe-0 ms-3 fw-semibold">Peat.</label>
                                                    <div class="col-lg-2 p-0">
                                                        <dx:ASPxTextBox ID="txtPeat" CssClass="form-control altura-textbox" NullText="-" AutoCompleteType="Disabled" runat="server">
                                                        </dx:ASPxTextBox>
                                                    </div>

                                                    <label class="col-lg-1 col-form-label ps-1 pe-0 ms-3 fw-semibold">Tira</label>
                                                    <div class="col-lg-2 p-0">
                                                        <dx:ASPxTextBox ID="txtTira" CssClass="form-control altura-textbox" NullText="-" AutoCompleteType="Disabled" runat="server">
                                                        </dx:ASPxTextBox>
                                                    </div>
                                                </div>

                                                <div class="row mb-1">
                                                    <label class="col-lg-2 pe-0 col-form-label fw-semibold">Entre Calles</label>
                                                    <div class="col-lg-9 p-0">
                                                        <dx:ASPxTextBox ID="txtEntreCalles" CssClass="form-control altura-textbox" AutoCompleteType="Disabled" runat="server">
                                                        </dx:ASPxTextBox>
                                                    </div>
                                                </div>

                                                <div class="row mb-1">
                                                    <label class="col-lg-2 pe-0 col-form-label fw-semibold">Barrio</label>
                                                    <div class="col-lg-9 p-0">
                                                        <dx:ASPxTextBox ID="txtBarrio" CssClass="form-control altura-textbox" AutoCompleteType="Disabled" runat="server">
                                                        </dx:ASPxTextBox>
                                                    </div>
                                                </div>

                                            </div>

                                            <p class="fw-medium text-danger mt-4 mb-0" style="font-size: 11px">(<span class="fs-5">•</span>) Campos Obligatorios</p>

                                        </div>

                                        <div class="card-body col-lg-4">
                                            <div action="#">

                                                <%--Mapa de Google Maps--%>

                                                <div id="map" class="w-100 border border-secondary rounded-2 border-width-2" style="height: 370px;"></div>
                                                <p class="text-body-secondary text-center">
                                                    Ctrl + rueda de desplazamiento del mouse acerca/aleja el mapa.<br />
                                                    Doble clic te permitirá fijar un punto o una dirección específica.
                                                </p>

                                            </div>
                                        </div>

                                        <div class="card-body col-lg-2">
                                            <div action="#">

                                                <div class="row mt-2">
                                                    <label class="col-lg-3 pe-0 col-form-label fw-semibold">Latit.</label>
                                                    <div class="col-lg-9 p-0">
                                                        <dx:ASPxTextBox ID="txtLat" ClientInstanceName="txtLat" CssClass="form-control altura-textbox" ReadOnly="true" NullText="Latitud" AutoCompleteType="Disabled" runat="server" />
                                                        <asp:HiddenField ID="hfLatitud" runat="server" />
                                                    </div>
                                                </div>
                                                <div class="row mt-2">
                                                    <label class="col-lg-3 pe-0 col-form-label fw-semibold">Long.</label>
                                                    <div class="col-lg-9 p-0">
                                                        <dx:ASPxTextBox ID="txtLng" ClientInstanceName="txtLng" CssClass="form-control altura-textbox" ReadOnly="true" NullText="Longitud" AutoCompleteType="Disabled" runat="server" />
                                                        <asp:HiddenField ID="hfLongitud" runat="server" />
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                    </div>

                                </div>

                                <div class="form-group d-flex justify-content-end align-items-center my-3 pe-5">
                                    <dx:ASPxButton ID="btnGuardarContrato" runat="server" CssClass="btn btn-secondary fw-semibold px-0 py-1" OnClick="btnGuardarContrato_Click" ClientInstanceName="btnGuardarContrato" AutoPostBack="False" CausesValidation="false" Text="Guardar">
                                        <ClientSideEvents Click="function(s,e){ e.processOnServer=validarGuardarPrincipal(); }" />
                                    </dx:ASPxButton>

                                    <dx:ASPxButton ID="btnCancelarContrato" runat="server" OnClick="btnCancelarContrato_Click" CssClass="btn text-white fw-semibold px-0 py-1 ms-3" BackColor="Brown" ClientInstanceName="btnCancelarContrato" AutoPostBack="False" Text="Cancelar">
                                        <ClientSideEvents Click="function(s,e){ RedireccionarAPaginaPrincipal(); LoadingPanel.Show(); ValidateOnClick(); panel.PerformCallback(); }" />
                                    </dx:ASPxButton>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <dx:ASPxCallback ID="BtnAgregar_Callback" runat="server" ClientInstanceName="BtnAgregar_Callback" OnCallback="Callback_Agregar_Callback">
        <ClientSideEvents CallbackComplete="function(s,e) { GrVContacto.PerformCallback(); LoadingPanel.Hide(); }" />
    </dx:ASPxCallback>

    <dx:ASPxCallback ID="BtnEliminar_Callback" runat="server" ClientInstanceName="BtnEliminar_Callback">
        <ClientSideEvents CallbackComplete="function(s,e) { GrVContacto.PerformCallback(); LoadingPanel.Hide(); }" />
    </dx:ASPxCallback>

    <dx:ASPxCallback ID="Editar_Callback" runat="server" ClientInstanceName="Editar_Callback" OnCallback="callback_editar_Callback">
        <ClientSideEvents CallbackComplete="function(s,e) { GrVContacto.PerformCallback(); LoadingPanel.Hide(); }" />
    </dx:ASPxCallback>

    <dx:ASPxCallback ID="callBackLocalidades" runat="server" ClientInstanceName="callBackLocalidades">
        <ClientSideEvents EndCallback="function(s,e) { cmbLocalidades.PerformCallback(cmbProvincias.GetValue().toString()); LoadingPanel.Hide(); }" />
    </dx:ASPxCallback>

    <dx:ASPxCallback ID="callBackCalles" runat="server" ClientInstanceName="callBackCalles">
        <ClientSideEvents EndCallback="function(s,e) { cmbCalles.PerformCallback(cmbLocalidades.GetValue().toString()); LoadingPanel.Hide(); }" />
    </dx:ASPxCallback>

</asp:Content>

<asp:Content ID="Content3" ContentPlaceHolderID="footer" runat="server">

    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDZCRSlDF2kaGvZusltpUYmNDczpmVcz_8&callback=initMap" async defer></script>

    <script>

        var selectedLatLng;
        var previousMarker = null;

        function initMap() {
            var map = new google.maps.Map(document.getElementById('map'), {
                center: { lat: -38.9499, lng: -68.0655 },
                zoom: 13,
                disableDoubleClickZoom: true
            });

            google.maps.event.addListener(map, 'dblclick', function (event) {

                if (previousMarker) {
                    previousMarker.setMap(null);
                }

                selectedLatLng = event.latLng;

                txtLat.SetText(selectedLatLng.lat());
                txtLng.SetText(selectedLatLng.lng());

                var marker = new google.maps.Marker({
                    position: selectedLatLng,
                    map: map,
                });

                previousMarker = marker;
            });
        };

        document.addEventListener("DOMContentLoaded", function () {
            initMap();
        });

    </script>

</asp:Content>
