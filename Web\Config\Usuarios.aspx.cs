﻿using Business.Services.Usuarios;
using DevExpress.Export;
using DevExpress.Web;
using Domain;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Script.Services;
using System.Web.Services;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;

namespace Web.Config
{
    public partial class Usuarios : System.Web.UI.Page
    {
     

        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                if (!IsPostBack)
                {
                   // Literal ltr_nombreBreadcrumb = (Literal)Master.FindControl("ltr_nombreBreadcrumb");
                    ltr_nombreBreadcrumb.Text = "Lista usuarios";

                  //  ltr_Titulo.Text = "Usuarios del sistema";

                 //   iconPage.Attributes.Add("class", "icon-user mr2");
                  
                }

                gv_users.DataSource = UsuarioService.GetAll();
                gv_users.DataBind();

            }
            catch(Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al cargar la página, consulte al administrador del sistema', 'error');", true);

            }
        }

        protected void Grid_ToolbarItemClick(object source, ASPxGridToolbarItemClickEventArgs e)
        {
            ASPxGridView grid = (ASPxGridView)source;
            switch (e.Item.Name)
            {
                case "CustomExportToXLS":
                    gv_users.ExportXlsToResponse(new DevExpress.XtraPrinting.XlsExportOptionsEx { ExportType = ExportType.WYSIWYG });
                    break;
                case "CustomExportToXLSX":
                    gv_users.ExportXlsxToResponse(new DevExpress.XtraPrinting.XlsxExportOptionsEx { ExportType = ExportType.WYSIWYG });
                    break;
                default:
                    break;
            }
        }

      

        protected void btn_agregar_Click(object sender, EventArgs e)
        {
            Response.Redirect(Global.ApplicationPath + "/Config/AddEditUsuario.aspx",false);
        }

        protected void btnEditar_Click(object sender, EventArgs e)
        {
            try
            {
                ASPxButton btn = (ASPxButton)sender;
                GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer)btn.NamingContainer;
                var ind = DataBinder.Eval(c.DataItem, "Id");

                Response.Redirect(Global.ApplicationPath + "/Config/AddEditUsuario.aspx?id=" + Util.Helpers.Encrypt(ind.ToString()), false);
            }
            catch(Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al tratar de editar un elemento, consulte al administrador del sistema', 'error');", true);

            }
        }

        protected void btnEliminar_Click(object sender, EventArgs e)
        {
            try
            {
                ASPxButton btn = (ASPxButton)sender;
                GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer)btn.NamingContainer;
                var ind = DataBinder.Eval(c.DataItem, "Id");

                Usuario user = UsuarioService.GetById(Convert.ToInt32(ind));
                user.Activo = false;
                user.Eliminado = true;
                UsuarioService.SaveOrUpdate(user);

                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "gv_users.PerformCallback();", true);
            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al tratar de eliminar un elemento, consulte al administrador del sistema', 'error');", true);

            }
        }

        protected string getRoles(object id)
        {
            string salida = "-";
            try
            {
                Usuario u = UsuarioService.GetById(Convert.ToInt32(id));
                if (u != null)
                {
                    salida = "";
                    foreach(Rol r in u.Rols)
                    {
                        salida += "[" + r.Nombre +"] ";
                    }
                }
            }catch(Exception ex)
            {

            }
            return salida;
        }

        
    }
}