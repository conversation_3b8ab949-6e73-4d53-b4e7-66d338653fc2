﻿<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="Zonas" table="Zonas">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Codigo" type="String">
      <column name="Codigo" not-null="false" length="10" sql-type="nvarchar" />
    </property>
    <property name="Descripcion" type="String">
      <column name="Descripcion" not-null="false" sql-type="nvarchar" />
    </property>
    <property name="Activo" type="Boolean">
      <column name="Activo" default="1" not-null="false" sql-type="bit" />
    </property>
    <set name="Localidades" inverse="true" generic="true">
      <key>
        <column name="IdZona" />
      </key>
      <one-to-many class="Localidades" />
    </set>
  </class>
</hibernate-mapping>