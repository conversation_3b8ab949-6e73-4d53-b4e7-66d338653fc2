﻿using DAO;
using Domain;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Contratos {
    public class ContratoDomiciliosServices {

        public static void SaveOrUpdate(ContratoDomicilios contratoDomicilios) {

            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                using(NHibernate.ITransaction tx = sess.BeginTransaction()) {
                    try {
                        new ContratoDomiciliosRepository(sess).Add(contratoDomicilios);
                        tx.Commit();
                    } catch(Exception e) {
                        tx.Rollback();
                        throw e;
                    } finally {
                        sess.Close();
                        sess.Dispose();
                    }
                }
            }
        }

        public static IList<ContratoDomicilios> GetAll() {
            IList<ContratoDomicilios> listaAux;
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                listaAux = (IList<ContratoDomicilios>) new ContratoDomiciliosRepository(sess).GetAll();
                sess.Close();
                sess.Dispose();
            }

            return (listaAux);
        }

        public static ContratoDomicilios GetById(int id) {
            ContratoDomicilios CC;
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                CC = new ContratoDomiciliosRepository(sess).GetByKey(id);

                if(CC != null) {
                    NHibernateUtil.Initialize(CC);
                    NHibernateUtil.Initialize(CC.DomiciliosTipos);
                }
                sess.Close();
                sess.Dispose();
                return CC;
            }
        }

        public static void Delete(Domain.ContratoDomicilios CC) {
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                using(NHibernate.ITransaction tx = sess.BeginTransaction()) {
                    try {
                        new ContratoDomiciliosRepository(sess).Remove(CC);
                        tx.Commit();
                    } catch(Exception e) {
                        tx.Rollback();
                        throw e;
                    } finally {
                        sess.Close();
                        sess.Dispose();
                    }
                }
            }
        }
    }
}
