<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="Monedas" table="Monedas">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Descripcion" type="String">
      <column name="Descripcion" not-null="false" length="15" sql-type="nvarchar" />
    </property>
    <property name="Simbolo" type="String">
      <column name="Simbolo" not-null="false" length="14" sql-type="nvarchar" />
    </property>
    <property name="MonedaNacional" type="Int16">
      <column name="MonedaNacional" not-null="false" precision="5" scale="0" sql-type="smallint" />
    </property>
    <property name="DescripcionPlural" type="String">
      <column name="DescripcionPlural" not-null="false" length="17" sql-type="nvarchar" />
    </property>
    <set name="Cheques" inverse="true" generic="true">
      <key>
        <column name="IdMonedas" />
      </key>
      <one-to-many class="Cheques" />
    </set>
    <set name="Comprobantes" inverse="true" generic="true">
      <key>
        <column name="IdMoneda" />
      </key>
      <one-to-many class="Comprobantes" />
    </set>
    <set name="Contratos" inverse="true" generic="true">
      <key>
        <column name="IdMoneda" />
      </key>
      <one-to-many class="Contratos" />
    </set>
    <set name="CuponesTarjetas" inverse="true" generic="true">
      <key>
        <column name="IdMoneda" />
      </key>
      <one-to-many class="CuponesTarjetas" />
    </set>
    <set name="Efectivos" inverse="true" generic="true">
      <key>
        <column name="IdMoneda" />
      </key>
      <one-to-many class="Efectivo" />
    </set>
  </class>
</hibernate-mapping>