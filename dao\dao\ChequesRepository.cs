﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 10/8/2023 15:55:09
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class ChequesRepository : NHibernateRepository<Domain.Cheques>, IChequesRepository
    {
        public ChequesRepository(ISession session) : base(session)
        {
        }

        public virtual ICollection<Domain.Cheques> GetAll()
        {
            return session.CreateQuery(string.Format("from Cheques")).List<Domain.Cheques>();
        }

        public virtual Domain.Cheques GetByKey(int _Id)
        {
            return session.Get<Domain.Cheques>(_Id);
        }
    }
}
