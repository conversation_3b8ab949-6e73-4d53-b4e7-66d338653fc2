//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 8/11/2023 09:53:25
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class RolDeterminacionesRepository : NHibernateRepository<Domain.RolDeterminaciones>, IRolDeterminacionesRepository
    {
        public RolDeterminacionesRepository(ISession session) : base(session)
        {
        }

        public virtual ICollection<Domain.RolDeterminaciones> GetAll()
        {
            return session.CreateQuery(string.Format("from RolDeterminaciones")).List<Domain.RolDeterminaciones>();
        }

        public virtual Domain.RolDeterminaciones GetByKey(int _Id)
        {
            return session.Get<Domain.RolDeterminaciones>(_Id);
        }
    }
}
