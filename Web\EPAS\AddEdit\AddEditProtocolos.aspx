﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" MaintainScrollPositionOnPostback="true" CodeBehind="AddEditProtocolos.aspx.cs" Inherits="Web.EPAS.AddEdit.AddEditProtocolos" %>

<%@ Register Assembly="DevExpress.Web.v18.1" Namespace="DevExpress.Web" TagPrefix="dx" %>


<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">

    <script type="text/javascript">

        function validateCargarResultado(s,e) {
            if (ASPxClientEdit.ValidateGroup('resultado')) {                
                LoadingPanel.Show();
                pcCargarResultado.Hide();
                gvDeterminaciones.PerformCallback();
                return true;
            } else {
                return false;
            
            }
        }

        function OnToolbarItemClick(s, e) {
            if (IsCustomExportToolbarCommand(e.item.name)) {
                e.processOnServer = true;
                e.usePostBack = true;
            } else {

            }
        }

        function IsCustomExportToolbarCommand(command) {
            return command == "CustomExportToXLS" || command == "CustomExportToXLSX";
        }

        function RedireccionarAPProtocolos() {
            window.location.href = "/SIGeLab/EPAS/Lista/Protocolos.aspx";
            LoadingPanel.Show();
        }

        function ValidateOnClick(s, e) {
            if (ASPxClientEdit.ValidateEditorsInContainer(null)) {
                LoadingPanel.Show();
                return true;
            } else {
                return false;
            };
        };

        var getFuentes = null;

        function OnFuentesChanged(fuente) {
       
            if (cmbSubFuente.InCallback()) {

                getFuentes = fuente.GetValue().toString();
                LoadingPanel.Hide();
            }
            else {

                cmbSubFuente.PerformCallback(fuente.GetValue().toString());
            }
        };

        function MostrarPopupTomador() {
            pcBuscarTomador.Show();
        }

        function MostrarPopupSolicitante() {
            pcBuscarSolicitante.Show();
        }

        function popupResultado() {
            pcCargarResultado.Show();
        }

    </script>

</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

    <div class="page-header page-header-light shadow">
        <div class="page-header-content d-lg-flex border-top">
            <div class="d-flex">
                <div class="breadcrumb py-2">
                    <a href="<%= Web.Util.Helpers.GetApplicationPath() %>/Index.aspx" class="breadcrumb-item"><i class="icon-home2 mr-2"></i>Inicio</a>
                    <a href="<%= Web.Util.Helpers.GetApplicationPath() %>/EPAS/Lista/Protocolos.aspx" class="breadcrumb-item"><i class="icon-display me-2"></i>Protocolo</a>
                    <span class="breadcrumb-item active">
                        <asp:Literal ID="ltr_nombreBreadcrumb" runat="server">Nuevo Protocolo</asp:Literal>
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- /page header -->

    <div class="row gx-1" runat="server">

        <div class="col-lg-12">
            <div class="navbar navbar-expand-xl bg-secondary navbar-dark rounded-top mt-1 py-0">
                <div class="container-fluid">
                    <div class="nav-item dropdown">
                        <h5 class="mt-2 mb-1 fs-5" data-bs-toggle="tab">Mantenimiento de protocolo</h5>
                    </div>
                </div>
            </div>

            <div class="row card my-0">
                <div class="col-lg-12 card-body">
                    <div action="#">
                        <div class="row mb-1 ms-3">
                            <label class="col-lg-1 px-0 col-form-label fw-semibold ">N° de Protocolo</label>
                            <div class="col-lg-1 ps-1">
                                <dx:ASPxTextBox ID="txtNroProtocolo" ReadOnly="true" ClientInstanceName="txtBarraBusqueda" CssClass="form-control altura-textbox" AutoCompleteType="Disabled" runat="server">
                                </dx:ASPxTextBox>
                            </div>


                            <label class="col-lg-1 col-form-label fw-semibold ps-4 pe-1">Solicitante</label>
                            <div class="col-lg-3 ps-1 pe-0">
                                <dx:ASPxTextBox ID="txtSolicitante" ClientInstanceName="txtSolicitante" ReadOnly="true" CssClass="form-control altura-textbox" AutoCompleteType="Disabled" runat="server">
                                </dx:ASPxTextBox>
                                <asp:HiddenField ID="hf_idSolicitante" runat="server" ClientIDMode="Static" Value="" />
                            </div>

                            <div class="col-lg-1 ms-2">
                                <dx:ASPxButton ID="btnBuscarSolicitante" runat="server" CausesValidation="false" AutoPostBack="false" UseSubmitBehavior="false" CssClass="btn btn-secondary px-0 py-1" ClientInstanceName="btnBuscarSolicitante" Text="🔎">
                                    <ClientSideEvents Click="function(s, e) { MostrarPopupSolicitante();  }" />
                                </dx:ASPxButton>
                            </div>


                            <%--Popup para buscar un Solicitante en base de datos--%>

                            <dx:ASPxPopupControl ID="pcBuscarSolicitante" runat="server" Width="1000" CloseOnEscape="true" Modal="True" PopupHorizontalAlign="WindowCenter" PopupVerticalAlign="WindowCenter" ClientInstanceName="pcBuscarSolicitante" CloseAction="CloseButton" HeaderText="Buscar Solicitante" AllowDragging="True" PopupAnimationType="None" EnableViewState="False" AutoUpdatePosition="false">

                                <ContentCollection>
                                    <dx:PopupControlContentControl runat="server">
                                        <dx:ASPxFormLayout runat="server" ID="ASPxFormLayout3" Width="100%" Height="100%">
                                            <Items>
                                                <dx:LayoutItem Caption="">
                                                    <LayoutItemNestedControlCollection>
                                                        <dx:LayoutItemNestedControlContainer>

                                                            <div class="row mb-1 ms-3">
                                                                <label class="col-lg-1 px-0 col-form-label fw-semibold btn-buscar">Buscar por</label>
                                                                <div class="col-lg-3 pe-0">
                                                                    <dx:ASPxComboBox ID="cmbBuscarSolicitante" NullText="Seleccione Búsqueda" CssClass="form-control altura-textbox" runat="server" EnableCallbackMode="true" DropDownStyle="DropDown">
                                                                        <Items>
                                                                            <dx:ListEditItem Text="N° de Solicitante" Value="0" />
                                                                            <dx:ListEditItem Text="DNI / CUIT" Value="1" />
                                                                            <dx:ListEditItem Text="Nombre / Razon Social" Value="2" />
                                                                        </Items>
                                                                    </dx:ASPxComboBox>
                                                                </div>

                                                                <div class="col-lg-5 form-control-feedback form-control-feedback-start flex-grow-1">
                                                                    <dx:ASPxTextBox ID="txtBarraBusquedaSolicitante" ClientInstanceName="txtBarraBusquedaSolicitante" CssClass="form-control altura-textbox" NullText="N° Solicitante, DNI/CUIT o Nombre" AutoCompleteType="Disabled" runat="server">
                                                                    </dx:ASPxTextBox>
                                                                </div>

                                                                <div class="col-lg-1 form-control-feedback form-control-feedback-start flex-grow-1">
                                                                    <dx:ASPxButton ID="btnBusquedaSolicitante" AutoPostBack="false" CausesValidation="false" runat="server" Style="float: right; margin-right: 20px" CssClass="btn btn-secondary px-0 py-1" ClientInstanceName="btnBusquedaSolicitante" Text="🔎">
                                                                        <ClientSideEvents Click="function(s,e){ LoadingPanel.Show();GrVSolicitante.PerformCallback();}" />
                                                                    </dx:ASPxButton>
                                                                </div>
                                                            </div>

                                                            <div class="row">
                                                                <div class="col-lg-12 card-body">
                                                                    <div class="row mt-2 align-content-center d-flex justify-content-center">

                                                                        <dx:ASPxGridView ID="GrVSolicitante" CssClass="fw-medium" OnDataBinding="GrVSolicitante_DataBinding" EnableRowsCache="false" ViewStateMode="Disabled" DataSourceForceStandardPaging="false" EnableViewState="false" AllowOnlyOneAdaptiveDetailExpanded="true" AutoGenerateColumns="False" KeyFieldName="Id" Width="100%" ClientInstanceName="GrVSolicitante" runat="server">
                                                                            <ClientSideEvents EndCallback="function(s,e){ LoadingPanel.Hide();}" />
                                                                            <Columns>

                                                                                <dx:GridViewDataColumn Caption="N° de Muestreador" HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center" Width="12%">
                                                                                    <DataItemTemplate>
                                                                                        <dx:ASPxLabel ID="lblNroSolicitante" CssClass="fw-semibold" runat="server" Font-Size="Small" Theme="Moderno" Text='<%# String.Format( "{0}", Eval("NroContrato")) %>'>
                                                                                        </dx:ASPxLabel>
                                                                                    </DataItemTemplate>
                                                                                </dx:GridViewDataColumn>

                                                                                <dx:GridViewDataColumn Caption="Nombre" HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center">
                                                                                    <DataItemTemplate>
                                                                                        <dx:ASPxLabel ID="lblNombre" CssClass="fw-semibold" runat="server" Font-Size="Small" Theme="Moderno" Text='<%# String.Format( "{0}", Eval("Nombre")) %>'>
                                                                                        </dx:ASPxLabel>
                                                                                    </DataItemTemplate>
                                                                                </dx:GridViewDataColumn>

                                                                                <dx:GridViewDataColumn Caption="Razón Social" HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center">
                                                                                    <DataItemTemplate>
                                                                                        <dx:ASPxLabel ID="lblRazonSocial" CssClass="fw-semibold" runat="server" Font-Size="Small" Theme="Moderno" Text='<%# String.Format( "{0}", Eval("RazonSocial")) %>'>
                                                                                        </dx:ASPxLabel>
                                                                                    </DataItemTemplate>
                                                                                </dx:GridViewDataColumn>

                                                                                <dx:GridViewDataColumn Caption="Tipo y </br> N° de Doc." HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center">
                                                                                    <DataItemTemplate>
                                                                                        <dx:ASPxLabel ID="lblTipoNroDoc" CssClass="fw-semibold" runat="server" Font-Size="Small" Theme="Moderno" Text='<%# String.Format( "{0} {1}", Eval("DocTipos.Abreviatura"), Eval("IdentificacionTributaria")) %>'>
                                                                                        </dx:ASPxLabel>
                                                                                    </DataItemTemplate>
                                                                                </dx:GridViewDataColumn>

                                                                                <dx:GridViewDataTextColumn Caption="Activo" HeaderStyle-HorizontalAlign="Center" Visible="true" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="true" CellStyle-HorizontalAlign="Center" Width="100">
                                                                                    <Settings AllowAutoFilter="false" />
                                                                                    <DataItemTemplate>
                                                                                        <dx:ASPxImage ID="imgAlta" runat="server" Visible='<%# !Convert.ToBoolean(Eval("Baja")) %>' ShowLoadingImage="true" ImageUrl="~/App_Themes/Tema1/img/check.png" Width="30px">
                                                                                        </dx:ASPxImage>
                                                                                        <dx:ASPxImage ID="imgBaja" runat="server" Visible='<%# Eval("Baja")  %>' ShowLoadingImage="true" ImageUrl="~/App_Themes/Tema1/img/no.png" Width="30px"></dx:ASPxImage>
                                                                                    </DataItemTemplate>

                                                                                    <DataItemTemplate>
                                                                                        <dx:ASPxImage ID="imgAlta" runat="server" Visible='<%# !Convert.ToBoolean(Eval("Baja")) %>' ShowLoadingImage="true" ImageUrl="~/App_Themes/Tema1/img/check.png" Width="30px">
                                                                                        </dx:ASPxImage>
                                                                                        <dx:ASPxImage ID="imgBaja" runat="server" Visible='<%# Eval("Baja")  %>' ShowLoadingImage="true" ImageUrl="~/App_Themes/Tema1/img/no.png" Width="30px"></dx:ASPxImage>
                                                                                    </DataItemTemplate>
                                                                                </dx:GridViewDataTextColumn>

                                                                                <dx:GridViewDataColumn Caption="" HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center" Width="12%">
                                                                                    <DataItemTemplate>
                                                                                        <dx:ASPxButton ID="btnSeleccionarSolicitante" runat="server" ClientInstanceName="btnSeleccionarSolicitante" RenderMode="Link" Text="Seleccionar" OnInit="btnSeleccionarSolicitante_Init" CssClass="fw-semibold text-decoration-none text-secondary" Font-Size="Small" Theme="Moderno" CommandArgument='<%# Eval("Id")%>' AutoPostBack="false">
                                                                                            <ClientSideEvents Click="function (s,e){ LoadingPanel.Show(); }" />
                                                                                        </dx:ASPxButton>
                                                                                    </DataItemTemplate>
                                                                                </dx:GridViewDataColumn>

                                                                            </Columns>

                                                                            <SettingsDataSecurity AllowInsert="false" />
                                                                            <EditFormLayoutProperties>
                                                                                <SettingsAdaptivity AdaptivityMode="SingleColumnWindowLimit" SwitchToSingleColumnAtWindowInnerWidth="700" />
                                                                            </EditFormLayoutProperties>
                                                                            <SettingsPopup>
                                                                                <EditForm Width="600">
                                                                                    <SettingsAdaptivity Mode="OnWindowInnerWidth" SwitchAtWindowInnerWidth="768" />
                                                                                </EditForm>
                                                                            </SettingsPopup>
                                                                        </dx:ASPxGridView>

                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </dx:LayoutItemNestedControlContainer>
                                                    </LayoutItemNestedControlCollection>
                                                </dx:LayoutItem>
                                            </Items>
                                        </dx:ASPxFormLayout>

                                    </dx:PopupControlContentControl>
                                </ContentCollection>
                                <ContentStyle>
                                    <Paddings PaddingBottom="5px" />
                                </ContentStyle>
                            </dx:ASPxPopupControl>

                            <%--Aquí finaliza el Popup de Solicitante--%>

                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-12 navbar navbar-expand-xl bg-secondary navbar-dark rounded-top mt-1 py-0">
            <div class="container-fluid">
                <div class="nav-item dropdown">
                    <h5 class="mt-2 mb-1 fs-5" data-bs-toggle="tab"></h5>
                </div>
            </div>
        </div>

        <div class="row gx-1">
            <div class="card col-lg-12">
                <div class="card-header py-0">
                    <p class="mb-0 fw-bold fs-5">Identificación</p>
                </div>

                <fieldset><legend class="col-lg-12 border-secondary border-bottom border-width-3"></legend>
                </fieldset>

                <div class="row gx-1">
                    <div class="card-body col-lg-12">
                        <div action="#">
                            <div class="row mb-1">
                                <label class="col-lg-1 col-form-label fw-semibold pe-0">Tipo análisis</label>
                                <div class="col-lg-4 px-0">
                                    <dx:ASPxComboBox ID="cmbAnalisisTipo" ClientInstanceName="CmbTipoAnalisis" runat="server" DropDownStyle="DropDownList" NullText="Seleccionar Tipo de Analisis" TextFormatString="{0} ({1})" Width="100%" CssClass="form-control altura-textbox" IncrementalFilteringMode="Contains" ValueType="System.Int32" EnableSynchronization="False">
                                        <ValidationSettings ValidationGroup="confirmarGeneral" Display="Dynamic" ErrorTextPosition="Bottom" ErrorDisplayMode="ImageWithText" SetFocusOnError="true">
                                            <RequiredField IsRequired="true" ErrorText="Campo Obligatorio." />
                                        </ValidationSettings>
                                    </dx:ASPxComboBox>
                                </div>
                            </div>

                            <div class="row mb-1">
                                <label class="col-lg-1 col-form-label fw-semibold pe-0">Fuente</label>
                                <div class="col-lg-2 ps-0">
                                    <dx:ASPxComboBox ID="cmbFuente" ClientInstanceName="cmbFuente" runat="server" DropDownStyle="DropDownList" NullText="Seleccionar Fuente" TextFormatString="{0} ({1})" Width="100%" CssClass="form-control altura-textbox" IncrementalFilteringMode="Contains" ValueType="System.Int32" EnableSynchronization="False">
                                        <ValidationSettings ValidationGroup="confirmarGeneral" Display="Dynamic" ErrorTextPosition="Bottom" ErrorDisplayMode="ImageWithText" SetFocusOnError="true">
                                            <RequiredField IsRequired="true" ErrorText="Campo Obligatorio." />
                                        </ValidationSettings>
                                        <ClientSideEvents SelectedIndexChanged="function(s, e) { OnFuentesChanged(s); }" />
                                    </dx:ASPxComboBox>
                                </div>

                                <div class="col-lg-2 pe-0">
                                    <dx:ASPxComboBox ID="cmbSubFuente" ClientInstanceName="cmbSubFuente" runat="server" DropDownStyle="DropDownList" TextFormatString="{0} ({1})" OnCallback="cmbSubFuente_Callback" Width="100%" CssClass="form-control altura-textbox" IncrementalFilteringMode="Contains" ValueType="System.Int32" EnableSynchronization="False">
                                        <ValidationSettings ValidationGroup="confirmarGeneral" Display="Dynamic" ErrorTextPosition="Bottom" ErrorDisplayMode="ImageWithText" SetFocusOnError="true">
                                            <RequiredField IsRequired="true" ErrorText="Campo Obligatorio." />
                                        </ValidationSettings>
                                    </dx:ASPxComboBox>
                                </div>
                            </div>

                            <div class="row mb-1">
                                <label class="col-lg-1 col-form-label fw-semibold pe-0">Sitio Toma</label>
                                <div class="col-lg-4 px-0">
                                    <dx:ASPxTextBox ID="txtSitioToma" ClientInstanceName="txtSitioToma" CssClass="form-control altura-textbox" AutoCompleteType="Disabled" runat="server">
                                    </dx:ASPxTextBox>
                                </div>

                                <label class="col-lg-1 col-form-label fw-semibold ps-3 pe-0">Fecha Toma</label>
                                <div class="col-lg-2 pe-0">
                                    <dx:ASPxDateEdit ID="dateFechaToma" ClientInstanceName="dateFechaToma" EditFormatString="dd/MM/yyyy - hh:mm tt" TimeSectionProperties-Visible="true" Theme="Moderno" runat="server" EditFormat="Custom">
                                        <TimeSectionProperties>
                                            <TimeEditProperties EditFormatString="hh:mm tt" />
                                        </TimeSectionProperties>
                                        <CalendarProperties>
                                            <FastNavProperties DisplayMode="Inline" />
                                        </CalendarProperties>
                                    </dx:ASPxDateEdit>
                                </div>

                                <div class="col-lg-2 ps-4" style="display:none">
                                    <dx:ASPxLabel ID="lblImprimirLeyenda" runat="server" Text="Imprimir leyenda" CssClass="col-form-label me-1 fw-semibold"></dx:ASPxLabel>
                                    <dx:ASPxCheckBox ID="chbImprimirLeyenda" CssClass="mt-1" Checked="true" ClientInstanceName="chbImprimirLeyenda" runat="server" ToggleSwitchDisplayMode="Always">
                                    </dx:ASPxCheckBox>
                                </div>

                                <div class="col-lg-2" style="display:none">
                                    <dx:ASPxLabel ID="lblUnidadesPrimarias" runat="server" Text="Unidades primarias" CssClass="col-form-label me-1 fw-semibold"></dx:ASPxLabel>
                                    <dx:ASPxCheckBox ID="chbUnidadesPrimarias" CssClass="mt-1" Checked="true" ClientInstanceName="chbUnidadesPrimarias" runat="server" ToggleSwitchDisplayMode="Always">
                                    </dx:ASPxCheckBox>
                                </div>

                            </div>

                            <div class="row mb-1">
                                <label class="col-lg-1 col-form-label fw-semibold pe-0">Muestreador</label>
                                <div class="col-lg-3 px-0">
                                    <dx:ASPxTextBox ID="txtTomadorMuestra" ClientInstanceName="txtTomadorMuestra" ReadOnly="true" CssClass="form-control altura-textbox" runat="server">
                                    </dx:ASPxTextBox>
                                    <asp:HiddenField ID="hf_idTomador" ClientIDMode="Static" runat="server" Value="" />
                                </div>
                                <div class="col-lg-1">
                                    <dx:ASPxButton ID="btnBuscarTomador" runat="server" CausesValidation="false" AutoPostBack="false" UseSubmitBehavior="false" CssClass="btn btn-secondary px-0 py-1" ClientInstanceName="btnBuscarTomador" Text="🔎">
                                        <ClientSideEvents Click="function(s, e) { MostrarPopupTomador();  }" />
                                    </dx:ASPxButton>
                                </div>


                                <%--Popup para buscar un Tomador de base de datos--%>

                                <dx:ASPxPopupControl ID="pcBuscarTomador" runat="server" Width="1000" CloseOnEscape="true" Modal="True" PopupHorizontalAlign="WindowCenter" PopupVerticalAlign="WindowCenter" ClientInstanceName="pcBuscarTomador" CloseAction="CloseButton" HeaderText="Buscar Muestreador" AllowDragging="True" PopupAnimationType="None" EnableViewState="False" AutoUpdatePosition="false">

                                    <ContentCollection>
                                        <dx:PopupControlContentControl runat="server">
                                            <dx:ASPxFormLayout runat="server" ID="ASPxFormLayout1" Width="100%" Height="100%">
                                                <Items>
                                                    <dx:LayoutItem Caption="">
                                                        <LayoutItemNestedControlCollection>
                                                            <dx:LayoutItemNestedControlContainer>

                                                                <div class="row mb-1 ms-3">
                                                                    <label class="col-lg-1 px-0 col-form-label fw-semibold btn-buscar">Buscar por</label>
                                                                    <div class="col-lg-3 pe-0">
                                                                        <dx:ASPxComboBox ID="cmbBuscarTomador" NullText="Seleccione Búsqueda" CssClass="form-control altura-textbox" runat="server" EnableCallbackMode="true" DropDownStyle="DropDown">
                                                                            <Items>
                                                                                <dx:ListEditItem Text="N° de Muestreador" Value="0" />
                                                                                <dx:ListEditItem Text="DNI / CUIT" Value="1" />
                                                                                <dx:ListEditItem Text="Nombre / Razon Social" Value="2" />
                                                                            </Items>
                                                                        </dx:ASPxComboBox>
                                                                    </div>

                                                                    <div class="col-lg-5 form-control-feedback form-control-feedback-start flex-grow-1">
                                                                        <dx:ASPxTextBox ID="txtBarraBusquedaTomador" ClientInstanceName="txtBarraBusquedaTomador" CssClass="form-control altura-textbox" NullText="N° Muestreador, DNI/CUIT o Nombre" AutoCompleteType="Disabled" runat="server">
                                                                        </dx:ASPxTextBox>
                                                                    </div>

                                                                    <div class="col-lg-1 form-control-feedback form-control-feedback-start flex-grow-1">
                                                                        <dx:ASPxButton ID="btnBusquedaTomador" AutoPostBack="false" runat="server" Style="float: right; margin-right: 20px" CssClass="btn btn-secondary px-0 py-1" CausesValidation="false" ClientInstanceName="btnBusquedaTomador" Text="🔎">
                                                                            <ClientSideEvents Click="function(s,e){ LoadingPanel.Show();GrVTomador.PerformCallback();}" />
                                                                        </dx:ASPxButton>
                                                                    </div>
                                                                </div>

                                                                <div class="row">
                                                                    <div class="col-lg-12 card-body">
                                                                        <div class="row mt-2 align-content-center d-flex justify-content-center">

                                                                            <dx:ASPxGridView ID="GrVTomador" CssClass="fw-medium" OnDataBinding="GrVTomador_DataBinding" EnableRowsCache="false" ViewStateMode="Disabled" DataSourceForceStandardPaging="false" EnableViewState="false" AllowOnlyOneAdaptiveDetailExpanded="true" AutoGenerateColumns="False" KeyFieldName="Id" Width="100%" ClientInstanceName="GrVTomador" runat="server">
                                                                                <ClientSideEvents EndCallback="function(s,e){ LoadingPanel.Hide();}" />
                                                                                <Columns>

                                                                                    <dx:GridViewDataColumn Caption="N° de Muestreador" HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center" Width="12%">
                                                                                        <DataItemTemplate>
                                                                                            <dx:ASPxLabel ID="lblNroSolicitante" CssClass="fw-semibold" runat="server" Font-Size="Small" Theme="Moderno" Text='<%# String.Format( "{0}", Eval("NroContrato")) %>'>
                                                                                            </dx:ASPxLabel>
                                                                                        </DataItemTemplate>
                                                                                    </dx:GridViewDataColumn>

                                                                                    <dx:GridViewDataColumn Caption="Nombre" HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center">
                                                                                        <DataItemTemplate>
                                                                                            <dx:ASPxLabel ID="lblNombre" CssClass="fw-semibold" runat="server" Font-Size="Small" Theme="Moderno" Text='<%# String.Format( "{0}", Eval("Nombre")) %>'>
                                                                                            </dx:ASPxLabel>
                                                                                        </DataItemTemplate>
                                                                                    </dx:GridViewDataColumn>

                                                                                    <dx:GridViewDataColumn Caption="Razón Social" HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center">
                                                                                        <DataItemTemplate>
                                                                                            <dx:ASPxLabel ID="lblRazonSocial" CssClass="fw-semibold" runat="server" Font-Size="Small" Theme="Moderno" Text='<%# String.Format( "{0}", Eval("RazonSocial")) %>'>
                                                                                            </dx:ASPxLabel>
                                                                                        </DataItemTemplate>
                                                                                    </dx:GridViewDataColumn>

                                                                                    <dx:GridViewDataColumn Caption="Tipo y </br> N° de Doc." HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center">
                                                                                        <DataItemTemplate>
                                                                                            <dx:ASPxLabel ID="lblTipoNroDoc" CssClass="fw-semibold" runat="server" Font-Size="Small" Theme="Moderno" Text='<%# String.Format( "{0} {1}", Eval("DocTipos.Abreviatura"), Eval("IdentificacionTributaria")) %>'>
                                                                                            </dx:ASPxLabel>
                                                                                        </DataItemTemplate>
                                                                                    </dx:GridViewDataColumn>

                                                                                    <dx:GridViewDataTextColumn Caption="Activo" HeaderStyle-HorizontalAlign="Center" Visible="true" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="true" CellStyle-HorizontalAlign="Center" Width="100">
                                                                                        <Settings AllowAutoFilter="false" />
                                                                                        <DataItemTemplate>
                                                                                            <dx:ASPxImage ID="imgAlta" runat="server" Visible='<%# !Convert.ToBoolean(Eval("Baja")) %>' ShowLoadingImage="true" ImageUrl="~/App_Themes/Tema1/img/check.png" Width="30px">
                                                                                            </dx:ASPxImage>
                                                                                            <dx:ASPxImage ID="imgBaja" runat="server" Visible='<%# Eval("Baja")  %>' ShowLoadingImage="true" ImageUrl="~/App_Themes/Tema1/img/no.png" Width="30px">
                                                                                            </dx:ASPxImage>
                                                                                        </DataItemTemplate>

                                                                                        <DataItemTemplate>
                                                                                            <dx:ASPxImage ID="imgAlta" runat="server" Visible='<%# !Convert.ToBoolean(Eval("Baja")) %>' ShowLoadingImage="true" ImageUrl="~/App_Themes/Tema1/img/check.png" Width="30px">
                                                                                            </dx:ASPxImage>
                                                                                            <dx:ASPxImage ID="imgBaja" runat="server" Visible='<%# Eval("Baja")  %>' ShowLoadingImage="true" ImageUrl="~/App_Themes/Tema1/img/no.png" Width="30px">
                                                                                            </dx:ASPxImage>
                                                                                        </DataItemTemplate>
                                                                                    </dx:GridViewDataTextColumn>

                                                                                    <dx:GridViewDataColumn Caption="" HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center" Width="12%">
                                                                                        <DataItemTemplate>
                                                                                            <dx:ASPxButton ID="btnSeleccionarTomador" runat="server" ClientInstanceName="btnSeleccionarTomador" RenderMode="Link" Text="Seleccionar" OnInit="btnSeleccionarTomador_Init" CssClass="fw-semibold text-decoration-none text-secondary" Font-Size="Small" Theme="Moderno" CommandArgument='<%# Eval("Id")%>' AutoPostBack="false">
                                                                                                <ClientSideEvents Click="function (s,e){ LoadingPanel.Show(); }" />
                                                                                            </dx:ASPxButton>
                                                                                        </DataItemTemplate>
                                                                                    </dx:GridViewDataColumn>

                                                                                </Columns>

                                                                                <SettingsDataSecurity AllowInsert="false" />
                                                                                <EditFormLayoutProperties>
                                                                                    <SettingsAdaptivity AdaptivityMode="SingleColumnWindowLimit" SwitchToSingleColumnAtWindowInnerWidth="700" />
                                                                                </EditFormLayoutProperties>
                                                                                <SettingsPopup>
                                                                                    <EditForm Width="600">
                                                                                        <SettingsAdaptivity Mode="OnWindowInnerWidth" SwitchAtWindowInnerWidth="768" />
                                                                                    </EditForm>
                                                                                </SettingsPopup>
                                                                            </dx:ASPxGridView>

                                                                        </div>
                                                                    </div>
                                                                </div>

                                                            </dx:LayoutItemNestedControlContainer>
                                                        </LayoutItemNestedControlCollection>
                                                    </dx:LayoutItem>
                                                </Items>
                                            </dx:ASPxFormLayout>

                                        </dx:PopupControlContentControl>
                                    </ContentCollection>
                                    <ContentStyle>
                                        <Paddings PaddingBottom="5px" />
                                    </ContentStyle>
                                </dx:ASPxPopupControl>


                                <label class="col-lg-1 col-form-label fw-semibold ps-3 pe-0">Recep. Toma</label>
                                <div class="col-lg-2 pe-0">
                                    <dx:ASPxDateEdit ID="dateRecepToma" ClientInstanceName="dateRecepToma" EditFormatString="dd/MM/yyyy - hh:mm tt" TimeSectionProperties-Visible="true" Theme="Moderno" runat="server" EditFormat="Custom">
                                        <TimeSectionProperties>
                                            <TimeEditProperties EditFormatString="hh:mm tt" />
                                        </TimeSectionProperties>
                                        <CalendarProperties>
                                            <FastNavProperties DisplayMode="Inline" />
                                        </CalendarProperties>
                                    </dx:ASPxDateEdit>
                                </div>
                            </div>

                            <div class="row mb-1">
                                <label class="col-lg-1 col-form-label fw-semibold">Módulo</label>
                                <div class="col-lg-2 ps-0 pe-2">
                                    <dx:ASPxTextBox ID="txtModulo" ClientInstanceName="txtModulo" CssClass="form-control altura-textbox" AutoCompleteType="Disabled" runat="server">
                                        <ValidationSettings ErrorTextPosition="Bottom" Display="Dynamic" ErrorDisplayMode="ImageWithText">
                                            <RequiredField IsRequired="True" ErrorText="Campo Obligatorio." />
                                            <RegularExpression ValidationExpression="^[0-9]+(,[0-9]+)*$" ErrorText="Error" />
                                        </ValidationSettings>
                                    </dx:ASPxTextBox>
                                </div>

                                <div class="col-lg-1"></div>

                                <label class="col-lg-1 col-form-label fw-semibold px-0">Monto total</label>
                                <div class="col-lg-1 px-0">
                                    <dx:ASPxSpinEdit ID="seMontoTotal" ClientInstanceName="seMontoTotal" SelectInputTextOnClick="true" SpinButtons-ClientVisible="false" NullText="0.00" Height="40px" CssClass="form-control altura-textbox" DecimalPlaces="2" DisplayFormatString="${0:N2}" Width="125px" runat="server">
                                    </dx:ASPxSpinEdit>
                                </div>

                                <div class="col-lg-1"></div>

                                <label class="col-lg-1 col-form-label fw-semibold px-0">Expediente</label>
                                <div class="col-lg-4 px-0">
                                    <dx:ASPxTextBox ID="txtExpediente" ClientInstanceName="txtExpediente1" CssClass="form-control altura-textbox" AutoCompleteType="Disabled" runat="server">
                                        <MaskSettings ShowHints="true" Mask="aaaa - aaaa - aaaa - aaaa" />
                                    </dx:ASPxTextBox>
                                </div>
                            </div>

                            <div class="row mb-1">
                                <label class="col-lg-1 col-form-label fw-semibold pe-0">Laboratorio</label>
                                <div class="col-lg-3 px-0">
                                    <dx:ASPxTextBox ID="txtLaboratorio" ClientInstanceName="txtNro" CssClass="form-control altura-textbox" AutoCompleteType="Disabled" runat="server">
                                    </dx:ASPxTextBox>
                                </div>

                                <label class="col-lg-2 col-form-label fw-semibold ps-5">Opciones resultados</label>
                                <div class="col-lg-2 ps-0">
                                    <dx:ASPxTextBox ID="txtOpcionesResultados" ClientInstanceName="txtNro" CssClass="form-control altura-textbox" AutoCompleteType="Disabled" runat="server">
                                    </dx:ASPxTextBox>
                                </div>

                                <label class="col-lg-1 col-form-label fw-semibold pe-0">Informes</label>
                                <div class="col-lg-3 px-0">
                                    <dx:ASPxTextBox ID="txtInformes" ClientInstanceName="txtNro" CssClass="form-control altura-textbox" AutoCompleteType="Disabled" runat="server">
                                    </dx:ASPxTextBox>
                                </div>
                            </div>

                            <div class="row mt-1">
                                <label class="col-lg-1 col-form-label fw-semibold pe-0">Observación</label>
                                <div class="col-lg-11 p-0">
                                    <dx:ASPxMemo ID="memoObservacion" CssClass="form-control" SkinID="None" runat="server" Native="True" Height="100%" Width="100%">
                                    </dx:ASPxMemo>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row gx-1">
                    <div class="card col-lg-12">
                        <div class="card-header py-0 bg-secondary">
                            <div class="row my-1">
                                <div class="col-lg-2">
                                    <h5 class="mt-2 mb-1 fs-5 fw-bold text-white">Veredicto</h5>
                                </div>
                            </div>
                        </div>

                        <div class="row gx-1 my-0">
                            <div class="card-body col-lg-12 ps-5">
                                <div action="#">
                                    <div class="row">
                                        <div class="col-lg-4">
                                            <dx:ASPxRadioButtonList ID="rbVeredicto" Theme="Moderno" runat="server" CssClass="customCheckBoxList" Font-Bold="true" RepeatColumns="3" Border-BorderWidth="0" RepeatLayout="Flow">
                                                <Items>
                                                    <dx:ListEditItem Value="0" Text="Buena" />
                                                    <dx:ListEditItem Value="1" Text="Deficiente" />
                                                    <dx:ListEditItem Value="2" Text="Sin Veredicto" />
                                                </Items>
                                            </dx:ASPxRadioButtonList>
                                        </div>

                                        <div class="col-lg-8">
                                            <div class="row mb-1">
                                                <label class="col-lg-2 pe-0 col-form-label fw-semibold">Observaciones</label>
                                                <div class="col-lg-9">
                                                    <dx:ASPxMemo ID="memoObservacionVeredicto" CssClass="form-control" SkinID="None" runat="server" Native="True" Height="100%" Width="100%">
                                                    </dx:ASPxMemo>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row gx-1">
                    <div class="card col-lg-12">
                        <div class="card-header py-0 bg-secondary">
                            <div class="row my-1">
                                <div class="col-lg-3">
                                    <h5 class="mt-2 mb-1 fs-5 fw-bold text-white">Determinaciones protocolo</h5>
                                </div>
                            </div>
                        </div>

                        <div class="card col-lg-12">
                            <div class="row gx-1">

                                <!-- Grilla Determinaciones -->

                                <div class="card-body col-lg-6">
                                    <div action="#">
                                        <div class="row">
                                            <label class="col-lg-2 col-form-label fw-semibold">Seleccionar</label>
                                            <div class="col-lg-8 ps-0">

                                                <dx:ASPxComboBox ID="CmbDeterminaciones" ClientInstanceName="CmbDeterminaciones" runat="server" DropDownStyle="DropDownList" TextFormatString="{1} ({0})" NullText="Deteminación..." Width="100%" CssClass="form-control altura-textbox" IncrementalFilteringMode="Contains" TextField="NombreDeterminacion" ValueType="System.Int32" EnableSynchronization="False">
                                                    <Columns>
                                                        <dx:ListBoxColumn FieldName="NroDeterminacion" Caption="Nro." Width="12%" />
                                                        <dx:ListBoxColumn FieldName="Descripcion" Caption="Descripción" />
                                                         <dx:ListBoxColumn FieldName="UnidadPrimaria" Caption="Un." />
                                                        <dx:ListBoxColumn FieldName="ValorReferencia" Caption="Valor de Ref." />
                                                    </Columns>
                                                </dx:ASPxComboBox>
                                            </div>

                                            <div class="col-lg-2 form-control-feedback form-control-feedback-start flex-grow-1">
                                                <dx:ASPxButton ID="btnAgregarDeterminacion" RenderMode="Link" OnClick="btnAgregarDeterminacion_Click" runat="server" CssClass="fw-medium rounded-2 text-white bg-info border border-secondary text-decoration-none p-2" ClientInstanceName="btnAgregarDeterminacion" AutoPostBack="false" CausesValidation="false" Text="Agregar">
                                                </dx:ASPxButton>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="card-body col-lg-6">
                                    <div action="#">
                                        <div class="row">
                                            <label class="col-lg-2 ps-3 pe-0 col-form-label fw-semibold">Seleccionar</label>
                                            <div class="col-lg-8 ps-0">
                                                <dx:ASPxComboBox ID="CmbPlantilla" ValueType="System.Int32" ClientInstanceName="CmbPlantilla" runat="server" DropDownStyle="DropDownList" TextFormatString="{1} ({0})" Width="100%" NullText="Plantilla..." CssClass="form-control altura-textbox" IncrementalFilteringMode="Contains" TextField="NombreDeterminacion" EnableSynchronization="False">
                                                    <Columns>
                                                        <dx:ListBoxColumn FieldName="Numero" Caption="Nro." Width="12%" />
                                                        <dx:ListBoxColumn FieldName="Descripcion" Caption="Descripción" />
                                                    </Columns>
                                                </dx:ASPxComboBox>
                                            </div>

                                            <div class="col-lg-2 form-control-feedback form-control-feedback-start flex-grow-1">
                                                <dx:ASPxButton ID="btnAgregarPlantilla" RenderMode="Link" OnClick="btnAgregarPlantilla_Click" runat="server" CssClass="fw-medium rounded-2 text-white bg-info border border-secondary text-decoration-none p-2" ClientInstanceName="btnAgregarPlantilla" AutoPostBack="false" CausesValidation="false" Text="Agregar">
                                                </dx:ASPxButton>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="card-body col-lg-12">
                                    <div action="#">
                                        <div class="row">

                                            <dx:ASPxGridView ID="gvDeterminaciones" CssClass="fw-medium border border-black rounded" EnableRowsCache="false" ViewStateMode="Disabled" OnDataBinding="gvDeterminaciones_DataBinding" DataSourceForceStandardPaging="false" EnableViewState="false" KeyFieldName="Id" Width="100%" ClientInstanceName="gvDeterminaciones" AutoGenerateColumns="False" runat="server">

                                                <Toolbars>
                                                    <dx:GridViewToolbar ItemAlign="Right" EnableAdaptivity="true">
                                                        <Items>
                                                            <dx:GridViewToolbarItem Text="Exportar a" Image-IconID="actions_refresh_16x16office2013" DisplayMode="ImageWithText" BeginGroup="true">
                                                                <Items>
                                                                    <dx:GridViewToolbarItem Command="ExportToPdf" Text="Exportar a PDF" />
                                                                    <dx:GridViewToolbarItem Command="ExportToXlsx" Text="Exportar a Excel" />
                                                                </Items>
                                                            </dx:GridViewToolbarItem>
                                                            <dx:GridViewToolbarItem BeginGroup="true">
                                                                <Template>
                                                                    <dx:ASPxButtonEdit ID="tbToolbarSearch" runat="server" NullText="Buscar..." Height="100%" CssClass="form-control">
                                                                        <Buttons>
                                                                            <dx:SpinButtonExtended Image-IconID="find_find_16x16gray" />
                                                                        </Buttons>
                                                                    </dx:ASPxButtonEdit>
                                                                </Template>
                                                            </dx:GridViewToolbarItem>
                                                        </Items>
                                                    </dx:GridViewToolbar>
                                                </Toolbars>
                                                <SettingsSearchPanel CustomEditorID="tbToolbarSearch" />

                                                <SettingsExport EnableClientSideExportAPI="true" ExcelExportMode="DataAware" />
                                                <ClientSideEvents ToolbarItemClick="OnToolbarItemClick" />
                                                <SettingsBehavior FilterRowMode="Auto" AllowDragDrop="false" AllowEllipsisInText="true" />
                                                <SettingsPager PageSize="20" AlwaysShowPager="true"></SettingsPager>
                                                <Columns>

                                                    <dx:GridViewDataColumn FieldName="Determinaciones.NroDeterminacion" Caption="N° de <br/> Det." HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center" Width="10%">
                                                        <DataItemTemplate>
                                                            <dx:ASPxLabel ID="lblNroDeterminacion" CssClass="fw-semibold" runat="server" Font-Size="Small" Theme="Moderno" Text='<%# String.Format( "{0}", Eval("Determinaciones.NroDeterminacion")) %>'>
                                                            </dx:ASPxLabel>
                                                        </DataItemTemplate>
                                                    </dx:GridViewDataColumn>

                                                    <dx:GridViewDataColumn FieldName="Determinaciones.Descripcion" Caption="Descripción" HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center">
                                                        <DataItemTemplate>
                                                            <dx:ASPxLabel ID="lblDescripcion" CssClass="fw-semibold" runat="server" Font-Size="Small" Theme="Moderno" Text='<%# String.Format( "{0}", Eval("Determinaciones.Descripcion")) %>'>
                                                            </dx:ASPxLabel>
                                                        </DataItemTemplate>
                                                    </dx:GridViewDataColumn>
                                                        <dx:GridViewDataColumn FieldName="Determinaciones.UnidadPrimaria" Caption="Un." HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center">
        <DataItemTemplate>
            <dx:ASPxLabel ID="lblUnidad" CssClass="fw-semibold" runat="server" Font-Size="Small" Theme="Moderno" Text='<%# String.Format( "{0}", Eval("Determinaciones.UnidadPrimaria")) %>'>
            </dx:ASPxLabel>
        </DataItemTemplate>
    </dx:GridViewDataColumn>
                                                    <dx:GridViewDataColumn FieldName="Determinaciones.ValorReferencia" Caption="Valor de Ref." HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center">
                                                        <DataItemTemplate>
                                                            <dx:ASPxLabel ID="lblValorReferencia" CssClass="fw-semibold" runat="server" Font-Size="Small" Theme="Moderno" Text='<%# String.Format( "{0}", Eval("Determinaciones.ValorReferencia")) %>'>
                                                            </dx:ASPxLabel>
                                                        </DataItemTemplate>
                                                    </dx:GridViewDataColumn>

                                                    <dx:GridViewDataColumn FieldName="Resultado" Caption="Resultado" HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center">
                                                        <DataItemTemplate>
                                                            <dx:ASPxLabel ID="lblValorReferencia" CssClass="fw-semibold" runat="server" Font-Size="Small" Theme="Moderno" Text='<%# String.Format( "{0} {1}", Eval("Signo"), Eval("Resultado")) %>'>
                                                            </dx:ASPxLabel>
                                                        </DataItemTemplate>
                                                    </dx:GridViewDataColumn>

                                                    <dx:GridViewDataColumn Caption="#" HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center" Width="15%">
                                                        <DataItemTemplate>
                                                            <dx:ASPxButton ID="btnCargarResultado" runat="server" CausesValidation="false" AutoPostBack="false" UseSubmitBehavior="false" CssClass="fw-semibold text-secondary text-decoration-none" RenderMode="Link" Text="Cargar Resultado" ClientInstanceName="btnCargarResultado" Visible='<%# GetVisibleCargarResultado(Eval("Protocolos")) %>' OnInit="btnCargarResultado_Init">
                                                            </dx:ASPxButton>
                                                        </DataItemTemplate>
                                                    </dx:GridViewDataColumn>

                                                    <dx:GridViewDataColumn Caption="#" HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center">
                                                        <DataItemTemplate>
                                                            <dx:ASPxButton ID="btnEliminarDeterminacion" runat="server" OnClick="btnEliminarDeterminacion_Click" CssClass="fw-semibold text-secondary text-decoration-none" ClientInstanceName="btnEliminarDeterminacion" RenderMode="Link" Text="Quitar" Visible='<%# GetVisibleQuitar(Eval("Protocolos")) %>' CausesValidation="false" AutoPostBack="false" CommandArgument='<%# Eval("Id")%>'>
                                                                <ClientSideEvents Click="function(s,e){e.processOnServer =confirm('¿Esta seguro que desea eliminar?');}" />
                                                            </dx:ASPxButton>
                                                        </DataItemTemplate>
                                                    </dx:GridViewDataColumn>

                                                </Columns>

                                                <SettingsDataSecurity AllowInsert="false" />
                                                <EditFormLayoutProperties>
                                                    <SettingsAdaptivity AdaptivityMode="SingleColumnWindowLimit" SwitchToSingleColumnAtWindowInnerWidth="700" />
                                                </EditFormLayoutProperties>
                                                <SettingsPopup>
                                                    <EditForm Width="600">
                                                        <SettingsAdaptivity Mode="OnWindowInnerWidth" SwitchAtWindowInnerWidth="768" />
                                                    </EditForm>
                                                </SettingsPopup>
                                            </dx:ASPxGridView>
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <dx:ASPxPopupControl ID="pcCargarResultado" runat="server" Width="650" CloseOnEscape="true" Modal="True" 
                                PopupHorizontalAlign="WindowCenter" PopupVerticalAlign="WindowCenter" 
                                ClientInstanceName="pcCargarResultado" CloseAction="CloseButton" HeaderText="Cargar Resultado" 
                                AllowDragging="True" PopupAnimationType="None" EnableViewState="False" AutoUpdatePosition="true">
                                <ContentCollection>
                                    <dx:PopupControlContentControl runat="server">
                                        <dx:ASPxFormLayout runat="server" ID="ASPxFormLayout2" Width="100%" Height="100%">
                                            <Items>
                                                <dx:LayoutItem Caption="">
                                                    <LayoutItemNestedControlCollection>
                                                        <dx:LayoutItemNestedControlContainer>

                                                            <asp:HiddenField ID="hf_ResultadoDeterminacion" ClientIDMode="Static" runat="server" />
                                                            <div class="row mb-2">
                                                                <label class="col-lg-2 col-form-label fw-semibold">Signo</label>
                                                                <div class="col-lg-1 form-control-feedback form-control-feedback-start flex-grow-1">
                                                                    <dx:ASPxTextBox ID="txtSigno" runat="server" AutoCompleteType="Disabled" ValidationSettings-CausesValidation="false" CssClass="form-control altura-textbox" ClientInstanceName="txtSigno" Width="100">
                                                                       
                                                                    </dx:ASPxTextBox>
                                                                </div>

                                                                <label class="col-lg-2 col-form-label fw-semibold">Resultado</label>
                                                                <div class="col-lg-1 form-control-feedback form-control-feedback-start flex-grow-1">
                                                                    <dx:ASPxTextBox ID="txtResultado" runat="server" AutoCompleteType="Disabled" CssClass="form-control altura-textbox" ClientInstanceName="txtResultado" Width="100%">
                                                                        <ValidationSettings ValidationGroup="resultado" ErrorTextPosition="Bottom" Display="Dynamic" ErrorDisplayMode="ImageWithText">
                                                                            <RequiredField IsRequired="True" ErrorText="Campo Obligatorio." />
                                                                            <RegularExpression ValidationExpression="^[0-9]+(,[0-9]+)*$" ErrorText="Campo solo numérico" />
                                                                        </ValidationSettings>
                                                                    </dx:ASPxTextBox>
                                                                </div>
                                                            </div>

                                                            <div class="row mb-3">
                                                                <label class="col-lg-2 col-form-label fw-semibold pe-0">Observación</label>
                                                                <div class="col-lg-4 form-control-feedback form-control-feedback-start flex-grow-1">
                                                                    <dx:ASPxMemo ID="memoObservacionResultado" CssClass="form-control px-2" SkinID="None" runat="server" Native="True" Height="100%" Width="100%">
                                                                    </dx:ASPxMemo>
                                                                </div>
                                                            </div>

                                                            <div class="row">
                                                                <div class="col-lg-1 form-control-feedback form-control-feedback-start flex-grow-1">
                                                                    <dx:ASPxButton ID="btnGuardarResultado" CausesValidation="true" ValidationGroup="resultado" OnClick="btnGuardarResultado_Click" runat="server" Style="float: right" CssClass="btn btn-info px-0 py-1" ClientInstanceName="btnGuardarResultado" Text="Guardar">
                                                                        <ClientSideEvents Click="function(s,e){ validateCargarResultado()}" />
                                                                    </dx:ASPxButton>
                                                                </div>
                                                            </div>

                                                        </dx:LayoutItemNestedControlContainer>
                                                    </LayoutItemNestedControlCollection>
                                                </dx:LayoutItem>
                                            </Items>
                                        </dx:ASPxFormLayout>
                                    </dx:PopupControlContentControl>
                                </ContentCollection>
                                <ContentStyle>
                                    <Paddings PaddingBottom="5px" />
                                </ContentStyle>
                            </dx:ASPxPopupControl>

                            <div class="row mb-2">
                                <div class="col-lg-2 pe-5 form-control-feedback form-control-feedback-start flex-grow-1 d-flex justify-content-end">
                                    <dx:ASPxButton ID="btnGuardarProtocolo" runat="server" CssClass="btn btn-secondary fw-semibold px-0 py-1" OnClick="btnGuardarProtocolo_Click" ClientInstanceName="btnGuardarProtocolo" AutoPostBack="false" CausesValidation="false" Text="Guardar">
                                    </dx:ASPxButton>

                                    <dx:ASPxButton ID="btnAprobarProtocolo" runat="server" Visible="false" CssClass="btn btn-secondary fw-semibold px-0 py-1" OnClick="btnAprobarProtocolo_Click" ClientInstanceName="btnAprobarProtocolo" AutoPostBack="false" CausesValidation="false" Text="Aprobar">
                                    </dx:ASPxButton>

                                    <dx:ASPxButton ID="btnCancelarProtocolo" runat="server" CssClass="btn text-white fw-semibold px-0 py-1 ms-3" BackColor="Brown" ClientInstanceName="btnCancelarProtocolo" AutoPostBack="False" CausesValidation="false" Text="Cancelar">
                                        <ClientSideEvents Click="function(s,e){ RedireccionarAPaginaPrincipal(); LoadingPanel.Show(); ValidateOnClick(); }" />
                                    </dx:ASPxButton>

                                    <dx:ASPxButton ID="btnRechazarProtocolo" runat="server" Visible="false" OnClick="btnRechazarProtocolo_Click" CssClass="btn text-white fw-semibold px-0 py-1 ms-3" BackColor="Brown" ClientInstanceName="btnRechazarProtocolo" AutoPostBack="False" CausesValidation="false" Text="Rechazar">
                                    </dx:ASPxButton>

                                    <dx:ASPxButton ID="btnVolverProtocolo" runat="server" Visible="false" CssClass="btn text-white fw-semibold px-0 py-1 ms-3" BackColor="Brown" ClientInstanceName="btnVolverProtocolo" AutoPostBack="False" CausesValidation="false" Text="Volver">
                                        <ClientSideEvents Click="function(s,e){ RedireccionarAPProtocolos(); LoadingPanel.Show(); }" />
                                    </dx:ASPxButton>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</asp:Content>


<asp:Content ID="Content3" ContentPlaceHolderID="footer" runat="server">
</asp:Content>
