﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 12/5/2023 11:08:46
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using NHibernate;
using NHibernate.Cfg;

namespace DAO
{
    public class NHibernateSessionProvider
    {

        private static Configuration configuration;
        private static ISessionFactory sessionFactory;

        private NHibernateSessionProvider()
        {
        }

        public static Configuration Configuration
        {
            get
            {
                if (configuration == null)
                {
                    configuration = new Configuration();
                    configuration.Configure();
                }
                return configuration;
            }
        }

        public static ISessionFactory SessionFactory
        {
            get
            {
                if (sessionFactory == null)
                {
                    sessionFactory = Configuration.BuildSessionFactory();
                }
                return sessionFactory;
            }
        }

        public static ISession GetSession()
        {

            return SessionFactory.OpenSession();
        }
    }
}
