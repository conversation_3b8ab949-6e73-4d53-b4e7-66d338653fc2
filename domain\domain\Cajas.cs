﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 22/5/2023 14:55:38
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.Cajas, Domain in the schema.
    /// </summary>
    public partial class Cajas {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for Cajas constructor in the schema.
        /// </summary>
        public Cajas()
        {
            this.Recibos = new HashSet<Recibos>();
            this.Usuarios = new HashSet<Usuario>();
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Descripcion in the schema.
        /// </summary>
        public virtual string Descripcion
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Terminal in the schema.
        /// </summary>
        public virtual string Terminal
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for FechaEnCero in the schema.
        /// </summary>
        public virtual System.Nullable<System.DateTime> FechaEnCero
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Nula in the schema.
        /// </summary>
        public virtual System.Nullable<short> Nula
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for IdUsuario in the schema.
        /// </summary>
        public virtual System.Nullable<int> IdUsuario
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Recibos in the schema.
        /// </summary>
        public virtual ISet<Recibos> Recibos
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Usuarios in the schema.
        /// </summary>
        public virtual ISet<Usuario> Usuarios
        {
            get;
            set;
        }
    }

}
