﻿using DAO;
using Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Contratos {
    public class NumeracionServices {

        public static Numeraciones getByCodigo(string cod) {

            Numeraciones u;

            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {

                u = new NumeracionesRepository(sess).GetByCodigo(cod);

                //Aumento en 1 la numeracion
                Numeraciones aumenta = u;
                aumenta.Numero = aumenta.Numero + 1;
                SaveOrUpdate(aumenta);
                sess.Close();
                sess.Dispose();
                return u;
            }

        }

        public static void SaveOrUpdate(Numeraciones aumenta) {
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                using(NHibernate.ITransaction tx = sess.BeginTransaction()) {
                    try {
                        new NumeracionesRepository(sess).Add(aumenta);

                        tx.Commit();
                    } catch(Exception e) {
                        tx.Rollback();
                        throw e;
                    } finally {
                        sess.Close();
                        sess.Dispose();
                    }
                }

            }
        }
    }
}
