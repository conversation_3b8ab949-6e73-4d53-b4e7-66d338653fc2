﻿using Common.DDL;
using DAO;
using Domain;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Caja
{
    public class CajaServices
    {
        public static List<ItemCombo> getAllDDL()
        {
            IList<Domain.Cajas> listaAux;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                listaAux = (IList<Cajas>)new CajasRepository(sess).GetAll();
                sess.Close();
                sess.Dispose();
            }

            ItemCombo item;
            ListaCombo lista = new ListaCombo();
            foreach (Cajas p in listaAux)
            {

                item = new ItemCombo();
                item.ID = p.Id.ToString();
                item.Descripcion = p.Descripcion;
                //item.Terminal = p.Terminal;
                lista.Add(item);
            }
            return (lista);
        }


        public static Cajas GetById(int id)
        {
            Cajas u;

            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                u = new CajasRepository(sess).GetByKey(id);

                if (u != null)
                {
                    NHibernateUtil.Initialize(u.Id);
                }




                sess.Close();
                sess.Dispose();
                return u;
            }

        }
        public static void SaveOrUpdate(Domain.Cajas c)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {

                        new CajasRepository(sess).Add(c);

                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }

            }
        }
        public static void Delete(Domain.Cajas u)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new CajasRepository(sess).Remove(u);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }

            }
        }
    }
}
