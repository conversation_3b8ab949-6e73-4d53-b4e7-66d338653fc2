﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 22/5/2023 14:55:38
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class ComprobanteTiposRepository : NHibernateRepository<Domain.ComprobanteTipos>, IComprobanteTiposRepository
    {
        public ComprobanteTiposRepository(ISession session) : base(session)
        {
        }

        public virtual ICollection<Domain.ComprobanteTipos> GetAll()
        {
            return session.CreateQuery(string.Format("from ComprobanteTipos")).List<Domain.ComprobanteTipos>();
        }

        public virtual ICollection<Domain.ComprobanteTipos> GetByPorAbreviatura(string Abreviatura)
        {
            string hql = "from ComprobanteTipos WHERE Abreviatura=:Abreviatura";

            IQuery q = session.CreateQuery(hql);
            q.SetParameter("Abreviatura", Abreviatura);
            return q.List<Domain.ComprobanteTipos>();

        }

        public virtual Domain.ComprobanteTipos GetByKey(int _Id)
        {
            return session.Get<Domain.ComprobanteTipos>(_Id);
        }
        public virtual Domain.ComprobanteTipos GetById(int id)
        {
            return session.CreateQuery(string.Format("from ComprobanteTipos where Id=:id")).SetParameter("id", id).UniqueResult<Domain.ComprobanteTipos>();
        
        }
    
    }
    
}
