﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 6/11/2023 16:03:54
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class MetodosdeMedicionRepository : NHibernateRepository<Domain.MetodosdeMedicion>, IMetodosdeMedicionRepository
    {
        public MetodosdeMedicionRepository(ISession session) : base(session)
        {
        }

        public virtual ICollection<Domain.MetodosdeMedicion> GetAll()
        {
            return session.CreateQuery(string.Format("from MetodosdeMedicion ORDER BY Codigo asc")).List<Domain.MetodosdeMedicion>();
        }

        public virtual Domain.MetodosdeMedicion ExisteCodigo(string stringCodigo)
        {
            string hql = "from MetodosdeMedicion where  upper(Codigo)=:stringCodigo  and Activo=true";
            return session.CreateQuery(hql)

          .SetParameter("stringCodigo", stringCodigo.ToUpper())
          .UniqueResult<Domain.MetodosdeMedicion>();
        }
        public virtual bool ExisteNombre(string Descripcion)
        {
            string hql = "select count(*) from MetodosdeMedicion where upper(Descripcion) = :Descripcion and Activo = true";
            long count = session.CreateQuery(hql)
                .SetParameter("Descripcion", Descripcion.ToUpper())
                .UniqueResult<long>();

            return count > 0;
        }
        public virtual Domain.MetodosdeMedicion GetByKey(int _Id)
        {
            return session.Get<Domain.MetodosdeMedicion>(_Id);
        }

    }
}
