//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 10/11/2023 18:16:26
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.ComprobanteTipos, Domain in the schema.
    /// </summary>
    public partial class ComprobanteTipos {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for ComprobanteTipos constructor in the schema.
        /// </summary>
        public ComprobanteTipos()
        {
            this.Comprobantes = new HashSet<Comprobantes>();
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Descripcion in the schema.
        /// </summary>
        public virtual string Descripcion
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Abreviatura in the schema.
        /// </summary>
        public virtual string Abreviatura
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Signo in the schema.
        /// </summary>
        public virtual System.Nullable<short> Signo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for UltimoNumero in the schema.
        /// </summary>
        public virtual decimal UltimoNumero
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for RelacionadoADeudas in the schema.
        /// </summary>
        public virtual short RelacionadoADeudas
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Observaciones in the schema.
        /// </summary>
        public virtual string Observaciones
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for SeMuestra in the schema.
        /// </summary>
        public virtual int SeMuestra
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for IdComprobante in the schema.
        /// </summary>
        public virtual string IdComprobante
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Comprobantes in the schema.
        /// </summary>
        public virtual ISet<Comprobantes> Comprobantes
        {
            get;
            set;
        }
    }

}
