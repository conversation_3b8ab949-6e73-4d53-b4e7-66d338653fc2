﻿using System;
using Business.Services.Epas;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Business.Services.Provincias;
using DevExpress.Web;
using Business.Provincias;
using DevExpress.Utils.OAuth.Provider;
using Web;
using System.Security.Cryptography;
using Domain;

namespace Web.EPAS.AddEdit
{
    public partial class AddEditTiposDeFuentes : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {

                txtcodigo.ClientEnabled = true;
                TxtID.Visible = false;
                btnVolverEditado.Visible = false;

                if (!IsPostBack)
                {
                    if (Request.QueryString["id"] != null)
                    {

                        int idtipofuente = Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["id"].ToString()));
                        TxtID.Text = idtipofuente.ToString();
                        CargartipoFuentes(idtipofuente);
                        btnVolverEditado.Visible = false;
                        txtcodigo.ClientEnabled = false;

                    }
                    else
                    {
                        TxtID.Text = "0";
                        btnVolverEditado.Visible = false;
                        CB_Activo.Checked = true;
                    }
                }
            }

            catch
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1",
                    "showMessage('Error al cargar la página, consulte al administrador del sistema', 'error');", true);
            }

        }

        private void CargartipoFuentes(int idtipofuente)
        {

            try
            {
                Domain.FuentesTipos tipofuente = FuentesTiposService.GetById(idtipofuente);

                if (tipofuente != null)
                {
                    TxtID.Text = tipofuente.ToString();
                    txtcodigo.Text = tipofuente.Codigo.ToString();
                    txtDescripcion.Text = tipofuente.Descripcion.ToString();
                    CB_Activo.Checked = Convert.ToBoolean(tipofuente.Activo);

                }

            }
            catch (Exception ex)
            {
            }

        }

        protected void btnGrabar_Click(object sender,EventArgs e) {

            try {
                Domain.FuentesTipos FT;

                //Verifica que se haya ingresado un codigo y nombre
                if(!string.IsNullOrEmpty(txtcodigo.Text) && !string.IsNullOrEmpty(txtDescripcion.Text)) {

                    //Verifico si el id que recibo está en base de datos (edición)
                    if(Request.QueryString["id"] != null) {

                        //Se está editando un departamento existente.
                        int fuentetipoId = Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["id"]));

                        //Obtengo el objeto departamento                        
                        FT = FuentesTiposService.GetById(fuentetipoId);

                        if(FT != null) {
                            //El código si es edición, no se puede modificar, sólo actualizo descripción y activo
                            if(FT.Codigo.ToUpper().Equals(txtcodigo.Text.Trim().ToUpper())) {
                                FT.Descripcion = txtDescripcion.Text.Trim().ToUpper();
                                FT.Activo = CB_Activo.Checked;

                                FuentesTiposService.SaveOrUpdate(FT);

                                ScriptManager.RegisterStartupScript(this,this.GetType(),"message3","showMessage('Fue editado correctamente.', 'success');",true);
                                txtcodigo.ClientEnabled = false;
                                btnGrabar.ClientEnabled = false;
                                btnVolver.Visible = false;
                                btnVolverEditado.Visible = true;


                            }
                        }
                        //  ScriptManager.RegisterStartupScript(this, this.GetType(), "message1", "showMessage('El Tipo de Fuente ya existe en la base de datos.', 'error');", true);                                             
                    } else {
                        // Se está creando un nuevo departamento.
                        // Verificar si ya existe en la lista local, solo se verifica el código
                        if(FuentesTiposService.ExisteCodigo(txtcodigo.Text.Trim()) || FuentesTiposService.ExisteNombre(txtDescripcion.Text.Trim())) {
                            ScriptManager.RegisterStartupScript(this,this.GetType(),"message2","showMessage('Ya existe un Tipo de Fuente con ese código o nombre, no se puede guardar.', 'error');",true);
                        } else {
                            //No existe el código, puedo guardar en la base
                            FT = new FuentesTipos();
                            FT.Codigo = txtcodigo.Text.Trim().ToUpper();
                            FT.Descripcion = txtDescripcion.Text.Trim().ToUpper();

                            FT.Activo = CB_Activo.Checked;
                            FuentesTiposService.SaveOrUpdate(FT);
                            ScriptManager.RegisterStartupScript(this,this.GetType(),"message3","showMessage('Agregado correctamente.', 'success');",true);
                            txtcodigo.ClientEnabled = false;
                            btnGrabar.ClientEnabled = false;
                            btnVolver.Visible = false;
                            btnVolverEditado.Visible = true;

                        }

                    }



                } else {
                    ScriptManager.RegisterStartupScript(this,this.GetType(),"message4","showMessage('Busque un Tipo de Fuente.', 'error');",true);
                }
            } catch(Exception ex) {
                ScriptManager.RegisterStartupScript(this,this.GetType(),"message5","showMessage('Error al agregar Tipo de Fuente, intente de nuevo.', 'error');",true);
            }

        }
    }
}       