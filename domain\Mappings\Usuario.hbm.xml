<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="Usuario" table="Usuario">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Username" type="String">
      <column name="Username" not-null="true" length="50" sql-type="nvarchar" />
    </property>
    <property name="Password" type="String">
      <column name="Password" not-null="true" length="50" sql-type="nvarchar" />
    </property>
    <property name="Nombre" type="String">
      <column name="Nombre" not-null="false" length="50" sql-type="nvarchar" />
    </property>
    <property name="Apellido" type="String">
      <column name="Apellido" not-null="false" length="50" sql-type="nvarchar" />
    </property>
    <property name="SuperAdmin" type="Boolean">
      <column name="SuperAdmin" default="false" not-null="false" sql-type="bit" />
    </property>
    <property name="Cuit" type="String">
      <column name="Cuit" not-null="false" length="250" sql-type="nvarchar" />
    </property>
    <property name="Direccion" type="String">
      <column name="Direccion" not-null="false" sql-type="nvarchar" />
    </property>
    <property name="Telefono" type="String">
      <column name="Telefono" not-null="false" length="50" sql-type="nvarchar" />
    </property>
    <property name="Email" type="String">
      <column name="Email" not-null="false" length="50" sql-type="nvarchar" />
    </property>
    <property name="Confirmacion" type="String">
      <column name="Confirmacion" not-null="false" sql-type="nvarchar" />
    </property>
    <property name="Eliminado" type="Boolean">
      <column name="Eliminado" default="false" not-null="true" sql-type="bit" />
    </property>
    <property name="Activo" type="Boolean">
      <column name="Activo" default="1" not-null="false" sql-type="bit" />
    </property>
    <property name="PrimerIngreso" type="Boolean">
      <column name="PrimerIngreso" default="1" not-null="false" sql-type="bit" />
    </property>
    <property name="UltimoIngreso" type="DateTime">
      <column name="UltimoIngreso" not-null="false" sql-type="datetime" />
    </property>
    <property name="IngresoActual" type="DateTime">
      <column name="IngresoActual" not-null="false" sql-type="datetime" />
    </property>
    <set name="Rols" table="RolUsuario" generic="true">
      <key>
        <column name="IdUsuario" not-null="false" precision="10" scale="0" sql-type="int" />
      </key>
      <many-to-many class="Rol" fetch="join">
        <column name="IdRol" not-null="false" precision="10" scale="0" sql-type="int" />
      </many-to-many>
    </set>
    <set name="Contratos" inverse="true" generic="true">
      <key>
        <column name="IdUsuario" />
      </key>
      <one-to-many class="Contratos" />
    </set>
    <set name="Protocolos_IdUsuario" inverse="true" generic="true">
      <key>
        <column name="IdUsuario" />
      </key>
      <one-to-many class="Protocolos" />
    </set>
    <set name="Protocolos_IdUsuarioAprobo" inverse="true" generic="true">
      <key>
        <column name="IdUsuarioAprobo" />
      </key>
      <one-to-many class="Protocolos" />
    </set>
  </class>
</hibernate-mapping>