﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 14/8/2023 15:38:23
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class TransferenciasRepository : NHibernateRepository<Domain.Transferencias>, ITransferenciasRepository
    {
        public TransferenciasRepository(ISession session) : base(session)
        {
        }

        public virtual ICollection<Domain.Transferencias> GetAll()
        {
            return session.CreateQuery(string.Format("from Transferencias")).List<Domain.Transferencias>();
        }

        public virtual Domain.Transferencias GetByKey(int _Id)
        {
            return session.Get<Domain.Transferencias>(_Id);
        }
    }
}
