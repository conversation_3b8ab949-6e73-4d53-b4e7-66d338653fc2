<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="Contratos" table="Contratos">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Nombre" type="String">
      <column name="Nombre" not-null="false" length="50" sql-type="nvarchar" />
    </property>
    <property name="RazonSocial" type="String">
      <column name="RazonSocial" not-null="false" length="250" sql-type="nvarchar" />
    </property>
    <property name="NroContrato" type="Int32">
      <column name="NroContrato" not-null="false" precision="10" scale="0" sql-type="int" />
    </property>
    <property name="IdentificacionTributaria" type="String">
      <column name="IdentificacionTributaria" not-null="false" length="60" sql-type="nvarchar" />
    </property>
    <property name="EsTomador" type="Boolean">
      <column name="EsTomador" not-null="false" sql-type="bit" />
    </property>
    <property name="FechaAlta" type="DateTime">
      <column name="FechaAlta" not-null="false" sql-type="datetime2" />
    </property>
    <property name="Baja" type="Boolean">
      <column name="Baja" not-null="false" sql-type="bit" />
    </property>
    <property name="FechaBaja" type="DateTime">
      <column name="FechaBaja" not-null="false" sql-type="datetime2" />
    </property>
    <property name="Observaciones" type="String">
      <column name="Observaciones" not-null="false" sql-type="nvarchar" />
    </property>
    <property name="FechaGeneracion" type="DateTime">
      <column name="FechaGeneracion" not-null="false" sql-type="datetime2" />
    </property>
    <property name="UltimaFechaFactura" type="DateTime">
      <column name="UltimaFechaFactura" not-null="false" sql-type="datetime2" />
    </property>
    <property name="Email" type="String">
      <column name="Email" not-null="false" length="200" sql-type="nvarchar" />
    </property>
    <property name="EmailAlter" type="String">
      <column name="EmailAlter" not-null="false" length="200" sql-type="nvarchar" />
    </property>
    <property name="Telefono" type="String">
      <column name="Telefono" not-null="false" length="15" sql-type="nvarchar" />
    </property>
    <property name="TelefonoAlter" type="String">
      <column name="TelefonoAlter" not-null="false" length="15" sql-type="nvarchar" />
    </property>
    <many-to-one name="Usuario" class="Usuario">
      <column name="IdUsuario" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <many-to-one name="IVACondiciones" class="IVACondiciones">
      <column name="IdIVACondicion" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <many-to-one name="Monedas" class="Monedas">
      <column name="IdMoneda" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <many-to-one name="DocTipos" class="DocTipos">
      <column name="IdDocTipo" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <many-to-one name="PersonasTipo" class="PersonasTipo">
      <column name="IdPersonaTipos" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <many-to-one name="TelefonoTipo_IdTelefonoTipo" class="TelefonoTipo">
      <column name="IdTelefonoTipo" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <many-to-one name="TelefonoTipo_IdTelefonoTipoAlter" class="TelefonoTipo">
      <column name="IdTelefonoTipoAlter" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <many-to-one name="ContratoTipo" class="ContratoTipo">
      <column name="IdContratoTipo" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <set name="Comprobantes" table="ComprobantesContratos" generic="true">
      <key>
        <column name="IdContrato" not-null="true" precision="10" scale="0" sql-type="int" />
      </key>
      <many-to-many class="Comprobantes" fetch="join">
        <column name="IdComprobante" not-null="true" precision="10" scale="0" sql-type="int" />
      </many-to-many>
    </set>
    <set name="ContratoContactos" inverse="true" generic="true">
      <key>
        <column name="IdContrato" />
      </key>
      <one-to-many class="ContratoContactos" />
    </set>
    <set name="ContratoDomicilios" inverse="true" generic="true">
      <key>
        <column name="IdContrato" />
      </key>
      <one-to-many class="ContratoDomicilios" />
    </set>
    <set name="Protocolos_IdContratoSolicitante" inverse="true" generic="true">
      <key>
        <column name="IdContratoSolicitante" />
      </key>
      <one-to-many class="Protocolos" />
    </set>
    <set name="Protocolos_IdContratoTomador" inverse="true" generic="true">
      <key>
        <column name="IdContratoTomador" />
      </key>
      <one-to-many class="Protocolos" />
    </set>
  </class>
</hibernate-mapping>