﻿using DAO;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Contratos
{
    public class PersonasServices
    {
        public static Domain.Contratos GetById(int id)
        {
            Domain.Contratos p;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                p = (Domain.Contratos)new ContratosRepository(sess).GetByKey(id);
                if (p != null)
                {
                    NHibernateUtil.Initialize(p.NroContrato);
                }
                sess.Close();
                sess.Dispose();
                return p;
            }
        }
    }
}
