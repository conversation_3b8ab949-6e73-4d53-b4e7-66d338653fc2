﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="AddEditPlantilla.aspx.cs" Inherits="Web.EPAS.AddEdit.AddEditPlantilla" %>

<%@ Register Assembly="DevExpress.Web.v18.1" Namespace="DevExpress.Web" TagPrefix="dx" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">

    <script type="text/javascript">

        function OnToolbarItemClick(s, e) {
            if (IsCustomExportToolbarCommand(e.item.name)) {
                e.processOnServer = true;
                e.usePostBack = true;
            } else {

            }
        }

        function IsCustomExportToolbarCommand(command) {
            return command == "CustomExportToXLS" || command == "CustomExportToXLSX";
        }

        function volverButton() {

            if (confirm) {
                Swal.fire({
                    text: "No se guardaron los cambios. Desea volver de todos modos?",
                    showCancelButton: true,
                    confirmButtonText: "Volver"
                }).then((result) => {
                    if (result.isConfirmed) {

                        window.location.href = "/SIGeLab/EPAS/Lista/Plantilla.aspx";

                    }
                });
            }
        }

        function volverButtonEditado() {

            if (confirm) {

                window.location.href = "/SIGeLab/EPAS/Lista/Plantilla.aspx";

            }
        }

    </script>

</asp:Content>


<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

    <div class="page-header page-header-light shadow">
        <div class="page-header-content d-lg-flex border-top">
            <div class="d-flex">
                <div class="breadcrumb py-2">
                    <a href="<%= Web.Util.Helpers.GetApplicationPath() %>/Inicio.aspx" class="breadcrumb-item"><i class="icon-home2 mr-2"></i>Inicio</a>
                    <a href="<%= Web.Util.Helpers.GetApplicationPath() %>/EPAS/Lista/Plantilla.aspx" class="breadcrumb-item"><i class="icon-display me-2"></i>Mantenimiento</a>
                    <a href="<%= Web.Util.Helpers.GetApplicationPath() %>/EPAS/Lista/Plantilla.aspx" class="breadcrumb-item">Plantilla</a>
                    <span class="breadcrumb-item active">
                        <asp:Literal ID="ltr_nombreBreadcrumb" runat="server">Agregar/Editar Plantilla</asp:Literal>
                    </span>
                </div>

                <a href="#" class="header-elements-toggle text-default d-md-none"><i class="icon-more"></i></a>
            </div>
        </div>
    </div>

    <div class="row gx-1">

        <div class="col-lg-12">

            <div class="navbar navbar-expand-xl bg-secondary navbar-dark rounded-top mt-1">
                <div class="container-fluid">                           
                    <div class="nav-item dropdown">
                        <span class="breadcrumb-item active mt-2 mb-1 fs-5 fw-medium">
                            <asp:Literal ID="ltr_PlantillaProtocolo" runat="server">Nuevo o Editar</asp:Literal>
                        </span>
                    </div>                        
                </div>
            </div>

            <!-- Card Cliente -->

            <div class="tab-content border border-1 rounded-bottom p-1">
                <div class="card">
                    <div class="row gx-0">
                    
                        <div class="card-body col-lg-7 my-3">
                            <div action="#">

                                <div class="row mb-1">
                                    <label class="col-lg-2 col-form-label pe-0 fw-semibold">N°</label>
                                    <div class="col-lg-2 ps-0">
                                        <dx:ASPxTextBox ID="txtNumero" ReadOnly="true" NullText="-" ClientInstanceName="txtNumero" CssClass="form-control altura-textbox" AutoCompleteType="Disabled" runat="server">
                                        </dx:ASPxTextBox>
                                    </div>
                                </div>

                                <div class="row mb-1">
                                    <label class="col-lg-2 col-form-label pe-0 fw-semibold">Protocolo</label>
                                    <div class="col-lg-5 ps-0">
                                        <dx:ASPxTextBox ID="txtDescripcion" ClientInstanceName="txtDescripcion" CssClass="form-control altura-textbox" AutoCompleteType="Disabled" runat="server">
                                        </dx:ASPxTextBox>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-5"></div>
                    </div>

                    <fieldset><legend class="col-lg-7 border-secondary border-bottom border-width-1"></legend></fieldset>

                    <div class="row gx-0">
                        <div class="col-lg-7 card-body mt-3">
                            <div action="#">

                                <div class="row mb-1">
                                    <label class="col-lg-2 col-form-label fw-semibold">Seleccionar</label>
                                    <div class="col-lg-8 ps-0">
                                        <dx:ASPxComboBox ID="CmbDeterminaciones" ClientInstanceName="CmbDeterminaciones" runat="server" DropDownStyle="DropDownList" TextFormatString="{1} ({0})" Width="100%" CssClass="form-control altura-textbox" IncrementalFilteringMode="Contains" TextField="NombreDeterminacion" ValueField="NombreDeterminacion" EnableSynchronization="False">
                                            <Columns>
                                                <dx:ListBoxColumn FieldName="NroDeterminacion" Caption="Nro." Width="12%" />
                                                <dx:ListBoxColumn FieldName="Descripcion" Caption="Descripción" />
                                                  <dx:ListBoxColumn FieldName="UnidadPrimaria" Caption="Un." />
                                                <dx:ListBoxColumn FieldName="ValorReferencia" Caption="Valor de Ref." />
                                            </Columns>
                                        </dx:ASPxComboBox>
                                    </div>

                                    <div class="col-lg-2 form-control-feedback form-control-feedback-start flex-grow-1">
                                        <dx:ASPxButton ID="btnAgregarDeterminacion" RenderMode="Link" OnClick="btnAgregarDeterminacion_Click" runat="server" CssClass="fw-medium ms-3 rounded-2 text-white bg-info border border-secondary text-decoration-none p-2" ClientInstanceName="btnAgregarDeterminacion" Text="Agregar">
                                        </dx:ASPxButton>                                    
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-5"></div>
                    </div>

                    <div class="row gx-0">
                        <div class="col-lg-7 card-body">
                            <div class="row mt-2 align-content-center d-flex justify-content-center">

                                <dx:ASPxGridView ID="gvDeterminaciones" CssClass="fw-medium border border-black rounded" ViewStateMode="Disabled" DataSourceForceStandardPaging="false" OnDataBinding="gvDeterminaciones_DataBinding" EnableRowsCache="false" EnableViewState="false" AutoGenerateColumns="False" KeyFieldName="Id" Width="100%" ClientInstanceName="gvDeterminaciones" runat="server">
                                    <Toolbars>
                                        <dx:GridViewToolbar ItemAlign="Right" EnableAdaptivity="true">
                                            <Items>
                                                <dx:GridViewToolbarItem Text="Exportar a" Image-IconID="actions_refresh_16x16office2013" DisplayMode="ImageWithText" BeginGroup="true">
                                                    <Items>
                                                        <dx:GridViewToolbarItem Command="ExportToPdf" Text="Exportar a PDF" />
                                                        <dx:GridViewToolbarItem Command="ExportToXlsx" Text="Exportar a Excel" />
                                                    </Items>
                                                </dx:GridViewToolbarItem>
                                                <dx:GridViewToolbarItem BeginGroup="true">
                                                    <Template>
                                                        <dx:ASPxButtonEdit ID="tbToolbarSearch" runat="server" NullText="Buscar..." Height="100%" CssClass="form-control">
                                                            <Buttons>
                                                                <dx:SpinButtonExtended Image-IconID="find_find_16x16gray" />
                                                            </Buttons>
                                                        </dx:ASPxButtonEdit>
                                                    </Template>
                                                </dx:GridViewToolbarItem>
                                            </Items>
                                        </dx:GridViewToolbar>
                                    </Toolbars>
                                    <SettingsSearchPanel CustomEditorID="tbToolbarSearch" />

                                    <SettingsExport EnableClientSideExportAPI="true" ExcelExportMode="DataAware" />
                                    <ClientSideEvents ToolbarItemClick="OnToolbarItemClick" />
                                    <SettingsBehavior AllowFocusedRow="true" FilterRowMode="Auto" AllowDragDrop="false" AllowEllipsisInText="true" />
                                    <SettingsPager PageSize="20" AlwaysShowPager="true"></SettingsPager>
                                    <Columns>

                                        <dx:GridViewDataColumn FieldName="NroDeterminacion" Caption="N°" HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" CellStyle-HorizontalAlign="Center" Width="10%" >
                                            <DataItemTemplate>
                                                <dx:ASPxLabel ID="lblNroDeterminacion" CssClass="fw-semibold" runat="server" Font-Size="Small" Theme="Moderno" Text='<%# String.Format( "{0}", Eval("NroDeterminacion")) %>'>
                                                </dx:ASPxLabel>
                                            </DataItemTemplate>
                                        </dx:GridViewDataColumn>

                                        <dx:GridViewDataColumn FieldName="Descripcion" Caption="Descripción" HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center">
                                            <DataItemTemplate>
                                                <dx:ASPxLabel ID="lblDescripcion" CssClass="fw-semibold" runat="server" Font-Size="Small" Theme="Moderno" Text='<%# String.Format( "{0}", Eval("Descripcion")) %>'>
                                                </dx:ASPxLabel>
                                            </DataItemTemplate>
                                        </dx:GridViewDataColumn>

                                        <dx:GridViewDataColumn FieldName="ValorReferencia" Caption="Valor de Ref." HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center">
                                            <DataItemTemplate>
                                                <dx:ASPxLabel ID="lblValorReferencia" CssClass="fw-semibold" runat="server" Font-Size="Small" Theme="Moderno" Text='<%# String.Format( "{0}", Eval("ValorReferencia")) %>'>
                                                </dx:ASPxLabel>
                                            </DataItemTemplate>
                                        </dx:GridViewDataColumn>

                                          <dx:GridViewDataColumn FieldName="UnidadPrimaria" Caption="Un." HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center">
      <DataItemTemplate>
          <dx:ASPxLabel ID="lblUnidad" CssClass="fw-semibold" runat="server" Font-Size="Small" Theme="Moderno" Text='<%# String.Format( "{0}", Eval("UnidadPrimaria")) %>'>
          </dx:ASPxLabel>
      </DataItemTemplate>
  </dx:GridViewDataColumn>

                                        <dx:GridViewDataColumn Caption="" HeaderStyle-HorizontalAlign="Center" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center">
                                            <DataItemTemplate>
                                                <dx:ASPxButton ID="btnEliminar" runat="server" CssClass="fw-semibold text-secondary text-decoration-none" ClientInstanceName="btnEliminar" RenderMode="Link" OnClick="btnEliminar_Click" Text="Quitar" CausesValidation="false" AutoPostBack="false"
                                             CommandArgument='<%# Eval("Id")%>'>
                                                    <ClientSideEvents Click="function(s,e){e.processOnServer =confirm('¿Esta seguro que desea eliminar?');}" />
                                                </dx:ASPxButton>
                                            </DataItemTemplate>
                                        </dx:GridViewDataColumn>
            
                                    </Columns>

                                    <SettingsDataSecurity AllowInsert="false" />
                                    <EditFormLayoutProperties>
                                        <SettingsAdaptivity AdaptivityMode="SingleColumnWindowLimit" SwitchToSingleColumnAtWindowInnerWidth="700" />
                                    </EditFormLayoutProperties>
                                    <SettingsPopup>
                                        <EditForm Width="600">
                                            <SettingsAdaptivity Mode="OnWindowInnerWidth" SwitchAtWindowInnerWidth="768" />
                                        </EditForm>
                                    </SettingsPopup>
                                </dx:ASPxGridView>

                            </div>
                        </div>
                        <div class="col-lg-5"></div>
                    </div>
                            
                            
                        <%----BOTONES----%>
                    <div class="row gx-0">
                        <div class="col-lg-8 card-body">
                            <div action="#">

                                <div class="row d-flex justify-content-end">
                                    <div class="col-lg-6 mt-3">
                                        <dx:ASPxButton ID="btnGrabar" runat="server" CssClass="btn btn-secondary fw-semibold ms-3 px-0 py-1" OnClick="btnGrabar_Click" ClientInstanceName="btnGrabar" AutoPostBack="False" CausesValidation="false" Text="Grabar">
                                            <ClientSideEvents Click="function(s,e) { MostrarLoading(); }" />
                                        </dx:ASPxButton>

                                        <dx:ASPxButton ID="btnVolver" runat="server" CssClass="btn text-white fw-semibold px-0 ms-3 py-1" BackColor="Brown" ClientInstanceName="btnVolver" AutoPostBack="False" CausesValidation="false" Text="Volver" >
                                            <ClientSideEvents Click="function(s,e) { volverButton(); }" />
                                        </dx:ASPxButton>

                                        <dx:ASPxButton ID="btnVolverEditado" runat="server" CssClass="btn text-white fw-semibold px-0 py-1 ms-3" BackColor="Brown" ClientInstanceName="btnVolverEditado" AutoPostBack="False" CausesValidation="false" Text="Volver" >
                                            <ClientSideEvents Click="function(s,e){ volverButtonEditado();}" />
                                        </dx:ASPxButton>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4"></div>
                    </div>

                </div>
            </div>
        </div>
    </div>

</asp:Content>


<asp:Content ID="Content3" ContentPlaceHolderID="footer" runat="server">
</asp:Content>
