﻿using DAO;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Epas
{
    public class MetodosdeMedicionService
    {
        //Trae todo
        public static IList<Domain.MetodosdeMedicion> GetAll()
        {
            IList<Domain.MetodosdeMedicion> listaAux;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                listaAux = (IList<Domain.MetodosdeMedicion>)new MetodosdeMedicionRepository(sess).GetAll();
                sess.Close();
                sess.Dispose();
            }
            return (listaAux);



        }




        //obtener por id
        public static Domain.MetodosdeMedicion GetById(int id)
        {

            Domain.MetodosdeMedicion p;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {

                p = (Domain.MetodosdeMedicion)new MetodosdeMedicionRepository(sess).GetByKey(id);
                sess.Close();
                sess.Dispose();
                return p;
            }

        }

        public static bool ExisteCodigo(string codigo)
        {

            Domain.MetodosdeMedicion p;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {

                p = (Domain.MetodosdeMedicion)new MetodosdeMedicionRepository(sess).ExisteCodigo(codigo);
                sess.Close();
                sess.Dispose();
                return p != null;

            }

        }


        public static bool ExisteNombre(string Descripcion)
        {
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                return new MetodosdeMedicionRepository(sess).ExisteNombre(Descripcion);
            }
        }

        //agregar y actualizar
        public static void SaveOrUpdate(Domain.MetodosdeMedicion p)
        {

            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {

                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {

                    try
                    {

                        new MetodosdeMedicionRepository(sess).Add(p);

                        tx.Commit();
                    }
                    catch (Exception e)
                    {

                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {

                        sess.Close();
                        sess.Dispose();
                    }

                }

            }

        }

        //borrar
        public static void Delete(Domain.MetodosdeMedicion p)
        {
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new MetodosdeMedicionRepository(sess).Remove(p);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }

                }

            }

        }
    }
}
