﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="PRUEBA.aspx.cs" Inherits="Web.PRUEBA" %>

<%@ Register Assembly="DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Namespace="DevExpress.Web" TagPrefix="dx" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title></title>
</head>
<body>
    <form id="form1" runat="server">
        <div>

            <asp:ScriptManager runat="server" ></asp:ScriptManager>
            <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
              
                <ContentTemplate>
                    
                    <asp:TextBox ID="TextBox1" runat="server"></asp:TextBox>
                    
                    <asp:Button ID="uno" runat="server" Text="Button" OnClick="Unnamed_Click"/>
                    <asp:Button ID="dos" runat="server" Text="Button" OnClick="Unnamed_Click1"/>

                </ContentTemplate>
                <Triggers>

                    <asp:AsyncPostBackTrigger ControlID ="uno" EventName="Click"/>
                    <asp:AsyncPostBackTrigger ControlID ="dos" EventName="Click"/>


                </Triggers>
            </asp:UpdatePanel>
        </div>
    </form>
</body>
</html>
