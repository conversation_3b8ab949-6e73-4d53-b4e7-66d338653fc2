//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 6/11/2023 09:33:14
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.DocTipos, Domain in the schema.
    /// </summary>
    [Serializable]

    public partial class DocTipos {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for DocTipos constructor in the schema.
        /// </summary>
        public DocTipos()
        {
            this.ContratoContactos = new HashSet<ContratoContactos>();
            this.Contratos = new HashSet<Contratos>();
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for TipoDoc in the schema.
        /// </summary>
        public virtual string TipoDoc
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Abreviatura in the schema.
        /// </summary>
        public virtual string Abreviatura
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Mascara in the schema.
        /// </summary>
        public virtual string Mascara
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for MascaraDeFormato in the schema.
        /// </summary>
        public virtual string MascaraDeFormato
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for AbreviaturaAImprimir in the schema.
        /// </summary>
        public virtual string AbreviaturaAImprimir
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Activo in the schema.
        /// </summary>
        public virtual System.Nullable<bool> Activo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ContratoContactos in the schema.
        /// </summary>
        public virtual ISet<ContratoContactos> ContratoContactos
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Contratos in the schema.
        /// </summary>
        public virtual ISet<Contratos> Contratos
        {
            get;
            set;
        }
    }

}
