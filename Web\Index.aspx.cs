﻿using Business.Services.Usuarios;
using Domain;
using System;
using System.Collections.Generic;
using System.EnterpriseServices;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web
{
    public partial class Index : System.Web.UI.Page
    {
        private object pnl_stats;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                if (Session["usuario"] != null)
                {
                    int idUsuario = (int)Session["usuario"];
                    Usuario u = UsuarioService.getByIdNoInitialize(idUsuario);
                    if (u != null)
                    {
                        lbl_usuario.Text = u.Nombre + " " + u.Apellido + " (" + u.Username + ")";

                        //pnl stats
                        if (u.Rols.ToList<Rol>().Count(q => q.Codigo.ToUpper() == "SA") > 0)
                        {
                            pnl_stats = true;
                           // Page.RegisterAsyncTask(new PageAsyncTask(CargarEstadisticas));
                        }
                        else
                        {
                            pnl_stats = false;
                        }

                        //Verifico si tiene permisos para cargar votos
                    }
                }
            }
        }



    }
};
