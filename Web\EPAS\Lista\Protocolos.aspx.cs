﻿using Business.Provincias;
using Business.Services.Epas;
using Business.Services.Provincias;
using DevExpress.Web;
using Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web.EPAS.Lista
{
    public partial class Protocolos : System.Web.UI.Page
    {
        protected void Page_Init(Object sender,EventArgs e)
        {
            if(!IsPostBack)
            {
                Session["ListaProtocolos"] = new List<Domain.Protocolos>();
            }
        }

        protected void Page_Load(object sender,EventArgs e)
        {

            if(Request.QueryString["idSolicitante"] != null)
            {

                int idSolicitante = Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["idSolicitante"].ToString()));
                Session["ListaProtocolosSolicitante"] = idSolicitante;
                CargarProtocolos(idSolicitante);

            } else
            {
                CargarDatosEnGvProtocolo();
            }
        }

        protected void CargarProtocolos(int idSolicitante)
        {
            List<Domain.Protocolos> protocolosSolicitante = (List<Domain.Protocolos>) Business.Services.Epas.ProtocolosService.GetByIdSolicitante(idSolicitante);

            gv_Protocolo.DataSource = protocolosSolicitante;
            gv_Protocolo.DataBind();
        }

        protected void CargarDatosEnGvProtocolo()
        {
            List<Domain.Protocolos> protocolos = (List<Domain.Protocolos>) Business.Services.Epas.ProtocolosService.GetAll();

            gv_Protocolo.DataSource = protocolos;
            gv_Protocolo.DataBind();

        }

        protected void btnEditar_Click(object sender,EventArgs e)
        {
            try
            {
                ASPxButton btn = (ASPxButton) sender;
                if(btn != null)
                {
                    GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer) btn.NamingContainer;
                    if(c != null)
                    {
                        var ind = DataBinder.Eval(c.DataItem,"Id");

                        if(ind != null)
                        {
                            Domain.Protocolos protocolo = ProtocolosService.GetById(Convert.ToInt32(ind));

                            switch (protocolo.ProtocolosEstados.Codigo)
                            {
                                case "B":
                                    string messageBaja = string.Format("El Protocolo {0} está dado de baja y no se puede editar.",protocolo.TipoAnalisis.Descripcion);
                                    ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('" + messageBaja + "')",true);
                                break;

                                case "ER":
                                    string messageRevision = string.Format("El Protocolo {0} está en revisión y no se puede editar.",protocolo.TipoAnalisis.Descripcion);
                                    ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('" + messageRevision + "')",true);
                                break;

                                case "A":
                                    string messageAprobado = string.Format("El Protocolo {0} está aprobado y no se puede editar.",protocolo.TipoAnalisis.Descripcion);
                                    ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('" + messageAprobado + "')",true);
                                break;

                                default:
                                Response.Redirect(Global.ApplicationPath + "/EPAS/AddEdit/AddEditProtocolos.aspx?id=" + Util.Helpers.Encrypt(ind.ToString()),false);
                                break;

                            }

                            if(Session["ListaProtocolosSolicitante"] != null)
                            {
                                int idSolicitante = Convert.ToInt32(Session["ListaProtocolosSolicitante"]);
                                CargarProtocolos(idSolicitante);
                            } else
                            {
                                CargarDatosEnGvProtocolo();
                            }
                        }
                    }
                }
            } catch(Exception ex)
            {
                ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('Error al tratar de editar un elemento.Consulte al administrador del sistema.', 'error');",true);
            }
        }

        protected void btnAnularProtocolo_Click(object sender,EventArgs e)
        {
            try
            {
                ASPxButton btn = (ASPxButton) sender;
                GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer) btn.NamingContainer;
                var ind = DataBinder.Eval(c.DataItem,"Id");

                Domain.Protocolos protocolo = ProtocolosService.GetById(Convert.ToInt32(ind));

                ProtocolosEstados pe = ProtocoloEstadoService.GetByCodigo("B");

                switch(protocolo.ProtocolosEstados.Codigo)
                {
                    case "ER":
                        string messageRevision = string.Format("El Protocolo {0} está en Revisión y no se puede anular!",protocolo.TipoAnalisis.Descripcion);
                        ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('" + messageRevision + "')",true);
                    break;

                    case "A":
                        string messageAprobado = string.Format("El Protocolo {0} está en Aprobado y no se puede anular!",protocolo.TipoAnalisis.Descripcion);
                        ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('" + messageAprobado + "')",true);
                    break;

                    case "B":
                        string messageBaja = string.Format("El Protocolo {0} ya está dado de Baja!",protocolo.TipoAnalisis.Descripcion);
                        ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('" + messageBaja + "')",true);
                    break;

                    case "P":
                        string messagePendiente = string.Format("El Protocolo {0} fue anulado con éxito!",protocolo.TipoAnalisis.Descripcion);
                        ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('" + messagePendiente + "')",true);

                        protocolo.ProtocolosEstados = pe;
                        ProtocolosService.SaveOrUpdate(protocolo);
                    break;

                    default:
                    break;
                }

                if(Session["ListaProtocolosSolicitante"] != null)
                {
                    int idSolicitante = Convert.ToInt32(Session["ListaProtocolosSolicitante"]);
                    CargarProtocolos(idSolicitante);

                } else
                {
                    CargarDatosEnGvProtocolo();
                }


            } catch(Exception ex)
            {
                ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('Error al tratar de eliminar un elemento, consulte al administrador del sistema', 'error');",true);

            }
        }
    }
}