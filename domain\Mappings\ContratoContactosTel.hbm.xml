<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="ContratoContactosTel" table="ContratoContactos_Tel">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="NroTelefono" type="String">
      <column name="NroTelefono" not-null="false" length="15" sql-type="nvarchar" />
    </property>
    <many-to-one name="ContratoContactos" class="ContratoContactos">
      <column name="IdContratoContacto" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <many-to-one name="TelefonoTipo" class="TelefonoTipo">
      <column name="IdTelefonoTipo" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
  </class>
</hibernate-mapping>