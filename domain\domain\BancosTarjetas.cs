//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 25/9/2023 14:39:43
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.BancosTarjetas, Domain in the schema.
    /// </summary>
    public partial class BancosTarjetas {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for BancosTarjetas constructor in the schema.
        /// </summary>
        public BancosTarjetas()
        {
            this.BancoCuentas = new HashSet<BancoCuentas>();
            this.Cheques = new HashSet<Cheques>();
            this.Comprobantes = new HashSet<Comprobantes>();
            this.CuponesTarjetas = new HashSet<CuponesTarjetas>();
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Nombre in the schema.
        /// </summary>
        public virtual string Nombre
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Cuit in the schema.
        /// </summary>
        public virtual string Cuit
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for QueEs in the schema.
        /// </summary>
        public virtual string QueEs
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Mascara in the schema.
        /// </summary>
        public virtual string Mascara
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for MascaraDeFormato in the schema.
        /// </summary>
        public virtual string MascaraDeFormato
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Abreviatura in the schema.
        /// </summary>
        public virtual string Abreviatura
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for CodExportacion in the schema.
        /// </summary>
        public virtual System.Nullable<int> CodExportacion
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Activo in the schema.
        /// </summary>
        public virtual System.Nullable<bool> Activo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ModalidadTarjeta in the schema.
        /// </summary>
        public virtual string ModalidadTarjeta
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for EsDebitoA in the schema.
        /// </summary>
        public virtual System.Nullable<bool> EsDebitoA
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for IdComprobante in the schema.
        /// </summary>
        public virtual System.Nullable<int> IdComprobante
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for BancoCuentas in the schema.
        /// </summary>
        public virtual ISet<BancoCuentas> BancoCuentas
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Cheques in the schema.
        /// </summary>
        public virtual ISet<Cheques> Cheques
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Comprobantes in the schema.
        /// </summary>
        public virtual ISet<Comprobantes> Comprobantes
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for CuponesTarjetas in the schema.
        /// </summary>
        public virtual ISet<CuponesTarjetas> CuponesTarjetas
        {
            get;
            set;
        }
    }

}
