﻿using Business.Services.Usuarios;
using Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web
{
    public partial class Login : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!Request.IsLocal && !Request.IsSecureConnection)
            {
                string redirectUrl = Request.Url.ToString().Replace("http:", "https:");
                Response.Redirect(redirectUrl, false);
            }
        }

        //open a web with selenium an Chrome webdriver  
        //using OpenQA.Selenium;
        //using OpenQA.Selenium.Chrome;

        //using OpenQA.Selenium.Support.UI;
        //using System;


        protected void btnEntrar_Click(object sender, EventArgs e)
        {
            try
            {

                Usuario u = UsuarioService.ExisteUsername(txtUsername.Text.Trim());

                if (u == null)
                {
                    lblError.Visible = true;
                    lblError.Text = "No existe el usuario ingresado";
                


                }
                else
                {

                    if (u.Password.Equals(txtPassword.Text.Trim()))
                    {
                        if (u.Activo==true & u.Eliminado == false)
                        {
                            if (Session["usuario"] != null)
                            {
                                Session.Remove("usuario");
                                Session.Add("usuario", u.Id);
                            }
                            else
                            {
                                Session.Add("usuario", u.Id);
                            }

                            if (u.PrimerIngreso == true)
                            {
                                u.PrimerIngreso = false;
                                u.IngresoActual = DateTime.Now;
                            }

                            u.UltimoIngreso = u.IngresoActual;

                            u.IngresoActual = DateTime.Now;

                            UsuarioService.SaveOrUpdate(u);

                            //GUARDO UNA COOKIE
                            HttpCookie myCookie = new HttpCookie("usuario_" + u.Id.ToString());
                            myCookie.Value = u.Id.ToString();
                            myCookie.Expires = DateTime.Now.AddDays(1d);
                            Response.Cookies.Add(myCookie);

                            Response.Redirect(Global.ApplicationPath + "/Index.aspx", true);
                            //Response.Redirect(Global.ApplicationPath + "/ImportarPatrones.aspx", true);
                        }
                        else
                        {
                            lblError.Visible = true;
                            lblError.Text = "Usuario desactivado ó eliminado";
                        }
                    }
                    else
                    {
                        lblError.Visible = true;
                        lblError.Text = "El usuario ó el password son incorrectos";
                     

                    }

                }
            }
            catch (Exception ex)
            {
                lblError.Visible = true;
                lblError.Text = "Error al iniciar, contacte al administrador del sistema . " + ex.Message;
            

            }
           
        }

        protected void chbMostrarPassword_CheckedChanged(object sender,EventArgs e) {

            txtPassword.ClientSideEvents.Init = "function(s, e) { s.SetText(txtPassword.GetText()); }";
            if(chbMostrarPassword.Checked) {
                txtPassword.Password = false;
                lblPassword.Text = "Ocultar Contraseña";
                unlocked.Visible = true;
                locked.Visible = false;
            } else {
                txtPassword.Password = true;
                lblPassword.Text = "Mostrar Contraseña";
                locked.Visible = true;
                unlocked.Visible = false;
            }
        }
    }
}