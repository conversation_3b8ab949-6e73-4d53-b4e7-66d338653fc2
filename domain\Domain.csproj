﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\EntityFramework.6.4.4\build\EntityFramework.props" Condition="Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{A1E15DE3-73E9-42A5-8E4C-41753F59DBE4}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Domain</RootNamespace>
    <AssemblyName>Domain</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <PlatformTarget>x86</PlatformTarget>
    <OutputPath>bin\x86\Debug\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <PlatformTarget>x86</PlatformTarget>
    <OutputPath>bin\x86\Release\</OutputPath>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Antlr3.Runtime, Version=3.5.0.2, Culture=neutral, PublicKeyToken=eb42632606e9261f, processorArchitecture=MSIL">
      <HintPath>..\packages\Antlr3.Runtime.3.5.2-rc1\lib\net40-client\Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.4.4\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.4.4\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="Iesi.Collections, Version=4.0.0.4000, Culture=neutral, PublicKeyToken=aa95f207798dfdb4, processorArchitecture=MSIL">
      <HintPath>..\packages\Iesi.Collections.4.0.4\lib\net461\Iesi.Collections.dll</HintPath>
    </Reference>
    <Reference Include="NHibernate, Version=5.3.0.0, Culture=neutral, PublicKeyToken=aa95f207798dfdb4, processorArchitecture=MSIL">
      <HintPath>..\packages\NHibernate.5.3.3\lib\net461\NHibernate.dll</HintPath>
    </Reference>
    <Reference Include="Remotion.Linq, Version=2.2.0.0, Culture=neutral, PublicKeyToken=fee00910d6e5f53b, processorArchitecture=MSIL">
      <HintPath>..\packages\Remotion.Linq.2.2.0\lib\net45\Remotion.Linq.dll</HintPath>
    </Reference>
    <Reference Include="Remotion.Linq.EagerFetching, Version=2.2.0.0, Culture=neutral, PublicKeyToken=fee00910d6e5f53b, processorArchitecture=MSIL">
      <HintPath>..\packages\Remotion.Linq.EagerFetching.2.2.0\lib\net45\Remotion.Linq.EagerFetching.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Domain\Aplicacion.cs" />
    <Compile Include="Domain\BancoCuentas.cs" />
    <Compile Include="Domain\BancosTarjetas.cs" />
    <Compile Include="Domain\Cajas.cs" />
    <Compile Include="Domain\Calles.cs" />
    <Compile Include="Domain\Categoria.cs" />
    <Compile Include="Domain\Cheques.cs" />
    <Compile Include="Domain\Comprobantes.cs" />
    <Compile Include="Domain\ComprobantesCabeceras.cs" />
    <Compile Include="Domain\ComprobantesItems.cs" />
    <Compile Include="Domain\ComprobantesRelacionados.cs" />
    <Compile Include="Domain\ComprobantesRetenciones.cs" />
    <Compile Include="Domain\ComprobanteTipos.cs" />
    <Compile Include="Domain\ContratoContactos.cs" />
    <Compile Include="Domain\ContratoContactosEmail.cs" />
    <Compile Include="Domain\ContratoContactosTel.cs" />
    <Compile Include="Domain\ContratoDomicilios.cs" />
    <Compile Include="Domain\Contratos.cs" />
    <Compile Include="Domain\ContratoTipo.cs" />
    <Compile Include="Domain\CuponesTarjetas.cs" />
    <Compile Include="Domain\Departamentos.cs" />
    <Compile Include="Domain\Determinaciones.cs" />
    <Compile Include="Domain\DocTipos.cs" />
    <Compile Include="Domain\DomiciliosTipos.cs" />
    <Compile Include="Domain\Efectivo.cs" />
    <Compile Include="Domain\Fuentes.cs" />
    <Compile Include="Domain\FuentesTipos.cs" />
    <Compile Include="Domain\IVAAlicuotas.cs" />
    <Compile Include="Domain\IVACondiciones.cs" />
    <Compile Include="Domain\Localidades.cs" />
    <Compile Include="Domain\MetodosdeMedicion.cs" />
    <Compile Include="Domain\Modulo.cs" />
    <Compile Include="Domain\ModuloControl.cs" />
    <Compile Include="Domain\Monedas.cs" />
    <Compile Include="Domain\Numeraciones.cs" />
    <Compile Include="Domain\PersonasTipo.cs" />
    <Compile Include="Domain\PlantillaProtocolos.cs" />
    <Compile Include="Domain\ProtocoloDeterminaciones.cs" />
    <Compile Include="Domain\Protocolos.cs" />
    <Compile Include="Domain\ProtocolosEstados.cs" />
    <Compile Include="Domain\Provincias.cs" />
    <Compile Include="Domain\Recibos.cs" />
    <Compile Include="Domain\Retenciones.cs" />
    <Compile Include="Domain\Rol.cs" />
    <Compile Include="Domain\RolDeterminaciones.cs" />
    <Compile Include="Domain\RolModulo.cs" />
    <Compile Include="Domain\Sectores.cs" />
    <Compile Include="Domain\Sexos.cs" />
    <Compile Include="Domain\SubFuentes.cs" />
    <Compile Include="Domain\TelefonoTipo.cs" />
    <Compile Include="Domain\TipoAnalisis.cs" />
    <Compile Include="Domain\TipoPermiso.cs" />
    <Compile Include="Domain\Transferencias.cs" />
    <Compile Include="Domain\Usuario.cs" />
    <Compile Include="Domain\Zonas.cs" />
    <Compile Include="DTO\Converters.cs" />
    <Compile Include="DTO\DTO_ContratoContactos.cs" />
    <Compile Include="DTO\ModuloDTO.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Mappings\Categoria.hbm.xml" />
    <EmbeddedResource Include="Mappings\Modulo.hbm.xml" />
    <EmbeddedResource Include="Mappings\ModuloControl.hbm.xml" />
    <EmbeddedResource Include="Mappings\Rol.hbm.xml" />
    <EmbeddedResource Include="Mappings\RolModulo.hbm.xml" />
    <EmbeddedResource Include="Mappings\Usuario.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Mappings\BancoCuentas.hbm.xml" />
    <EmbeddedResource Include="Mappings\BancosTarjetas.hbm.xml" />
    <EmbeddedResource Include="Mappings\Cajas.hbm.xml" />
    <EmbeddedResource Include="Mappings\Comprobantes.hbm.xml" />
    <EmbeddedResource Include="Mappings\ComprobantesCabeceras.hbm.xml" />
    <EmbeddedResource Include="Mappings\ComprobantesItems.hbm.xml" />
    <EmbeddedResource Include="Mappings\ComprobantesRelacionados.hbm.xml" />
    <EmbeddedResource Include="Mappings\ComprobantesRetenciones.hbm.xml" />
    <EmbeddedResource Include="Mappings\ComprobanteTipos.hbm.xml" />
    <EmbeddedResource Include="Mappings\Contratos.hbm.xml" />
    <EmbeddedResource Include="Mappings\CuponesTarjetas.hbm.xml" />
    <EmbeddedResource Include="Mappings\DocTipos.hbm.xml" />
    <EmbeddedResource Include="Mappings\Efectivo.hbm.xml" />
    <EmbeddedResource Include="Mappings\IVAAlicuotas.hbm.xml" />
    <EmbeddedResource Include="Mappings\IVACondiciones.hbm.xml" />
    <EmbeddedResource Include="Mappings\Localidades.hbm.xml" />
    <EmbeddedResource Include="Mappings\Monedas.hbm.xml" />
    <EmbeddedResource Include="Mappings\Provincias.hbm.xml" />
    <EmbeddedResource Include="Mappings\Recibos.hbm.xml" />
    <EmbeddedResource Include="Mappings\Retenciones.hbm.xml" />
    <EmbeddedResource Include="Mappings\Transferencias.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Mappings\Aplicacion.hbm.xml" />
    <EmbeddedResource Include="Mappings\Departamentos.hbm.xml" />
    <EmbeddedResource Include="Mappings\Determinaciones.hbm.xml" />
    <EmbeddedResource Include="Mappings\Fuentes.hbm.xml" />
    <EmbeddedResource Include="Mappings\ProtocoloDeterminaciones.hbm.xml" />
    <EmbeddedResource Include="Mappings\Protocolos.hbm.xml" />
    <EmbeddedResource Include="Mappings\Sectores.hbm.xml" />
    <EmbeddedResource Include="Mappings\SubFuentes.hbm.xml" />
    <EmbeddedResource Include="Mappings\TipoAnalisis.hbm.xml" />
    <EmbeddedResource Include="Mappings\Zonas.hbm.xml" />
    <EmbeddedResource Include="Mappings\Numeraciones.hbm.xml" />
    <EmbeddedResource Include="Mappings\ContratoContactosEmail.hbm.xml" />
    <EmbeddedResource Include="Mappings\ContratoContactosTel.hbm.xml" />
    <EmbeddedResource Include="Mappings\Sexos.hbm.xml" />
    <EmbeddedResource Include="Mappings\ContratoContactos.hbm.xml" />
    <EmbeddedResource Include="Mappings\ContratoDomicilios.hbm.xml" />
    <EmbeddedResource Include="Mappings\DomiciliosTipos.hbm.xml" />
    <EmbeddedResource Include="Mappings\TelefonoTipo.hbm.xml" />
    <EmbeddedResource Include="Mappings\Calles.hbm.xml" />
    <EmbeddedResource Include="Mappings\Cheques.hbm.xml" />
    <EmbeddedResource Include="Mappings\PersonasTipo.hbm.xml" />
    <EmbeddedResource Include="Mappings\TipoPermiso.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Mappings\FuentesTipos.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Mappings\ContratoTipo.hbm.xml" />
    <EmbeddedResource Include="Mappings\MetodosdeMedicion.hbm.xml" />
    <EmbeddedResource Include="Mappings\RolDeterminaciones.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Mappings\PlantillaProtocolos.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Mappings\ProtocolosEstados.hbm.xml" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>Este proyecto hace referencia a los paquetes NuGet que faltan en este equipo. Use la restauración de paquetes NuGet para descargarlos. Para obtener más información, consulte http://go.microsoft.com/fwlink/?LinkID=322105. El archivo que falta es {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.4.4\build\EntityFramework.props'))" />
    <Error Condition="!Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.4.4\build\EntityFramework.targets'))" />
  </Target>
  <Import Project="..\packages\EntityFramework.6.4.4\build\EntityFramework.targets" Condition="Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.targets')" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>