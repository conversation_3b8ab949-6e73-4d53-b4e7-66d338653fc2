//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 3/11/2023 09:44:13
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class PersonasTipoRepository : NHibernateRepository<Domain.PersonasTipo>, IPersonasTipoRepository
    {
        public PersonasTipoRepository(ISession session) : base(session)
        {
        }

        public virtual ICollection<Domain.PersonasTipo> GetAll()
        {
            return session.CreateQuery(string.Format("from PersonasTipo")).List<Domain.PersonasTipo>();
        }

        public virtual Domain.PersonasTipo GetByKey(int _Id)
        {
            return session.Get<Domain.PersonasTipo>(_Id);
        }
    }
}
