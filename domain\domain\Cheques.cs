//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 25/9/2023 14:39:43
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.Cheques, Domain in the schema.
    /// </summary>
    public partial class Cheques {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for Cheques constructor in the schema.
        /// </summary>
        public Cheques()
        {
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for SerieNro in the schema.
        /// </summary>
        public virtual string SerieNro
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ImporteCheque in the schema.
        /// </summary>
        public virtual System.Nullable<decimal> ImporteCheque
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for FecEmision in the schema.
        /// </summary>
        public virtual System.Nullable<System.DateTime> FecEmision
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for FecDeposito in the schema.
        /// </summary>
        public virtual System.Nullable<System.DateTime> FecDeposito
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Diferido in the schema.
        /// </summary>
        public virtual System.Nullable<bool> Diferido
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Comprobantes in the schema.
        /// </summary>
        public virtual Comprobantes Comprobantes
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Monedas in the schema.
        /// </summary>
        public virtual Monedas Monedas
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for BancosTarjetas in the schema.
        /// </summary>
        public virtual BancosTarjetas BancosTarjetas
        {
            get;
            set;
        }
    }

}
