<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="ComprobanteTipos" table="ComprobanteTipos">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Descripcion" type="String">
      <column name="Descripcion" not-null="false" length="30" sql-type="nvarchar" />
    </property>
    <property name="Abreviatura" type="String">
      <column name="Abreviatura" not-null="false" length="3" sql-type="nvarchar" />
    </property>
    <property name="Signo" type="Int16">
      <column name="Signo" not-null="false" precision="5" scale="0" sql-type="smallint" />
    </property>
    <property name="UltimoNumero" type="Decimal">
      <column name="UltimoNumero" not-null="true" precision="18" scale="0" sql-type="decimal" />
    </property>
    <property name="RelacionadoADeudas" type="Int16">
      <column name="RelacionadoADeudas" not-null="true" precision="5" scale="0" sql-type="smallint" />
    </property>
    <property name="Observaciones" type="String">
      <column name="Observaciones" not-null="false" sql-type="nvarchar" />
    </property>
    <property name="SeMuestra" type="Int32">
      <column name="SeMuestra" not-null="true" precision="10" scale="0" sql-type="int" />
    </property>
    <property name="IdComprobante" type="String">
      <column name="IdComprobante" not-null="false" length="10" sql-type="nchar" />
    </property>
    <set name="Comprobantes" inverse="true" generic="true">
      <key>
        <column name="IdComprobanteTipo" />
      </key>
      <one-to-many class="Comprobantes" />
    </set>
  </class>
</hibernate-mapping>