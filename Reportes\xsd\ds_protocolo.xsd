﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="ds_protocolo" targetNamespace="http://tempuri.org/ds_protocolo.xsd" xmlns:mstns="http://tempuri.org/ds_protocolo.xsd" xmlns="http://tempuri.org/ds_protocolo.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="ds_protocolo" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="ds_protocolo" msprop:EnableTableAdapterManager="true" msprop:Generator_DataSetName="ds_protocolo">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="Protocolo" msprop:Generator_RowEvHandlerName="ProtocoloRowChangeEventHandler" msprop:Generator_RowDeletedName="ProtocoloRowDeleted" msprop:Generator_RowDeletingName="ProtocoloRowDeleting" msprop:Generator_RowEvArgName="ProtocoloRowChangeEvent" msprop:Generator_TablePropName="Protocolo" msprop:Generator_RowChangedName="ProtocoloRowChanged" msprop:Generator_UserTableName="Protocolo" msprop:Generator_RowChangingName="ProtocoloRowChanging" msprop:Generator_RowClassName="ProtocoloRow" msprop:Generator_TableClassName="ProtocoloDataTable" msprop:Generator_TableVarName="tableProtocolo">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Id" msprop:Generator_ColumnPropNameInTable="IdColumn" msprop:Generator_ColumnPropNameInRow="Id" msprop:Generator_UserColumnName="Id" msprop:Generator_ColumnVarNameInTable="columnId" type="xs:int" />
              <xs:element name="NroProtocolo" msprop:Generator_ColumnPropNameInTable="NroProtocoloColumn" msprop:Generator_ColumnPropNameInRow="NroProtocolo" msprop:Generator_UserColumnName="NroProtocolo" msprop:Generator_ColumnVarNameInTable="columnNroProtocolo" type="xs:int" minOccurs="0" />
              <xs:element name="FechaToma" msprop:Generator_ColumnPropNameInTable="FechaTomaColumn" msprop:Generator_ColumnPropNameInRow="FechaToma" msprop:Generator_UserColumnName="FechaToma" msprop:Generator_ColumnVarNameInTable="columnFechaToma" type="xs:dateTime" minOccurs="0" />
              <xs:element name="RecepcionToma" msprop:Generator_ColumnPropNameInTable="RecepcionTomaColumn" msprop:Generator_ColumnPropNameInRow="RecepcionToma" msprop:Generator_UserColumnName="RecepcionToma" msprop:Generator_ColumnVarNameInTable="columnRecepcionToma" type="xs:dateTime" minOccurs="0" />
              <xs:element name="ObservacionSitioToma" msprop:Generator_ColumnPropNameInTable="ObservacionSitioTomaColumn" msprop:Generator_ColumnPropNameInRow="ObservacionSitioToma" msprop:Generator_UserColumnName="ObservacionSitioToma" msprop:Generator_ColumnVarNameInTable="columnObservacionSitioToma" type="xs:string" minOccurs="0" />
              <xs:element name="Expediente" msprop:Generator_ColumnPropNameInTable="ExpedienteColumn" msprop:Generator_ColumnPropNameInRow="Expediente" msprop:Generator_UserColumnName="Expediente" msprop:Generator_ColumnVarNameInTable="columnExpediente" type="xs:string" minOccurs="0" />
              <xs:element name="Nro_de_Fuentes" msprop:Generator_ColumnPropNameInTable="Nro_de_FuentesColumn" msprop:Generator_ColumnPropNameInRow="Nro_de_Fuentes" msprop:Generator_UserColumnName="Nro_de_Fuentes" msprop:Generator_ColumnVarNameInTable="columnNro_de_Fuentes" type="xs:string" minOccurs="0" />
              <xs:element name="Descripcion_Fuentes" msprop:Generator_ColumnPropNameInTable="Descripcion_FuentesColumn" msprop:Generator_ColumnPropNameInRow="Descripcion_Fuentes" msprop:Generator_UserColumnName="Descripcion_Fuentes" msprop:Generator_ColumnVarNameInTable="columnDescripcion_Fuentes" type="xs:string" minOccurs="0" />
              <xs:element name="Descripcion_SubFuentes" msprop:Generator_ColumnPropNameInTable="Descripcion_SubFuentesColumn" msprop:Generator_ColumnPropNameInRow="Descripcion_SubFuentes" msprop:Generator_UserColumnName="Descripcion_SubFuentes" msprop:Generator_ColumnVarNameInTable="columnDescripcion_SubFuentes" type="xs:string" minOccurs="0" />
              <xs:element name="Localidad_Fuentes" msprop:Generator_ColumnPropNameInTable="Localidad_FuentesColumn" msprop:Generator_ColumnPropNameInRow="Localidad_Fuentes" msprop:Generator_UserColumnName="Localidad_Fuentes" msprop:Generator_ColumnVarNameInTable="columnLocalidad_Fuentes" type="xs:string" minOccurs="0" />
              <xs:element name="Descripcion_Departamento" msprop:Generator_ColumnPropNameInTable="Descripcion_DepartamentoColumn" msprop:Generator_ColumnPropNameInRow="Descripcion_Departamento" msprop:Generator_UserColumnName="Descripcion_Departamento" msprop:Generator_ColumnVarNameInTable="columnDescripcion_Departamento" type="xs:string" minOccurs="0" />
              <xs:element name="Nombre_Protocolo_Estado" msprop:Generator_ColumnPropNameInTable="Nombre_Protocolo_EstadoColumn" msprop:Generator_ColumnPropNameInRow="Nombre_Protocolo_Estado" msprop:Generator_UserColumnName="Nombre_Protocolo_Estado" msprop:Generator_ColumnVarNameInTable="columnNombre_Protocolo_Estado" type="xs:string" minOccurs="0" />
              <xs:element name="Nombre_Solicitante_Contrato" msprop:Generator_ColumnPropNameInTable="Nombre_Solicitante_ContratoColumn" msprop:Generator_ColumnPropNameInRow="Nombre_Solicitante_Contrato" msprop:Generator_UserColumnName="Nombre_Solicitante_Contrato" msprop:Generator_ColumnVarNameInTable="columnNombre_Solicitante_Contrato" type="xs:string" minOccurs="0" />
              <xs:element name="Nombre_Tomador_Contrato" msprop:Generator_ColumnPropNameInTable="Nombre_Tomador_ContratoColumn" msprop:Generator_ColumnPropNameInRow="Nombre_Tomador_Contrato" msprop:Generator_UserColumnName="Nombre_Tomador_Contrato" msprop:Generator_ColumnVarNameInTable="columnNombre_Tomador_Contrato" type="xs:string" minOccurs="0" />
              <xs:element name="Tipo_de_Analisis" msprop:Generator_UserColumnName="Tipo_de_Analisis" msprop:Generator_ColumnPropNameInTable="Tipo_de_AnalisisColumn" msprop:Generator_ColumnPropNameInRow="Tipo_de_Analisis" msprop:Generator_ColumnVarNameInTable="columnTipo_de_Analisis" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Determinaciones" msprop:Generator_RowEvHandlerName="DeterminacionesRowChangeEventHandler" msprop:Generator_RowDeletedName="DeterminacionesRowDeleted" msprop:Generator_RowDeletingName="DeterminacionesRowDeleting" msprop:Generator_RowEvArgName="DeterminacionesRowChangeEvent" msprop:Generator_TablePropName="Determinaciones" msprop:Generator_RowChangedName="DeterminacionesRowChanged" msprop:Generator_UserTableName="Determinaciones" msprop:Generator_RowChangingName="DeterminacionesRowChanging" msprop:Generator_RowClassName="DeterminacionesRow" msprop:Generator_TableClassName="DeterminacionesDataTable" msprop:Generator_TableVarName="tableDeterminaciones">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Id" msprop:Generator_ColumnPropNameInTable="IdColumn" msprop:Generator_ColumnPropNameInRow="Id" msprop:Generator_UserColumnName="Id" msprop:Generator_ColumnVarNameInTable="columnId" type="xs:int" />
              <xs:element name="NroDeterminacion" msprop:Generator_ColumnPropNameInTable="NroDeterminacionColumn" msprop:Generator_ColumnPropNameInRow="NroDeterminacion" msprop:Generator_UserColumnName="NroDeterminacion" msprop:Generator_ColumnVarNameInTable="columnNroDeterminacion" type="xs:int" minOccurs="0" />
              <xs:element name="Descripcion" msprop:Generator_ColumnPropNameInTable="DescripcionColumn" msprop:Generator_ColumnPropNameInRow="Descripcion" msprop:Generator_UserColumnName="Descripcion" msprop:Generator_ColumnVarNameInTable="columnDescripcion" type="xs:string" minOccurs="0" />
              <xs:element name="Unidad_primaria" msprop:Generator_ColumnPropNameInTable="Unidad_primariaColumn" msprop:Generator_ColumnPropNameInRow="Unidad_primaria" msprop:Generator_UserColumnName="Unidad_primaria" msprop:Generator_ColumnVarNameInTable="columnUnidad_primaria" type="xs:string" minOccurs="0" />
              <xs:element name="Unidad_secundaria" msprop:Generator_ColumnPropNameInTable="Unidad_secundariaColumn" msprop:Generator_ColumnPropNameInRow="Unidad_secundaria" msprop:Generator_UserColumnName="Unidad_secundaria" msprop:Generator_ColumnVarNameInTable="columnUnidad_secundaria" type="xs:string" minOccurs="0" />
              <xs:element name="Factor_conversion_Prefijo" msprop:Generator_ColumnPropNameInTable="Factor_conversion_PrefijoColumn" msprop:Generator_ColumnPropNameInRow="Factor_conversion_Prefijo" msprop:Generator_UserColumnName="Factor_conversion_Prefijo" msprop:Generator_ColumnVarNameInTable="columnFactor_conversion_Prefijo" type="xs:decimal" minOccurs="0" />
              <xs:element name="Factor_conversion_Sufijo" msprop:Generator_ColumnPropNameInTable="Factor_conversion_SufijoColumn" msprop:Generator_ColumnPropNameInRow="Factor_conversion_Sufijo" msprop:Generator_UserColumnName="Factor_conversion_Sufijo" msprop:Generator_ColumnVarNameInTable="columnFactor_conversion_Sufijo" type="xs:decimal" minOccurs="0" />
              <xs:element name="ValorReferencia" msprop:Generator_ColumnPropNameInTable="ValorReferenciaColumn" msprop:Generator_ColumnPropNameInRow="ValorReferencia" msprop:Generator_UserColumnName="ValorReferencia" msprop:Generator_ColumnVarNameInTable="columnValorReferencia" type="xs:string" minOccurs="0" />
              <xs:element name="Decimales_mostrar" msprop:Generator_ColumnPropNameInTable="Decimales_mostrarColumn" msprop:Generator_ColumnPropNameInRow="Decimales_mostrar" msprop:Generator_UserColumnName="Decimales_mostrar" msprop:Generator_ColumnVarNameInTable="columnDecimales_mostrar" type="xs:int" minOccurs="0" />
              <xs:element name="Activo" msprop:Generator_ColumnPropNameInTable="ActivoColumn" msprop:Generator_ColumnPropNameInRow="Activo" msprop:Generator_UserColumnName="Activo" msprop:Generator_ColumnVarNameInTable="columnActivo" type="xs:boolean" minOccurs="0" />
              <xs:element name="Resultado" msprop:Generator_ColumnPropNameInTable="ResultadoColumn" msprop:Generator_ColumnPropNameInRow="Resultado" msprop:Generator_UserColumnName="Resultado" msprop:Generator_ColumnVarNameInTable="columnResultado" type="xs:decimal" minOccurs="0" />
              <xs:element name="Signo" msprop:Generator_ColumnPropNameInTable="SignoColumn" msprop:Generator_ColumnPropNameInRow="Signo" msprop:Generator_UserColumnName="Signo" msprop:Generator_ColumnVarNameInTable="columnSigno" type="xs:string" minOccurs="0" />
              <xs:element name="Descripcion_TipoAnalisis" msprop:Generator_ColumnPropNameInTable="Descripcion_TipoAnalisisColumn" msprop:Generator_ColumnPropNameInRow="Descripcion_TipoAnalisis" msprop:Generator_UserColumnName="Descripcion_TipoAnalisis" msprop:Generator_ColumnVarNameInTable="columnDescripcion_TipoAnalisis" type="xs:string" minOccurs="0" />
              <xs:element name="Observacion" msprop:Generator_ColumnPropNameInTable="ObservacionColumn" msprop:Generator_ColumnPropNameInRow="Observacion" msprop:Generator_UserColumnName="Observacion" msprop:Generator_ColumnVarNameInTable="columnObservacion" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="ProtocoloKey1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Protocolo" />
      <xs:field xpath="mstns:Id" />
    </xs:unique>
    <xs:unique name="DeterminacionesKey1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Determinaciones" />
      <xs:field xpath="mstns:Id" />
    </xs:unique>
  </xs:element>
</xs:schema>