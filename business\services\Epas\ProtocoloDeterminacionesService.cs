﻿using Common.DDL;
using DAO;
using Domain;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Epas
{
    public class ProtocoloDeterminacionesService
    {
        //Trae todo
        public static IList<Domain.ProtocoloDeterminaciones> GetAll()
        {
            IList<Domain.ProtocoloDeterminaciones> listaAux;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                listaAux = (IList<Domain.ProtocoloDeterminaciones>)new ProtocoloDeterminacionesRepository(sess).GetAll();
                foreach(ProtocoloDeterminaciones pd in listaAux)
                {
                    NHibernateUtil.Initialize(pd.Determinaciones);
                    NHibernateUtil.Initialize(pd.Protocolos);
                    NHibernateUtil.Initialize(pd.Protocolos.ProtocolosEstados);

                }
                sess.Close();
                sess.Dispose();
            }
            return (listaAux);

        }

        public static List<ItemCombo> GetAllCombo() {
            IList<Domain.ProtocoloDeterminaciones> listaAux;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                listaAux = (IList<Domain.ProtocoloDeterminaciones>)new ProtocoloDeterminacionesRepository(sess).GetAll();

                foreach(ProtocoloDeterminaciones pd in listaAux)
                {
                    NHibernateUtil.Initialize(pd.Determinaciones);
                    NHibernateUtil.Initialize(pd.Protocolos);
                    NHibernateUtil.Initialize(pd.Protocolos.ProtocolosEstados);
                }
                sess.Close();
                sess.Dispose();
            }

            ItemCombo item;
            ListaCombo lista = new ListaCombo();
            foreach(ProtocoloDeterminaciones p in listaAux) {

                item = new ItemCombo();
                item.ID = p.Id.ToString();
                item.Descripcion = p.Determinaciones.Descripcion;
                lista.Add(item);
            }
            return (lista);
        }

        //obtener por id
        public static Domain.ProtocoloDeterminaciones GetById(int id)
        {

            Domain.ProtocoloDeterminaciones pd;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {

                pd = (Domain.ProtocoloDeterminaciones)new ProtocoloDeterminacionesRepository(sess).GetByKey(id);
               
                NHibernateUtil.Initialize(pd.Determinaciones);
                NHibernateUtil.Initialize(pd.Protocolos);
                NHibernateUtil.Initialize(pd.Protocolos.ProtocolosEstados);

                sess.Close();
                sess.Dispose();
                return pd;
            }

        }


        /// <summary>
      
        /// </summary>
        /// <param name="codigo"></param>
        /// <returns></returns>
        public static IList<Domain.ProtocoloDeterminaciones> GetByIDProtocolo(int idProtocolo)
        {

            IList<Domain.ProtocoloDeterminaciones> listaAux;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {

                listaAux =new ProtocoloDeterminacionesRepository(sess).GetByIDProtocolo(idProtocolo);
                foreach(ProtocoloDeterminaciones pd in listaAux)
                {
                    NHibernateUtil.Initialize(pd.Determinaciones);
                    NHibernateUtil.Initialize(pd.Protocolos);
                    NHibernateUtil.Initialize(pd.Protocolos.ProtocolosEstados);
                }

                sess.Close();
                sess.Dispose();
                return listaAux;

            }

        }


        public static Domain.ProtocoloDeterminaciones GetByIDProtocoloAndIDDeterminacion(int idProtocolo, int idDeterminacion)
        {

            Domain.ProtocoloDeterminaciones listaAux;
            using(ISession sess = NHibernateSessionProvider.GetSession())
            {

                listaAux = new ProtocoloDeterminacionesRepository(sess).GetByIDProtocoloAndIDDeterminacion(idProtocolo,idDeterminacion);

              if(listaAux != null)
                {
                    NHibernateUtil.Initialize(listaAux.Determinaciones);
                    NHibernateUtil.Initialize(listaAux.Protocolos);
                    NHibernateUtil.Initialize(listaAux.Protocolos.ProtocolosEstados);
                }

                sess.Close();
                sess.Dispose();
                return listaAux;

            }

        }



        //agregar y actualizar
        public static void SaveOrUpdate(Domain.ProtocoloDeterminaciones a)
        {

            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {

                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {

                    try
                    {

                        new ProtocoloDeterminacionesRepository(sess).Add(a);

                        tx.Commit();
                    }
                    catch (Exception e)
                    {

                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {

                        sess.Close();
                        sess.Dispose();
                    }

                }

            }

        }

        //borrar
        public static void Delete(Domain.ProtocoloDeterminaciones a)
        {
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new ProtocoloDeterminacionesRepository(sess).Remove(a);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }

                }

            }

        }

    }
}
