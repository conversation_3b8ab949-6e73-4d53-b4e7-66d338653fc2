﻿using Business.Services.Contratos;
using Business.Services.Epas;
using Business.Services.Provincias;
using DevExpress.Utils.Extensions;
using DevExpress.Web;
using Domain;
using Domain.DTO;
using NHibernate.Mapping;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics.Contracts;
using System.Linq;
using System.Security.Cryptography;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web.EPAS.Lista
{
    public partial class Plantilla : System.Web.UI.Page
    {
        protected void Page_Load(object sender,EventArgs e)
        {
            try
            {
                LlenarGvPlantilla();

                if(!IsPostBack)
                {
                }

            } catch(Exception ex)
            {
                ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('Error al cargar la grilla de Protocolos. Consulte al administrador del sistema.', 'error');",true);
            }
        }

        private void LlenarGvPlantilla()
        {
            try
            {
                List<Domain.PlantillaProtocolos> pp = (List<Domain.PlantillaProtocolos>) PlantillaProtocolosService.GetAll();
                gvPlantillas.DataSource = pp;
                gvPlantillas.DataBind();

            } catch(Exception ex)
            {
                string g = ex.Message;
            }
        }

        protected void btnEditar_Click(object sender,EventArgs e)
        {
            try
            {
                ASPxButton btn = (ASPxButton) sender;
                if(btn != null)
                {
                    GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer) btn.NamingContainer;
                    if(c != null)
                    {
                        var ind = DataBinder.Eval(c.DataItem,"Id");

                        if(ind != null)
                        {
                            Domain.PlantillaProtocolos PP = PlantillaProtocolosService.GetById(Convert.ToInt32(ind));

                            if(PP.Activo == false)
                            {
                                string message = string.Format("La Plantilla {0} no puede ser editada ya que está anulada.",PP.Descripcion);
                                ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('" + message + "')",true);

                            } else
                            {
                                Response.Redirect(Global.ApplicationPath + "/EPAS/AddEdit/AddEditPlantilla.aspx?id=" + Util.Helpers.Encrypt(ind.ToString()),false);
                            }

                        }
                    }
                }
            } catch(Exception ex)
            {
                ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('Error al tratar de editar un elemento.Consulte al administrador del sistema.', 'error');",true);
            }
        }

        protected void btnAnularActivar_Click(object sender,EventArgs e)
        {
            try
            {
                ASPxButton btn = (ASPxButton) sender;
                GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer) btn.NamingContainer;
                var ind = DataBinder.Eval(c.DataItem,"Id");

                Domain.PlantillaProtocolos PP = PlantillaProtocolosService.GetById(Convert.ToInt32(ind));
                PP.Activo = !PP.Activo;

                PlantillaProtocolosService.SaveOrUpdate(PP);
                LlenarGvPlantilla();


            } catch(Exception ex)
            {
                ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('Error al tratar de eliminar un elemento, consulte al administrador del sistema', 'error');",true);

            }
        }

    }
}