﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{EE87FD63-87BC-4406-884F-185CD3597792}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>DAO</RootNamespace>
    <AssemblyName>DAO</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <PlatformTarget>x86</PlatformTarget>
    <OutputPath>bin\x86\Debug\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <PlatformTarget>x86</PlatformTarget>
    <OutputPath>bin\x86\Release\</OutputPath>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Antlr3.Runtime, Version=3.5.0.2, Culture=neutral, PublicKeyToken=eb42632606e9261f, processorArchitecture=MSIL">
      <HintPath>..\packages\Antlr3.Runtime.3.5.2-rc1\lib\net40-client\Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Iesi.Collections, Version=4.0.0.4000, Culture=neutral, PublicKeyToken=aa95f207798dfdb4, processorArchitecture=MSIL">
      <HintPath>..\packages\Iesi.Collections.4.0.4\lib\net461\Iesi.Collections.dll</HintPath>
    </Reference>
    <Reference Include="NHibernate, Version=5.3.0.0, Culture=neutral, PublicKeyToken=aa95f207798dfdb4, processorArchitecture=MSIL">
      <HintPath>..\packages\NHibernate.5.3.3\lib\net461\NHibernate.dll</HintPath>
    </Reference>
    <Reference Include="Remotion.Linq, Version=2.2.0.0, Culture=neutral, PublicKeyToken=fee00910d6e5f53b, processorArchitecture=MSIL">
      <HintPath>..\packages\Remotion.Linq.2.2.0\lib\net45\Remotion.Linq.dll</HintPath>
    </Reference>
    <Reference Include="Remotion.Linq.EagerFetching, Version=2.2.0.0, Culture=neutral, PublicKeyToken=fee00910d6e5f53b, processorArchitecture=MSIL">
      <HintPath>..\packages\Remotion.Linq.EagerFetching.2.2.0\lib\net45\Remotion.Linq.EagerFetching.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="DAO\CallesRepository.cs" />
    <Compile Include="DAO\ChequesRepository.cs" />
    <Compile Include="DAO\DepartamentosRepository.cs" />
    <Compile Include="DAO\ContratoContactosEmailRepository.cs" />
    <Compile Include="DAO\ContratoContactosRepository.cs" />
    <Compile Include="DAO\ContratoContactosTelRepository.cs" />
    <Compile Include="DAO\ContratoDomiciliosRepository.cs" />
    <Compile Include="DAO\ContratosRepository.cs" />
    <Compile Include="DAO\DocTiposRepository.cs" />
    <Compile Include="DAO\DomiciliosTiposRepository.cs" />
    <Compile Include="DAO\ContratoTipoRepository.cs" />
    <Compile Include="DAO\DeterminacionesRepository.cs" />
    <Compile Include="DAO\PlantillaProtocolosRepository.cs" />
    <Compile Include="DAO\ProtocoloDeterminacionesRepository.cs" />
    <Compile Include="DAO\ProtocolosEstadosRepository.cs" />
    <Compile Include="DAO\ProtocolosRepository.cs" />
    <Compile Include="DAO\RolDeterminacionesRepository.cs" />
    <Compile Include="DAO\MetodosdeMedicionRepository.cs" />
    <Compile Include="DAO\IVACondicionesRepository.cs" />
    <Compile Include="DAO\LocalidadesRepository.cs" />
    <Compile Include="DAO\PersonasTipoRepository.cs" />
    <Compile Include="DAO\ProvinciasRepository.cs" />
    <Compile Include="DAO\SexosRepository.cs" />
    <Compile Include="DAO\TelefonoTipoRepository.cs" />
    <Compile Include="DAO\FuentesRepository.cs" />
    <Compile Include="DAO\FuentesTiposRepository.cs" />
    <Compile Include="DAO\NumeracionesRepository.cs" />
    <Compile Include="DAO\SectoresRepository.cs" />
    <Compile Include="DAO\SubFuentesRepository.cs" />
    <Compile Include="DAO\TipoAnalisisRepository.cs" />
    <Compile Include="DAO\ZonasRepository.cs" />
    <Compile Include="Interface\IContratoContactosEmailRepository.cs" />
    <Compile Include="Interface\IContratoContactosTelRepository.cs" />
    <Compile Include="Interface\IContratoTipoRepository.cs" />
    <Compile Include="Interface\IDepartamentosRepository.cs" />
    <Compile Include="Interface\IDeterminacionesRepository.cs" />
    <Compile Include="Interface\IFuentesRepository.cs" />
    <Compile Include="Interface\IFuentesTiposRepository.cs" />
    <Compile Include="Interface\INumeracionesRepository.cs" />
    <Compile Include="Interface\IPlantillaProtocolosRepository.cs" />
    <Compile Include="Interface\IProtocoloDeterminacionesRepository.cs" />
    <Compile Include="Interface\IProtocolosEstadosRepository.cs" />
    <Compile Include="Interface\IProtocolosRepository.cs" />
    <Compile Include="Interface\IRolDeterminacionesRepository.cs" />
    <Compile Include="Interface\ISectoresRepository.cs" />
    <Compile Include="Interface\ISexosRepository.cs" />
    <Compile Include="DAO\ModuloControlRepository.cs" />
    <Compile Include="DAO\ModuloRepository.cs" />
    <Compile Include="DAO\NHibernateRepository.cs" />
    <Compile Include="DAO\NHibernateSessionProvider.cs" />
    <Compile Include="DAO\NHibernateUnitOfWork.cs" />
    <Compile Include="DAO\NHibernateUnitOfWorkFactory.cs" />
    <Compile Include="DAO\RolModuloRepository.cs" />
    <Compile Include="DAO\RolRepository.cs" />
    <Compile Include="DAO\BancoCuentasRepository.cs" />
    <Compile Include="DAO\BancosTarjetasRepository.cs" />
    <Compile Include="DAO\CajasRepository.cs" />
    <Compile Include="DAO\ComprobantesCabecerasRepository.cs" />
    <Compile Include="DAO\ComprobantesItemsRepository.cs" />
    <Compile Include="DAO\ComprobantesRelacionadosRepository.cs" />
    <Compile Include="DAO\ComprobantesRepository.cs" />
    <Compile Include="DAO\ComprobantesRetencionesRepository.cs" />
    <Compile Include="DAO\ComprobanteTiposRepository.cs" />
    <Compile Include="DAO\CuponesTarjetasRepository.cs" />
    <Compile Include="DAO\EfectivoRepository.cs" />
    <Compile Include="DAO\IVAAlicuotasRepository.cs" />
    <Compile Include="DAO\MonedasRepository.cs" />
    <Compile Include="DAO\RecibosRepository.cs" />
    <Compile Include="DAO\RetencionesRepository.cs" />
    <Compile Include="DAO\TransferenciasRepository.cs" />
    <Compile Include="DAO\TipoPermisoRepository.cs" />
    <Compile Include="DAO\UsuarioRepository.cs" />
    <Compile Include="Interface\IBancoCuentasRepository.cs" />
    <Compile Include="Interface\IBancosTarjetasRepository.cs" />
    <Compile Include="Interface\ICajasRepository.cs" />
    <Compile Include="Interface\ICallesRepository.cs" />
    <Compile Include="Interface\ICategoriaRepository.cs" />
    <Compile Include="Interface\IChequesRepository.cs" />
    <Compile Include="Interface\IComprobantesCabecerasRepository.cs" />
    <Compile Include="Interface\IComprobantesItemsRepository.cs" />
    <Compile Include="Interface\IComprobantesRelacionadosRepository.cs" />
    <Compile Include="Interface\IComprobantesRepository.cs" />
    <Compile Include="Interface\IComprobantesRetencionesRepository.cs" />
    <Compile Include="Interface\IComprobanteTiposRepository.cs" />
    <Compile Include="Interface\IContratoContactosRepository.cs" />
    <Compile Include="Interface\IContratoDomiciliosRepository.cs" />
    <Compile Include="Interface\IContratosRepository.cs" />
    <Compile Include="Interface\ICuponesTarjetasRepository.cs" />
    <Compile Include="Interface\IDocTiposRepository.cs" />
    <Compile Include="Interface\IDomiciliosTiposRepository.cs" />
    <Compile Include="Interface\IEfectivoRepository.cs" />
    <Compile Include="Interface\IIVAAlicuotasRepository.cs" />
    <Compile Include="Interface\IIVACondicionesRepository.cs" />
    <Compile Include="Interface\ILocalidadesRepository.cs" />
    <Compile Include="Interface\IModuloControlRepository.cs" />
    <Compile Include="Interface\IModuloRepository.cs" />
    <Compile Include="Interface\IMonedasRepository.cs" />
    <Compile Include="Interface\IPersonasTipoRepository.cs" />
    <Compile Include="Interface\IProvinciasRepository.cs" />
    <Compile Include="Interface\IRecibosRepository.cs" />
    <Compile Include="Interface\IRepository.cs" />
    <Compile Include="Interface\IRetencionesRepository.cs" />
    <Compile Include="Interface\IRolModuloRepository.cs" />
    <Compile Include="Interface\IRolRepository.cs" />
    <Compile Include="Interface\ISubFuentesRepository.cs" />
    <Compile Include="Interface\ITelefonoTipoRepository.cs" />
    <Compile Include="Interface\ITipoAnalisisRepository.cs" />
    <Compile Include="Interface\ITipoPermisoRepository.cs" />
    <Compile Include="Interface\ITransferenciasRepository.cs" />
    <Compile Include="Interface\IUnitOfWork.cs" />
    <Compile Include="Interface\IUnitOfWorkFactory.cs" />
    <Compile Include="Interface\IUsuarioRepository.cs" />
    <Compile Include="Interface\IZonasRepository.cs" />
    <Compile Include="Interface\IMetodosdeMedicionRepository.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Common\Common.csproj">
      <Project>{B240BC08-FBE1-46B6-93E1-F1146A829C4C}</Project>
      <Name>Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\Domain\Domain.csproj">
      <Project>{A1E15DE3-73E9-42A5-8E4C-41753F59DBE4}</Project>
      <Name>Domain</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>