<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="ContratoTipo" table="ContratoTipo">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Descripcion" type="String">
      <column name="Descripcion" not-null="true" length="50" sql-type="varchar" />
    </property>
    <property name="Activo" type="Boolean">
      <column name="Activo" not-null="true" sql-type="bit" />
    </property>
    <set name="Contratos" inverse="true" generic="true">
      <key>
        <column name="IdContratoTipo" />
      </key>
      <one-to-many class="Contratos" />
    </set>
  </class>
</hibernate-mapping>