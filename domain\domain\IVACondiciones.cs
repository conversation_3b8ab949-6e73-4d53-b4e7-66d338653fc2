//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 3/11/2023 09:44:12
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.IVACondiciones, Domain in the schema.
    /// </summary>
    [Serializable]
    
    public partial class IVACondiciones {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for IVACondiciones constructor in the schema.
        /// </summary>
        public IVACondiciones()
        {
            this.Contratos = new HashSet<Contratos>();
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Descripcion in the schema.
        /// </summary>
        public virtual string Descripcion
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Abreviatura in the schema.
        /// </summary>
        public virtual string Abreviatura
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for LetraFactura in the schema.
        /// </summary>
        public virtual string LetraFactura
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ExigeCUIT in the schema.
        /// </summary>
        public virtual System.Nullable<bool> ExigeCUIT
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Activo in the schema.
        /// </summary>
        public virtual System.Nullable<bool> Activo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Contratos in the schema.
        /// </summary>
        public virtual ISet<Contratos> Contratos
        {
            get;
            set;
        }
    }

}
