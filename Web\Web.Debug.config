<?xml version="1.0" encoding="utf-8"?>

<!-- Para obtener más información sobre cómo usar la transformación de web.config, visite https://go.microsoft.com/fwlink/?LinkId=125889 -->

<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
  <!--
    En el ejemplo siguiente, la transformación "SetAttributes" cambiará el valor de    "connectionString" para que solamente use "ReleaseSQLServer" cuando el localizador "Match"    encuentre un atributo "name" con el valor "MyDB".    <connectionStrings>     <add name="MyDB".        connectionString="Data Source=ReleaseSQLServer;Initial Catalog=MyReleaseDB;Integrated Security=True"        xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>    </connectionStrings>
  -->
  <system.web>
    <!--
      En el ejemplo siguiente, la transformación "Replace" reemplazará toda la 
      sección <customErrors> del archivo web.config.
      Tenga en cuenta que, como solo hay una sección customErrors bajo el nodo 
      <system.web>, no es necesario usar el atributo "xdt:Locator".
      
      <customErrors defaultRedirect="GenericError.htm"
        mode="RemoteOnly" xdt:Transform="Replace">
        <error statusCode="500" redirect="InternalError.htm"/>
      </customErrors>
    -->
  </system.web>

	<devExpress>
		<bootstrap mode="Bootstrap4" />
	</devExpress>
</configuration>