﻿using Business.Provincias;
using Business.Services.Epas;
using Business.Services.Provincias;
using Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web.EPAS.AddEdit
{
    public partial class AddEditMetodosdeMedicion : System.Web.UI.Page
    {
        protected void Page_Load(object sender,EventArgs e)
        {
            try
            {

                Txtcodigo.ClientEnabled = true;
                TxtID.Visible = false;
                btnVolverEditado.Visible = false;
                if (!IsPostBack)
                {
                    if (Request.QueryString["id"] != null)
                    {

                        int idme = Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["id"].ToString()));
                        TxtID.Text = idme.ToString();
                        btnVolverEditado.Visible = false;
                        CargarMetodosdeMedicion(idme);

                        Txtcodigo.ClientEnabled = false;

                    }
                    else
                    {
                        TxtID.Text = "0";
                        CB_Activo.Checked = true;
                        btnVolverEditado.Visible = false;
                    }
                }
            }

            catch
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1",
                    "showMessage('Error al cargar la página, consulte al administrador del sistema', 'error');", true);
            }

        }

        private void CargarMetodosdeMedicion(int idme)
        {

            try
            {
                Domain.MetodosdeMedicion me = MetodosdeMedicionService.GetById(idme);

                if (me != null)
                {
                    Txtcodigo.Text = me.Codigo.ToString();
                    Txtdescripcion.Text = me.Descripcion.ToString();
                    CB_Activo.Checked = Convert.ToBoolean(me.Activo);
                }

            }
            catch (Exception ex)
            {
            }

        }

        protected void btnGrabar_Click(object sender,EventArgs e)
        {
            try
            {
                MetodosdeMedicion Z;
                //Verifica que se haya ingresado un codigo y nombre

                if (!string.IsNullOrEmpty(Txtcodigo.Text) && !string.IsNullOrEmpty(Txtdescripcion.Text))
                {
                    if (Request.QueryString["id"] != null)
                    {
                        // Se está editando un departamento existente.

                        int MId = Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["id"]));
                        //Obtengo el objeto departamento                        
                        Z = MetodosdeMedicionService.GetById(MId);
                        if (Z != null)
                        {
                            //El código si es edición, no se puede modificar, sólo actualizo descripción y activo y provincia
                            if (Z.Codigo.ToUpper().Equals(Txtcodigo.Text.Trim().ToUpper()))
                            {
                                Z.Descripcion = Txtdescripcion.Text.Trim().ToUpper();
                                Z.Activo = CB_Activo.Checked;

                                MetodosdeMedicionService.SaveOrUpdate(Z);

                                ScriptManager.RegisterStartupScript(this, this.GetType(), "message3", "showMessage('Fue editado correctamente.', 'success');", true);

                                Txtcodigo.ClientEnabled = false;

                                Response.Redirect(Global.ApplicationPath + "/EPAS/Lista/MetodosdeMedicion.aspx", false);
                                Response.End();
                            }
                        }
                        //  ScriptManager.RegisterStartupScript(this, this.GetType(), "message1", "showMessage('El departamento ya existe en la base de datos.', 'error');", true);                                             
                    }
                    else
                    {
                        // Verificar si ya existe en la lista local, solo se verifica el código
                        if (MetodosdeMedicionService.ExisteCodigo(Txtcodigo.Text.Trim()) || MetodosdeMedicionService.ExisteNombre(Txtdescripcion.Text.Trim()))
                        {
                            ScriptManager.RegisterStartupScript(this, this.GetType(), "message2", "showMessage('Ya existe un Metodos de Mediciòn con ese código o nombre, no se puede guardar.', 'error');", true);
                        }
                        else
                        {
                            //No existe el código, puedo guardar en la base
                            Z = new MetodosdeMedicion();

                            Z.Codigo = Txtcodigo.Text.Trim().ToUpper();
                            Z.Descripcion = Txtdescripcion.Text.Trim().ToUpper();
                            Z.Activo = CB_Activo.Checked;

                            MetodosdeMedicionService.SaveOrUpdate(Z);

                            ScriptManager.RegisterStartupScript(this, this.GetType(), "message3", "showMessage('Agregado correctamente.', 'success');", true);

                            Txtcodigo.ClientEnabled = false;

                            Response.Redirect(Global.ApplicationPath + "/EPAS/Lista/MetodosdeMedicion.aspx", false);
                            Response.End();
                        }

                    }



                }
                else
                {
                    ScriptManager.RegisterStartupScript(this, this.GetType(), "message4", "showMessage('Busque un Metodos de Mediciòn.', 'error');", true);
                }
            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "message5", "showMessage('Error al agregar un Metodos de Mediciòn , intente de nuevo.', 'error');", true);
            }


        }
    }
}