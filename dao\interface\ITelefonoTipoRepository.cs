//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 3/11/2023 09:44:13
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Collections.Generic;

namespace Domain
{
    public partial interface ITelefonoTipoRepository : IRepository<TelefonoTipo>
    {
        ICollection<TelefonoTipo> GetAll();
        TelefonoTipo GetByKey(int _Id);
    }
}
