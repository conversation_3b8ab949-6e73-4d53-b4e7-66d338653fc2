﻿using DAO;
using Domain;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Usuarios
{
   //public class TipoPermisoService
   // {

   //     public static IList<TipoPermiso> GetAll()
   //     {
   //         IList<TipoPermiso> listaAux;
   //         using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
   //         {
   //             listaAux = new TipoPermisoRepository(sess).GetAll();
   //             sess.Close();
   //             sess.Dispose();
   //         }

   //         return (listaAux);
   //     }

   //     public static TipoPermiso getById(int id)
   //     {
   //         TipoPermiso u;
   //         using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
   //         {
   //             u = new TipoPermisoRepository(sess).GetByKey(id);

   //             if (u != null)
   //             {
   //                 NHibernateUtil.Initialize(u.ModuloControls);
                
   //             }
   //             sess.Close();
   //             sess.Dispose();
   //             return u;
   //         }
   //     }

   //     public static TipoPermiso getByCodigo(string codigo)
   //     {
   //         TipoPermiso u;
   //         using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
   //         {
   //             u = new TipoPermisoRepository(sess).GetByCodigo(codigo);

   //             if (u != null)
   //             {
   //                 NHibernateUtil.Initialize(u.ModuloControls);

   //             }
   //             sess.Close();
   //             sess.Dispose();
   //             return u;
   //         }
   //     }
   // }
}
