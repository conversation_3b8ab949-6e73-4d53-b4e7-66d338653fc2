//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 3/11/2023 09:44:13
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class IVACondicionesRepository : NHibernateRepository<Domain.IVACondiciones>, IIVACondicionesRepository
    {
        public IVACondicionesRepository(ISession session) : base(session)
        {
        }

        public virtual ICollection<Domain.IVACondiciones> GetAll()
        {
            return session.CreateQuery(string.Format("from IVACondiciones")).List<Domain.IVACondiciones>();
        }

        public virtual Domain.IVACondiciones GetByKey(int _Id)
        {
            return session.Get<Domain.IVACondiciones>(_Id);
        }
        public virtual ICollection<Domain.IVACondiciones> GetByCondicion(bool Activo)
        {
            string hql = "from IVACondiciones  WHERE Activo = :Activo order BY Descripcion asc";

            IQuery q = session.CreateQuery(hql);
            q.SetParameter("Activo", Activo);
            return q.List<Domain.IVACondiciones>();

        }

    

        public virtual Domain.IVACondiciones GetByPorAbreviatura(string abreviatura)
        {
            string hql = "from IVACondiciones  WHERE Abreviatura = :Abreviatura";

            IQuery q = session.CreateQuery(hql);
            q.SetParameter("Abreviatura", abreviatura);
            return q.UniqueResult<Domain.IVACondiciones>();

        }


    }
}
