﻿<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="Sectores" table="Sectores">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Codigo" type="String">
      <column name="Codigo" not-null="false" length="10" sql-type="nvarchar" />
    </property>
    <property name="Descripcion" type="String">
      <column name="Descripcion" not-null="false" length="50" sql-type="nvarchar" />
    </property>
    <property name="Texto" type="String">
      <column name="Texto" not-null="false" sql-type="nvarchar" />
    </property>
  </class>
</hibernate-mapping>