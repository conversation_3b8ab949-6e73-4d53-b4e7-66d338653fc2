﻿using DAO;
using Domain;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Usuarios
{
   public class RolModuloService
    {

        public static RolModulo getById(int id)
        {
            RolModulo u;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                u = new RolModuloRepository(sess).GetByKey(id);

                if (u != null)
                {
                    NHibernateUtil.Initialize(u.Modulo);
                    NHibernateUtil.Initialize(u.ModuloControls);
                }
                sess.Close();
                sess.Dispose();
                return u;
            }
        }
        public static IList<RolModulo> GetByRol(int idRol)
        {
            IList<RolModulo> lista;

            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                lista = new RolModuloRepository(sess).GetByRol(idRol);
                foreach (RolModulo ra in lista)
                {
                    NHibernateUtil.Initialize(ra.Modulo);
                }
                sess.Close();
                sess.Dispose();
                return lista;

            }
        }

        public static RolModulo getByRolAndModulo(int idRol,int idModulo)
        {
            RolModulo lista;

            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                lista = new RolModuloRepository(sess).GetByRolAndModulo(idRol,idModulo);
                if (lista != null)
                {
                    NHibernateUtil.Initialize(lista.Modulo);
                    NHibernateUtil.Initialize(lista.Rol);
                
                }
                sess.Close();
                sess.Dispose();
                return lista;

            }
        }

        public static IList<RolModulo> getByRol(int idRol,int idApp)
        {
            IList<RolModulo> lista;

            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                lista = new RolModuloRepository(sess).GetByRol(idRol);
                foreach (RolModulo ra in lista)
                {
                    NHibernateUtil.Initialize(ra.Modulo);
                    if (ra.Modulo.RolModulos != null)
                    {
                        
                        //foreach (ModuloControl mc in ra.ModuloControls)
                        //{
                        //    NHibernateUtil.Initialize(mc.TipoPermiso);
                        //}
                    }
                }
                sess.Close();
                sess.Dispose();
                return lista;

            }
        }

        public static void SaveOrUpdate(RolModulo u)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new RolModuloRepository(sess).Add(u);

                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }

            }
        }

        public static void Delete(RolModulo u)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new RolModuloRepository(sess).Remove(u);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }

            }
        }

    }
}
