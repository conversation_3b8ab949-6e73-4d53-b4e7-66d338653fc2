﻿using Business.Services.Caja;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Domain;
using System.ServiceModel.Channels;
using Business.Services.Contratos;
using Common;
using static Common.Struct.Estructuras;
using DevExpress.Web;
using CrystalDecisions.Web;
using DevExpress.XtraExport.Helpers;
using DevExpress.Web.Data;
using Business.Services.Usuarios;
using System.Xml;
using static DevExpress.Xpo.DB.DataStoreLongrunnersWatch;
using DevExpress.Web.Internal;
using DevExpress.Utils.Extensions;
using Business.Services.Moneda;
using DevExpress.Utils.MVVM;
using DevExpress.XtraRichEdit.Internal.PrintLayout;

namespace Web.InterfazCaja
{
    public partial class WebForm1 : System.Web.UI.Page
    {
        protected override void OnInit(EventArgs e)
        {
            base.OnInit(e);
            GrVCheques.DataBind();
            GrVTransf.DataBind();
            GrVRetencion.DataBind();
            GrVTarjeta.DataBind();


        }

        protected void Page_Load(object sender, EventArgs e)
        {

            if (!IsPostBack)
            {
                LlenadoDrop_Tarjeta();
                LlenadoDrop_Bancos();
                LlenadoDrop_Bancos_1();
                LlenadoDrop_Retenciones();

                if (Session["efectivo"] != null)
                {
                    Session.Remove("efectivo");
                }
                if (Session["ListaCheques"] != null)
                {
                    Session.Remove("ListaCheques");
                }
                if (Session["ListaTransferencias"] != null)
                {
                    Session.Remove("ListaTransferencias");
                }
                if (Session["ListaRetenciones"] != null)
                {
                    Session.Remove("ListaRetenciones");                    
                }
                if (Session["ListaCuponesTarjetas"] != null)
                {
                    Session.Remove("ListaCuponesTarjetas");
                }
            }

            Calcular_Total();


        }

        private void LlenadoDrop_Bancos()
        {
            Drop_Bancos.DataSource = TarjetaServices.GetByBuscarBancos(true, "B");
            Drop_Bancos.TextField = "Descripcion";
            Drop_Bancos.ValueField = "Id";
            Drop_Bancos.DataBind();
            Drop_Bancos.Items.Insert(0, new ListEditItem());

        }
        private void LlenadoDrop_Bancos_1()
        {
            Drop_Bancos_1.DataSource = TarjetaServices.GetByBuscarBancos(true, "B");
            Drop_Bancos_1.TextField = "Descripcion";
            Drop_Bancos_1.ValueField = "Id";
            Drop_Bancos_1.DataBind();
            Drop_Bancos_1.Items.Insert(0, new ListEditItem());

        }

        private void LlenadoDrop_Tarjeta()
        {

            Drop_Tarjeta.DataSource = TarjetaServices.GetByBuscarBancos(true, "T");
            Drop_Tarjeta.TextField = "Descripcion";
            Drop_Tarjeta.ValueField = "Id";
            Drop_Tarjeta.DataBind();
            Drop_Tarjeta.Items.Insert(0, new ListEditItem());

        }

        private void LlenadoDrop_Retenciones()
        {

            Drop_Retenciones.DataSource = RetencionesServices.GetAll();
            Drop_Retenciones.TextField = "Descripcion";
            Drop_Retenciones.ValueField = "Id";
            Drop_Retenciones.DataBind();
            Drop_Retenciones.Items.Insert(0, new ListEditItem());

        }


        protected void btnConfirmarPagoTotal_Click(object sender, EventArgs e) {

            Contratos contrato = ContratosServices.GetById(Convert.ToInt32(Drop_Clientes.SelectedItem.Value));
            Monedas monedas = MonedaServices.BuscarMoneda("$");
            IList<ComprobanteTipos> comprobanteTipos = ComprobanteTiposServices.BuscarTipoComprobante("RE");

            List<Domain.ComprobantesRetenciones> ListaRetenciones = new List<ComprobantesRetenciones>();
            List<Domain.CuponesTarjetas> ListaCuponesTarjetas = new List<CuponesTarjetas>();
            List<Domain.Transferencias> ListaTransferencias = new List<Transferencias>();
            Domain.Efectivo efectivo = new Domain.Efectivo();
            List<Domain.Cheques> ListaCheques = new List<Cheques>();

            try {

                Comprobantes comprobante = new Comprobantes();                

                comprobante.Anulado = 0;
                comprobante.Fecha = Convert.ToDateTime(DateTime.Now);
                comprobante.Importe = Convert.ToDecimal(sePagoTotal.Text);
                comprobante.Saldo = 0;
                comprobante.Letra = "X";
                comprobante.Prefijo = 0;
                comprobante.Numero = 0;
                comprobante.ValorMoneda = 0;
                comprobante.Contratos.Add(contrato);
                comprobante.InteresMensualAplicableRecargos = 0;
                comprobante.Eliminado= 0;
                comprobante.Contado = 0;
                comprobante.ValorMoneda= 0;
                comprobante.ComprobanteTipos = (ComprobanteTipos)comprobanteTipos[0];
                comprobante.Monedas = monedas;


                if(Session["efectivo"] != null)
                {
                    efectivo = (Efectivo) Session["efectivo"];

                }

                if (Session["ListaCheques"] != null)
                {
                    ListaCheques = (List<Domain.Cheques>) Session["ListaCheques"];

                }

                if(Session["ListaTransferencias"] != null)
                {
                    ListaTransferencias = (List<Domain.Transferencias>) Session["ListaTransferencias"];

                }

                if(Session["ListaRetenciones"] != null)
                {
                    ListaRetenciones = (List<Domain.ComprobantesRetenciones>) Session["ListaRetenciones"];

                }

                if(Session["ListaCuponesTarjetas"] != null)
                {
                    ListaCuponesTarjetas = (List<Domain.CuponesTarjetas>) Session["ListaCuponesTarjetas"];

                }


                Pagoservice.SaveOrUpdateComprobante(comprobante, ListaCheques, ListaTransferencias, efectivo, ListaRetenciones, ListaCuponesTarjetas);

                DeleteGrVRows();
                camposCliente();
                BlanquearCamposTotales();

                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('El pago se realizó con éxito!');", true);

                if(Session["efectivo"] != null) {
                    Session.Remove("efectivo");
                }
                if(Session["ListaCheques"] != null) {
                    Session.Remove("ListaCheques");
                }
                if(Session["ListaTransferencias"] != null) {
                    Session.Remove("ListaTransferencias");
                }
                if(Session["ListaRetenciones"] != null) {
                    Session.Remove("ListaRetenciones");
                }
                if(Session["ListaCuponesTarjetas"] != null) {
                    Session.Remove("ListaCuponesTarjetas");
                }

            } catch(Exception ex) {

                ScriptManager.RegisterStartupScript(this,this.GetType(),"keyError",$"alert('Error al guardar los datos: {ex.Message}');",true);
            }

        }
        private void Calcular_Total()
        {
            try
            {

                //Cheques
                decimal totalCh = 0;
                if (Session["ListaCheques"] != null)
                {
                    List<Domain.Cheques> lstCh = (List<Domain.Cheques>)Session["ListaCheques"];


                    foreach (Domain.Cheques ch in lstCh)
                    {
                        totalCh += Convert.ToDecimal(ch.ImporteCheque);
                    }

                    seTotalCheque.Text = totalCh.ToString();


                }

                //Retenciones
                decimal totalRet = 0;
                if (Session["ListaRetenciones"] != null)
                {
                    List<Domain.ComprobantesRetenciones> lstCRete = (List<Domain.ComprobantesRetenciones>)Session["ListaRetenciones"];

                    foreach (Domain.ComprobantesRetenciones re in lstCRete)
                    {
                        totalRet += Convert.ToDecimal(re.Importe);
                    }

                    seTotalReten.Text = totalRet.ToString();


                }

                //Tarjeta
                decimal totalTj = 0;
                if (Session["ListaCuponesTarjetas"] != null)
                {
                    List<Domain.CuponesTarjetas> lstTj = (List<Domain.CuponesTarjetas>)Session["ListaCuponesTarjetas"];

                    foreach (Domain.CuponesTarjetas tj in lstTj)
                    {
                        totalTj += Convert.ToDecimal(tj.Importe);
                    }

                    seTotalTarjeta.Text = totalTj.ToString();
                }


                //Transferencia
                decimal totalTr = 0;
                if (Session["ListaTransferencias"] != null)
                {
                    List<Domain.Transferencias> lstTr = (List<Domain.Transferencias>)Session["ListaTransferencias"];

                    foreach (Domain.Transferencias tr in lstTr)
                    {
                        totalTr += Convert.ToDecimal(tr.Importe);
                    }

                    seTotalTransfer.Text = totalTr.ToString();

                }

                //Efectivo
                decimal totalef = 0;
                if (Session["efectivo"] != null)
                {
                    Domain.Efectivo efectivo = (Domain.Efectivo)Session["efectivo"];

                    totalef += Convert.ToDecimal(efectivo.Importe);

                    seTotalEfectivo.Text = totalef.ToString();
                }


                sePagoTotal.Text = (totalCh + totalRet + totalTj + totalTr + totalef).ToString();




            }
            catch (Exception ex)
            {

            }
        }

        private void BlanquearCamposTotales()
        {
            seTotalEfectivo.Text = "";
            seTotalCheque.Text = "";
            seTotalTransfer.Text = "";
            seTotalReten.Text = "";
            seTotalTarjeta.Text = "";
            sePagoTotal.Text = "";
        }

        private void DeleteGrVRows() {

            int rowCheques = GrVCheques.VisibleRowCount;
            for (int i = rowCheques - 1; i >= 0; i--) {
                GrVCheques.DeleteRow(i);
            }

            int rowTransfer = GrVTransf.VisibleRowCount;
            for (int i = rowTransfer - 1; i >= 0; i--) {
                GrVTransf.DeleteRow(i);
            }

            int rowReten = GrVRetencion.VisibleRowCount;
            for (int i = rowReten - 1; i >= 0; i--) {
                GrVRetencion.DeleteRow(i);
            }

            int rowTarjeta = GrVTarjeta.VisibleRowCount;
            for (int i = rowTarjeta - 1; i >= 0; i--) {
                GrVTarjeta.DeleteRow(i);
            }
        }

        private void camposCliente() {
            cmbBuscar.Text = "";
            txtBarraBusqueda.Text = "";
            Drop_Clientes.Text = "";
        }

        protected void Btn_BuscarContrato_Click(object sender, EventArgs e)
        {
            IList<Domain.Contratos> lst = Business.Services.Contratos.ContratosServices.GetByCamposBusqueda(Convert.ToInt32(cmbBuscar.Value), txtBarraBusqueda.Text);

            if(lst != null && lst.Count == 1) {
                Drop_Clientes.DataSource = lst;
                Drop_Clientes.TextField = "Nombre";// Usar solo el nombre en el texto desplegable
                Drop_Clientes.ValueField = "Id"; // Usar el número de contrato como valor
                Drop_Clientes.DataBind();
                Drop_Clientes.SelectedIndex = 0;

            } else if(lst != null && lst.Count >= 2) {
                Drop_Clientes.DataSource = lst;
                Drop_Clientes.TextField = "Nombre";
                Drop_Clientes.ValueField = "Id";
                Drop_Clientes.DataBind();
                Drop_Clientes.Text = "Se encontraron " + lst.Count + " resultados. (Desplegar)";

            } else {
                // Limpiar el DropDown si no se encontraron contratos
                Drop_Clientes.DataSource = null;
                Drop_Clientes.DataBind();
                Drop_Clientes.Text = "";
                Drop_Clientes.Items.Clear();

                ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('No se encontraron resultados')",true);
            }
        }

        protected void Callback_Agregar_Callback(object sender, CallbackEventArgs e)
        {
            try
            {
                string mediosDePagos = e.Parameter.ToString();

                switch (mediosDePagos)
                {

                    case "efectivo":

                        #region CargaEfectivo

                        Domain.Efectivo efectivo = new Domain.Efectivo();
                            
                        efectivo.Importe = Convert.ToDecimal(seEfectivo.Text);

                        Session.Add("efectivo", efectivo);

                        #endregion

                        break;

                    case "cheque":
                        #region CargaCheque
                        
                        Domain.Cheques cheques = new Domain.Cheques();

                        List<Domain.Cheques> ListaCheques;

                        if(Session["ListaCheques"] != null) {
                            ListaCheques = (List<Domain.Cheques>) Session["ListaCheques"];

                        } else {
                            ListaCheques = new List<Domain.Cheques>();
                        }

                        string serieNro = txtSerie.Text.Trim().ToUpper() + "-" + txtNro.Text.Trim().ToUpper();

                        if(ListaCheques.Exists(p => p.SerieNro == serieNro)) {

                            ScriptManager.RegisterStartupScript(this,this.GetType(),"alert","alert('Ya existe un cheque con esos datos');",true);

                        } else {
                            cheques.SerieNro = serieNro.ToUpper();
                            cheques.Diferido = cboxDiferido.Checked;
                            cheques.FecEmision = Convert.ToDateTime(dateChequeFecha.Text);
                            cheques.FecDeposito = Convert.ToDateTime(dateChequeDeposito.Text);
                            cheques.ImporteCheque = Convert.ToDecimal(seImporteCheque.Text);

                            Calcular_Total();
                            ListaCheques.Add(cheques);
                            Session.Add("ListaCheques",ListaCheques);
                        }

                        #endregion

                        break;

                    case "transferencia":
                        #region CargaTransferencia
                        
                        Domain.Transferencias transferencias = new Domain.Transferencias();

                        List<Domain.Transferencias> ListaTransferencias;

                        if (Session["ListaTransferencias"] != null)
                        {
                            ListaTransferencias = (List<Domain.Transferencias>)Session["ListaTransferencias"];

                        }
                        else
                        {
                            ListaTransferencias = new List<Domain.Transferencias>();
                        }

                        string nroTransferencia = seTransferNumero.Text;

                        if(ListaTransferencias.Exists(p => p.NroTransferencia == nroTransferencia)) {

                            return;

                        } else {

                            transferencias.Fecha = Convert.ToDateTime(dateTransferFecha.Text);
                            transferencias.NroTransferencia = seTransferNumero.Text;
                            transferencias.Importe = Convert.ToDecimal(seTransferImporte.Text);
                            Calcular_Total();
                            ListaTransferencias.Add(transferencias);
                            Session.Add("ListaTransferencias",ListaTransferencias);
                        }

                        #endregion

                        break;

                    case "retencion":
                        #region CargaRetenciones
                        
                        Domain.ComprobantesRetenciones comprobantesRetenciones = new Domain.ComprobantesRetenciones();
                        
                        List<Domain.ComprobantesRetenciones> ListaRetenciones;
                        if (Session["ListaRetenciones"] != null)
                        {
                            ListaRetenciones = (List<Domain.ComprobantesRetenciones>)Session["ListaRetenciones"];

                        }
                        else
                        {
                            ListaRetenciones = new List<Domain.ComprobantesRetenciones>();

                        }

                        int numero = Convert.ToInt32(seNumeroReten.Text);

                        if (ListaRetenciones.Exists(p => p.Numero == numero))
                        {
                            ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('Se guardaron correctamente!!');",true);
                        }
                        else
                        {

                            comprobantesRetenciones.Importe = Convert.ToDecimal(seRetenImporte.Text);
                            comprobantesRetenciones.Fecha = Convert.ToDateTime(dateRetenFecha.Text);
                            comprobantesRetenciones.Numero = Convert.ToInt32(seNumeroReten.Text);
                            Calcular_Total();
                            ListaRetenciones.Add(comprobantesRetenciones);
                            Session.Add("ListaRetenciones", ListaRetenciones);
                        }

                        
                        #endregion


                        break;

                    case "tarjeta":
                        #region CargaTarjeta
                        
                        Domain.CuponesTarjetas cuponesTarjetas = new Domain.CuponesTarjetas();
                        
                        List<Domain.CuponesTarjetas> ListaCuponesTarjetas;
                        if (Session["ListaCuponesTarjetas"] != null)
                        {
                            ListaCuponesTarjetas = (List<Domain.CuponesTarjetas>)Session["ListaCuponesTarjetas"];

                        }
                        else
                        {
                            ListaCuponesTarjetas = new List<Domain.CuponesTarjetas>();
                        }
                        int codCupon = Convert.ToInt32(txtNroCupon.Text);

                        if (ListaCuponesTarjetas.Exists(p => p.CodCupon == codCupon))
                        {
                            ScriptManager.RegisterStartupScript(this, this.GetType(), "mensajeExistenteTJ", $"Swal.fire('{codCupon}', 'Ya fue cargado!', 'warning');", true);
                                   
                        }
                        else
                        {
                            // Agregar código para asignar valores a cuponesTarjetas
                            cuponesTarjetas.CodCupon = Convert.ToInt32(txtNroCupon.Text);
                            cuponesTarjetas.CantCuotas = Convert.ToInt32(seCuotas.Text);
                            cuponesTarjetas.CodAutorizacion = txtNroAutorizacion.Text;
                            cuponesTarjetas.Importe = Convert.ToDecimal(seImporteCupon.Text);

                            Calcular_Total();
                            ListaCuponesTarjetas.Add(cuponesTarjetas);
                            Session.Add("ListaCuponesTarjetas", ListaCuponesTarjetas);
                        }

                        
                        #endregion

                        break;
                }

            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al reiniciar la ventana', 'error');", true);

            }
        }

        protected void GrVCheques_DataBinding(object sender,EventArgs e)
        {
            if(Session["ListaCheques"] != null)
            {
                GrVCheques.DataSource = (List<Domain.Cheques>) Session["ListaCheques"];

            }
        }
        protected void GrVTransf_DataBinding(object sender, EventArgs e)
        {
            if (Session["ListaTransferencias"] != null)
            {
                GrVTransf.DataSource = (List<Domain.Transferencias>)Session["ListaTransferencias"];

            }
        }
        protected void GrVRetencion_DataBinding(object sender, EventArgs e)
        {
            if (Session["ListaRetenciones"] != null)
            {
                GrVRetencion.DataSource = (List<Domain.ComprobantesRetenciones>)Session["ListaRetenciones"];

            }
        }

        protected void GrVTarjeta_DataBinding(object sender, EventArgs e)
        {
            if (Session["ListaCuponesTarjetas"] != null)
            {
                GrVTarjeta.DataSource = (List<Domain.CuponesTarjetas>)Session["ListaCuponesTarjetas"];

            }
        }



        protected void GrVCheques_RowDeleting(object sender, DevExpress.Web.Data.ASPxDataDeletingEventArgs e)
        {
            try
            {
                int i = GrVCheques.FindVisibleIndexByKeyValue(e.Keys[GrVCheques.KeyFieldName]);
                
                e.Cancel = true;
                string serieNro = (e.Keys[GrVCheques.KeyFieldName]).ToString();
                
                List<Domain.Cheques> ListaCheques;
                if (Session["ListaCheques"] != null)
                {
                    ListaCheques = (List<Domain.Cheques>)Session["ListaCheques"];
                }
                else
                {
                    ListaCheques = new List<Domain.Cheques>();

                }
                decimal totalCh = 0;
                foreach (Domain.Cheques ch in ListaCheques)
                {
                    totalCh += Convert.ToDecimal(ch.ImporteCheque);
                }
                Cheques che = ListaCheques.Find(q => q.SerieNro == serieNro.ToString());
                if (che != null)
                {
                    // Realizar la resta del importe del cheque eliminado
                    decimal importeChequeEliminado = Convert.ToDecimal(che.ImporteCheque);
                    totalCh -= importeChequeEliminado;

                    // Luego, proceder a eliminar el cheque de la lista y actualizar el Session
                    ListaCheques.Remove(che);
                    Session["ListaCheques"] = ListaCheques;
                    
                }
                // Actualizar el campo txtTotalCheque con el nuevo total calculado
                seTotalCheque.Text = totalCh.ToString();

                // Resto del código para actualizar los otros campos de total si es necesario

                Calcular_Total();
                //BlanquearCamposCheques();

            }
            catch(Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al reiniciar la ventana', 'error');", true);
            }

        }

        protected void GrVTransf_RowDeleting(object sender, DevExpress.Web.Data.ASPxDataDeletingEventArgs e)
        {
            try
            { 
                int i = GrVTransf.FindVisibleIndexByKeyValue(e.Keys[GrVTransf.KeyFieldName]);
                
                e.Cancel = true;
                string nroTransferencia = (e.Keys[GrVTransf.KeyFieldName]).ToString();

                List<Domain.Transferencias> ListaTransferencias;
                if (Session["ListaTransferencias"] != null)
                {
                    ListaTransferencias = (List<Domain.Transferencias>)Session["ListaTransferencias"];
                    Calcular_Total();
                    
                }
                else
                {
                    ListaTransferencias = new List<Domain.Transferencias>();
                    Calcular_Total();
                    

                }
                Transferencias TR= ListaTransferencias.Find(q => q.NroTransferencia == nroTransferencia.ToString());
                if (TR != null)
                {
                    ListaTransferencias.Remove(TR);
                    Session["ListaTransferencias"] = ListaTransferencias;
                    //Calcular_Total();
                    //BlanquearCamposTransfer();
                }
               
                

            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al reiniciar la ventana', 'error');", true);
            }

        }

        protected void GrVRetencion_RowDeleting(object sender, DevExpress.Web.Data.ASPxDataDeletingEventArgs e)
        {
            try
            {
                int i = GrVRetencion.FindVisibleIndexByKeyValue(e.Keys[GrVRetencion.KeyFieldName]);

                e.Cancel = true;
                string numero = (e.Keys[GrVRetencion.KeyFieldName]).ToString();

                List<Domain.ComprobantesRetenciones> ListaRetenciones;
                if (Session["ListaRetenciones"] != null)
                {
                    ListaRetenciones = (List<Domain.ComprobantesRetenciones>)Session["ListaRetenciones"];
                }
                else
                {
                    ListaRetenciones = new List<Domain.ComprobantesRetenciones>();

                }

                ComprobantesRetenciones R = ListaRetenciones.Find(q => q.Numero == Convert.ToInt32(numero));
                if (R != null)
                {
                    ListaRetenciones.Remove(R);
                    Session["ListaRetenciones"] = ListaRetenciones;
                }
                Calcular_Total();

            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al reiniciar la ventana', 'error');", true);
            }

        }
        protected void GrVTarjeta_RowDeleting(object sender, DevExpress.Web.Data.ASPxDataDeletingEventArgs e)
        {
            try
            {
                int i = GrVTarjeta.FindVisibleIndexByKeyValue(e.Keys[GrVTarjeta.KeyFieldName]);

                e.Cancel = true;
                string numero = (e.Keys[GrVTarjeta.KeyFieldName]).ToString();

                List<Domain.CuponesTarjetas> ListaCuponesTarjetas;
                if (Session["ListaCuponesTarjetas"] != null)
                {
                    ListaCuponesTarjetas = (List<Domain.CuponesTarjetas>)Session["ListaCuponesTarjetas"];
                }
                else
                {
                    ListaCuponesTarjetas = new List<Domain.CuponesTarjetas>();

                }
                decimal totaltj = 0;
                foreach (Domain.CuponesTarjetas tj in ListaCuponesTarjetas)
                {
                    totaltj += Convert.ToDecimal(tj.Importe);
                }
                CuponesTarjetas T = ListaCuponesTarjetas.Find(q => q.CodCupon == Convert.ToInt32(numero));
                if (T != null)
                {                        
                        decimal importeCuponEliminado = Convert.ToDecimal(T.Importe);
                         totaltj -= importeCuponEliminado;

                    // Luego, proceder a eliminar el cheque de la lista y actualizar el Session
                    ListaCuponesTarjetas.Remove(T);
                    Session["ListaCuponesTarjetas"] = ListaCuponesTarjetas;
                }
                // Actualizar el campo txtTotalCheque con el nuevo total calculado
                seTotalTarjeta.Text = totaltj.ToString();
                    
                Calcular_Total();
                //BlanquearCamposReten();

            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al reiniciar la ventana', 'error');", true);
            }

        }

        
    }

}
