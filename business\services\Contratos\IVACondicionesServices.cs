﻿using Common.DDL;
using DAO;
using Domain;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Contratos {
    public class IVACondicionesServices {

        public static IList<IVACondiciones> GetAll() {
            IList<IVACondiciones> listaAux;
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                listaAux = (IList<IVACondiciones>) new IVACondicionesRepository(sess).GetAll();
                sess.Close();
                sess.Dispose();
            }

            return (listaAux);
        }

        public static IVACondiciones PorAbreviatura(string abreviatura)
        {
            IVACondiciones I;

            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {

                I = (IVACondiciones)new IVACondicionesRepository(sess).GetByPorAbreviatura(abreviatura);

                if (I != null)
                {
                    NHibernateUtil.Initialize(I.Abreviatura);
                }

                sess.Close();
                sess.Dispose();
                return I;
            }

        }

        public static List<ItemCombo> GetByCondicion(bool Activo) {
            IList<IVACondiciones> listaAux;
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                listaAux = (IList<IVACondiciones>) new IVACondicionesRepository(sess).GetByCondicion(Activo);
                sess.Close();
                sess.Dispose();
            }

            ItemCombo item;
            ListaCombo lista = new ListaCombo();
            foreach(IVACondiciones p in listaAux) {

                item = new ItemCombo();
                item.ID = p.Id.ToString();
                item.Descripcion = p.Descripcion;
                lista.Add(item);
            }
            return (lista);
        }

        public static IVACondiciones getById(int id) {
            IVACondiciones u;
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                u = new IVACondicionesRepository(sess).GetByKey(id);

                if(u != null) {
                    //NHibernateUtil.Initialize();

                }
                sess.Close();
                sess.Dispose();
                return u;
            }
        }

        public static void Delete(Domain.IVACondiciones u) {
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                using(NHibernate.ITransaction tx = sess.BeginTransaction()) {
                    try {
                        new IVACondicionesRepository(sess).Remove(u);
                        tx.Commit();
                    } catch(Exception e) {
                        tx.Rollback();
                        throw e;
                    } finally {
                        sess.Close();
                        sess.Dispose();
                    }
                }

            }
        }

    }
}
