﻿<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="Modulo" table="Modulo">
    <id name="Id" type="Int32">
      <column name="id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Icon" type="String">
      <column name="icon" not-null="false" length="50" sql-type="nvarchar" />
    </property>
    <property name="NodoPadre" type="Int32">
      <column name="nodo_padre" not-null="true" precision="10" scale="0" sql-type="int" />
    </property>
    <property name="Nodo" type="Int32">
      <column name="nodo" not-null="true" precision="10" scale="0" sql-type="int" />
    </property>
    <property name="Descripcion" type="String">
      <column name="descripcion" not-null="true" sql-type="nvarchar" />
    </property>
    <property name="Url" type="String">
      <column name="url" not-null="true" sql-type="nvarchar" />
    </property>
    <property name="Orden" type="Int32">
      <column name="orden" not-null="false" precision="10" scale="0" sql-type="int" />
    </property>
    <set name="RolModulos" inverse="true" generic="true">
      <key>
        <column name="IdModulo" />
      </key>
      <one-to-many class="RolModulo" />
    </set>
  </class>
</hibernate-mapping>