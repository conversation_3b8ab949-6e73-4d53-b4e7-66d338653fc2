﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 11/03/2019 15:26:30
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class RolModuloRepository : NHibernateRepository<Domain.RolModulo>, IRolModuloRepository
    {
        public RolModuloRepository(ISession session) : base(session)
        {
        }

        public virtual IList<Domain.RolModulo> GetAll()
        {
            return session.CreateQuery(string.Format("from RolModulo")).List<Domain.RolModulo>();
        }

        public virtual Domain.RolModulo GetByKey(int _Id)
        {
            return session.Get<Domain.RolModulo>(_Id);
        }
        public virtual IList<Domain.RolModulo> GetByRol(int idRol)
        {
            string hql = "from RolModulo v where v.Rol.Id = :idRol";

            IQuery q = session.CreateQuery(hql);
            q.SetParameter("idRol", idRol);

            return q.List<Domain.RolModulo>();
        }

        public virtual Domain.RolModulo GetByRolAndModulo(int idRol, int idModulo)
        {
            string hql = "from RolModulo v where v.Rol.Id = :idRol and v.Modulo.Id=:idModulo";

            IQuery q = session.CreateQuery(hql);
            q.SetParameter("idRol", idRol);
            q.SetParameter("idModulo", idModulo);
            return q.UniqueResult<Domain.RolModulo>();
        }


        public virtual IList<Domain.RolModulo> GetByRol(int idRol,int idApp)
        {
            string hql = "from RolModulo v where v.Rol.Id = :idRol and v.Modulo.Aplicacion.Id=:idApp";

            IQuery q = session.CreateQuery(hql);
            q.SetParameter("idRol", idRol);
            q.SetParameter("idApp", idApp);
            return q.List<Domain.RolModulo>();
        }
    }
}
