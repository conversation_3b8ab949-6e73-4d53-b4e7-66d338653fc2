﻿using Common.DDL;
using DAO;
using Domain;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Epas
{
    public class ProtocoloEstadoService
    {
        //Trae todo
        public static IList<Domain.ProtocolosEstados> GetAll()
        {
            IList<Domain.ProtocolosEstados> listaAux;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                listaAux = (IList<Domain.ProtocolosEstados>)new ProtocolosEstadosRepository(sess).GetAll();
                sess.Close();
                sess.Dispose();
            }
            return (listaAux);

        }

        public static List<ItemCombo> GetAllCombo() {
            IList<Domain.ProtocolosEstados> listaAux;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                listaAux = (IList<Domain.ProtocolosEstados>)new ProtocolosEstadosRepository(sess).GetAll();
                sess.Close();
                sess.Dispose();
            }

            ItemCombo item;
            ListaCombo lista = new ListaCombo();
            foreach(ProtocolosEstados p in listaAux) {

                item = new ItemCombo();
                item.ID = p.Codigo.ToString();
                item.Descripcion = p.Nombre;
                lista.Add(item);
            }
            return (lista);
        }

        //obtener por id
        public static Domain.ProtocolosEstados GetById(int id)
        {

            Domain.ProtocolosEstados a;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {

                a = (Domain.ProtocolosEstados)new ProtocolosEstadosRepository(sess).GetByKey(id);
                sess.Close();
                sess.Dispose();
                return a;
            }

        }


        /// <summary>
        /// Hay 3 estados:
        /// P=Pendiente
        /// ER=En Revision
        /// A=Aprobado
        /// </summary>
        /// <param name="codigo"></param>
        /// <returns></returns>
        public static Domain.ProtocolosEstados GetByCodigo(string codigo)
        {

            Domain.ProtocolosEstados PE;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {

                PE = (Domain.ProtocolosEstados)new ProtocolosEstadosRepository(sess).GetByCodigo(codigo);
                if(PE != null)
                {
                    NHibernateUtil.Initialize(PE.Protocolos);
                }

                sess.Close();
                sess.Dispose();
                return PE;

            }

        }



        //agregar y actualizar
        public static void SaveOrUpdate(Domain.ProtocolosEstados a)
        {

            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {

                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {

                    try
                    {

                        new ProtocolosEstadosRepository(sess).Add(a);

                        tx.Commit();
                    }
                    catch (Exception e)
                    {

                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {

                        sess.Close();
                        sess.Dispose();
                    }

                }

            }

        }

        //borrar
        public static void Delete(Domain.ProtocolosEstados a)
        {
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new ProtocolosEstadosRepository(sess).Remove(a);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }

                }

            }

        }

    }
}
