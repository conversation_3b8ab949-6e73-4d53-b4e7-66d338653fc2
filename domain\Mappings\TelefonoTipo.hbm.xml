<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="TelefonoTipo" table="TelefonoTipo">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Tipo" type="String">
      <column name="Tipo" not-null="false" length="20" sql-type="nvarchar" />
    </property>
    <property name="Observacion" type="String">
      <column name="Observacion" not-null="false" length="255" sql-type="nvarchar" />
    </property>
    <set name="ContratoContactosTels" inverse="true" cascade="delete" generic="true">
      <key>
        <column name="IdTelefonoTipo" />
      </key>
      <one-to-many class="ContratoContactosTel" />
    </set>
    <set name="Contratos_IdTelefonoTipo" inverse="true" generic="true">
      <key>
        <column name="IdTelefonoTipo" />
      </key>
      <one-to-many class="Contratos" />
    </set>
    <set name="Contratos_IdTelefonoTipoAlter" inverse="true" generic="true">
      <key>
        <column name="IdTelefonoTipoAlter" />
      </key>
      <one-to-many class="Contratos" />
    </set>
  </class>
</hibernate-mapping>