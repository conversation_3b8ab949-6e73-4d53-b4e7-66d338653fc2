﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 11/03/2019 14:47:12
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class ModuloControlRepository : NHibernateRepository<Domain.ModuloControl>, IModuloControlRepository
    {
        public ModuloControlRepository(ISession session) : base(session)
        {
        }

        public virtual IList<Domain.ModuloControl> GetAll()
        {
            return session.CreateQuery(string.Format("from ModuloControl")).List<Domain.ModuloControl>();
        }

        public virtual IList<Domain.ModuloControl> GetAllByModulo(int idModulo)
        {
            return session.CreateQuery(string.Format("from ModuloControl Where RolModulo.Modulo.Id=:idModulo")).SetParameter("idModulo", idModulo).List<Domain.ModuloControl>();
        }

        public virtual IList<Domain.ModuloControl> GetAllByIdRolModulo(int idRolModulo)
        {
            return session.CreateQuery(string.Format("from ModuloControl Where RolModulo.Id=:idRolModulo")).SetParameter("idRolModulo", idRolModulo).List<Domain.ModuloControl>();
        }

        public virtual Domain.ModuloControl GetByKey(int _Id)
        {
            return session.Get<Domain.ModuloControl>(_Id);
        }
    }
}
