﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" MaintainScrollPositionOnPostback="true" AutoEventWireup="true" CodeBehind="AddEditFuentes.aspx.cs" Inherits="Web.EPAS.AddEdit.AddEditFuentes" %>

<%@ Register Assembly="DevExpress.Web.v18.1" Namespace="DevExpress.Web" TagPrefix="dx" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">

     <script type="text/javascript">

         function volverButton() {

             if (confirm) {
                 Swal.fire({
                     text: "No se guardaron los cambios. Desea volver de todos modos?",
                     showCancelButton: true,
                     confirmButtonText: "Volver"
                 }).then((result) => {
                     if (result.isConfirmed) {

                         window.location.href = "/SIGeLab/EPAS/Lista/Fuentes.aspx";

                     }
                 });
             }
         };

         function volverButtonEditado() {

             if (confirm) {

                 window.location.href = "/SIGeLab/EPAS/Lista/Fuentes.aspx";

             }
         };

         function OnLocalidadChanged(l) {

             getLocalidad = l.GetValue().toString();
             LoadingPanel.Hide();

         };

         function mostrarMensaje() {
             var lblMsg = document.getElementById('<%= lblMsg.ClientID %>');

             if (lblMsg) {
                 lblMsg.style.display = 'block';  // Hace visible el label
             }
         };

         function OnTiposFuentesChanged(TF) {

             getTipodeFuente = TF.GetValue().toString();
             LoadingPanel.Hide();

         };

         function UpdateGridHeight() {
             gv_datos.SetHeight(0);
             var containerHeight = ASPxClientUtils.GetDocumentClientHeight();
             if (document.body.scrollHeight > containerHeight)
                 containerHeight = document.body.scrollHeight;
             gv_datos.SetHeight(containerHeight);
         };

         window.addEventListener('resize', function (evt) {
             if (ASPxClientUtils && !ASPxClientUtils.androidPlatform)
                 return;
             var activeElement = document.activeElement;
             if (activeElement && (activeElement.tagName === "INPUT" || activeElement.tagName === "TEXTAREA") && activeElement.scrollIntoViewIfNeeded)
                 window.setTimeout(function () { activeElement.scrollIntoViewIfNeeded(); }, 0);
         });

     </script>

</asp:Content>


<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

    <div class="page-header page-header-light shadow">
        <div class="page-header-content d-lg-flex border-top">
            <div class="d-flex">
                <div class="breadcrumb py-2">
                    <a href="<%= Web.Util.Helpers.GetApplicationPath() %>/Inicio.aspx" class="breadcrumb-item"><i class="icon-home2 mr-2"></i>Inicio</a>
                    <a href="<%= Web.Util.Helpers.GetApplicationPath() %>/EPAS/Lista/Fuentes.aspx" class="breadcrumb-item"><i class="icon-display me-2"></i>Mantenimiento</a>
                    <a href="<%= Web.Util.Helpers.GetApplicationPath() %>/EPAS/Lista/Fuentes.aspx" class="breadcrumb-item">Fuentes</a>
                    <span class="breadcrumb-item active">
                        <asp:Literal ID="ltr_nombreBreadcrumb" runat="server">Agregar/Editar Fuentes</asp:Literal>
                    </span>
                </div>
            </div>
        </div>
    </div>

    <div class="row gx-1">

        <div class="col-lg-12">
            <div class="navbar navbar-expand-xl bg-secondary navbar-dark rounded-top mt-1">
                <div class="container-fluid">                           
                    <div class="nav-item dropdown">
                        <h5 class="mt-2 mb-1 fs-5" data-bs-toggle="tab">Fuentes - Mantenimiento</h5>
                    </div>                        
                </div>
            </div>

            <!-- Card Cliente -->

            <div class="tab-content border border-1 rounded-bottom p-1">

                <div class="card">
                    <div class="row gx-0">
                        <dx:ASPxTextBox ID ="TxtID" Visible="false" CssClass="form-control altura-textbox" AutoCompleteType="Disabled"  runat="server">
                        </dx:ASPxTextBox>
                        
                        <div class="card-body col-lg-6 mt-3">
                            <div action="#">

                                <div class="row mb-1">
                                    <label class="col-lg-2 col-form-label pe-0 fw-semibold">N° de Fuente</label>
                                    <div class="col-lg-3 p-0">
                                        <dx:ASPxTextBox ID="txtNumerodefuente" CssClass="form-control altura-textbox" AutoCompleteType="Disabled" runat="server">
                                        </dx:ASPxTextBox>
                                    </div>
                                </div>

                                <div class="row mb-1">
                                    <label class="col-lg-2 col-form-label pe-0 fw-semibold">Descripción</label>
                                    <div class="col-lg-7 p-0">
                                        <dx:ASPxTextBox ID="txtDescripcion" ClientInstanceName="txtDescripcion" CssClass="form-control altura-textbox" AutoCompleteType="Disabled" runat="server">
                                        </dx:ASPxTextBox>
                                    </div>
                                </div>

                                <div class="row mb-1">
                                    <label class="col-lg-2 col-form-label pe-0 fw-semibold">Nota</label>
                                    <div class="col-lg-7 p-0">
                                        <dx:ASPxTextBox ID="txtNota" ClientInstanceName="txtNota" CssClass="form-control altura-textbox" AutoCompleteType="Disabled" runat="server">
                                        </dx:ASPxTextBox>
                                    </div>
                                </div>

                                <div class="row mb-1">
                                    <label class="col-lg-2 col-form-label pe-0 fw-semibold">Localidad</label>
                                    <div class="col-lg-3 p-0">
                                        <dx:ASPxComboBox ID="CmbLocalidades" ClientInstanceName="CmbLocalidades" runat="server" DropDownStyle="DropDownList" NullText="Seleccionar Localidad" TextFormatString="{0} ({1})" Width="100%" CssClass="form-control altura-textbox" IncrementalFilteringMode="Contains" TextField="NombreZonas" ValueField="NombreLocalidad" EnableSynchronization="False">
                                            <ValidationSettings ValidationGroup="confirmarGeneral" Display="Dynamic" ErrorTextPosition="Bottom" ErrorDisplayMode="ImageWithText" SetFocusOnError="true">
                                                <RequiredField IsRequired="true" ErrorText="Campo Obligatorio." />
                                            </ValidationSettings>
                                            <ClientSideEvents SelectedIndexChanged="function(s, e) { OnLocalidadChanged(s); }" />
                                        </dx:ASPxComboBox>
                                    </div>
                                </div>

                                <div class="row mb-1">
                                    <label class="col-lg-2 col-form-label pe-0 fw-semibold">Tipo de Fuente</label>
                                    <div class="col-lg-3 p-0">
                                        <dx:ASPxComboBox ID="CmbTipodeFuente" ClientInstanceName="CmbTipodeFuente" runat="server" DropDownStyle="DropDownList" Width="100%" CssClass="form-control altura-textbox" IncrementalFilteringMode="Contains" TextField="NombreFuente" ValueField="NombreFuente" EnableSynchronization="False">
                                            <ValidationSettings ValidationGroup="confirmarGeneral" Display="Dynamic" ErrorTextPosition="Bottom" ErrorDisplayMode="ImageWithText" SetFocusOnError="true">
                                                <RequiredField IsRequired="true" ErrorText="Campo Obligatorio." />
                                            </ValidationSettings>
                                            <ClientSideEvents SelectedIndexChanged="function(s, e) { OnTiposFuentesChanged(s); }" />
                                        </dx:ASPxComboBox>
                                    </div>
                                </div>

                                <div class="row mb-1">
                                    <label class="col-lg-2 col-form-label pe-0 fw-semibold btn-buscar">Activo</label>
                                    <div class="col-lg-10 p-0">
                                        <dx:ASPxCheckBox ID="CB_Activo" runat="server" ToggleSwitchDisplayMode="Always">
                                        </dx:ASPxCheckBox>
                                        <dx:ASPxLabel ID="txtActivo" runat="server" CssClass="text-danger"></dx:ASPxLabel>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <%--Mapa de Google Maps--%>

                        <div class="card-body col-lg-3 pt-0">
                            <div action="#">

                                <h5 class="my-2">Mapa de <span class="text-decoration-underline">Fuentes</span>
                                </h5>

                                <div id="map" class="w-90 border border-secondary rounded-2 border-width-2" style="height: 320px;">
                                </div>
                                <%--<p class="text-body-secondary text-center">Ctrl + rueda de desplazamiento acerca/aleja
                                    el mapa.<br />
                                    Doble clic permite fijar un punto o dirección específica.</p>--%>
                            </div>
                        </div>

                        <div class="card-body col-lg-1">
                            <div action="#">
                                <div class="row mt-5">
                                    <div class="col-lg-12 p-0">
                                        <dx:ASPxTextBox ID="txtLat" ClientInstanceName="txtLat" CssClass="form-control altura-textbox" ReadOnly="true" NullText="Lat." AutoCompleteType="Disabled" runat="server">
                                        </dx:ASPxTextBox>
                                    </div>
                                </div>
                                <div class="row mt-1">
                                    <div class="col-lg-12 p-0">
                                        <dx:ASPxTextBox ID="txtLng" ClientInstanceName="txtLng" CssClass="form-control altura-textbox" ReadOnly="true" NullText="Lon." AutoCompleteType="Disabled" runat="server">
                                        </dx:ASPxTextBox>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <dx:ASPxTextBox ID ="ASPxTextBox1" Visible="false" CssClass="form-cpntrol altura-textbox" AutoCompleteType="Disabled"  runat="server"></dx:ASPxTextBox>

                        <div class="card-body col-lg-6">
                            <div action="#">

                                <fieldset><legend class="col-lg-12 border-secondary border-bottom border-width-1"></legend>
                                </fieldset>

                                <dx:ASPxFormLayout ID="Grip_SubFuentes" CssClass="mt-4" ClientInstanceName="FormLayout" runat="server" Theme="MaterialCompact" RequiredMarkDisplayMode="Auto" UseDefaultPaddings="false" AlignItemCaptionsInAllGroups="true" Width="100%" ColumnCount="1">
                                    <Paddings PaddingBottom="30" PaddingTop="10" />
                                    <Styles>
                                        <LayoutGroupBox CssClass="fullWidth fullHeight"></LayoutGroupBox>
                                        <LayoutGroup Cell-CssClass="fullHeight"></LayoutGroup>
                                    </Styles>
                                    <SettingsAdaptivity>
                                        <GridSettings WrapCaptionAtWidth="400">
                                            <Breakpoints>
                                                <dx:LayoutBreakpoint ColumnCount="1" MaxWidth="790" Name="S" />
                                            </Breakpoints>
                                        </GridSettings>
                                    </SettingsAdaptivity>
                                    <Items>
                                        <dx:LayoutGroup Caption="Sub Fuentes" GroupBoxDecoration="Box">
                                            <GridSettings WrapCaptionAtWidth="400"></GridSettings>
                                            <SpanRules>
                                                <dx:SpanRule BreakpointName="S" ColumnSpan="1" RowSpan="1" />
                                            </SpanRules>
                                            <Items>
                                                <dx:LayoutItem Caption="Código">
                                                    <LayoutItemNestedControlCollection>
                                                        <dx:LayoutItemNestedControlContainer>
                                                            <dx:ASPxTextBox ID="txtSubCodigo" runat="server" NullText="Código" Width="100%" CssClass="form-control altura-textbox">
                                                                <ValidationSettings Display="Dynamic" RequiredField-IsRequired="true" ErrorDisplayMode="Text" SetFocusOnError="true" ErrorTextPosition="Bottom" ErrorFrameStyle-Wrap="true" />
                                                            </dx:ASPxTextBox>
                                                        </dx:LayoutItemNestedControlContainer>
                                                    </LayoutItemNestedControlCollection>
                                                </dx:LayoutItem>

                                                <dx:LayoutItem Caption="Descripción">
                                                    <LayoutItemNestedControlCollection>
                                                        <dx:LayoutItemNestedControlContainer>
                                                            <dx:ASPxTextBox ID="txtSubDescripcion" runat="server" NullText="Nombre" Width="100%" CssClass="form-control altura-textbox">
                                                                <ValidationSettings Display="Dynamic" RequiredField-IsRequired="true" ErrorDisplayMode="Text" SetFocusOnError="true" ErrorTextPosition="Bottom" ErrorFrameStyle-Wrap="true" />
                                                            </dx:ASPxTextBox>
                                                        </dx:LayoutItemNestedControlContainer>
                                                    </LayoutItemNestedControlCollection>
                                                </dx:LayoutItem>

                                                <dx:LayoutItem Caption="Calle">
                                                    <LayoutItemNestedControlCollection>
                                                        <dx:LayoutItemNestedControlContainer>
                                                            <dx:ASPxTextBox ID="txtSubCalle" runat="server" NullText="Calle" Width="100%" CssClass="form-control altura-textbox">
                                                            </dx:ASPxTextBox>
                                                        </dx:LayoutItemNestedControlContainer>
                                                    </LayoutItemNestedControlCollection>
                                                </dx:LayoutItem>

                                                <dx:LayoutItem Caption="Altura">
                                                    <LayoutItemNestedControlCollection>
                                                        <dx:LayoutItemNestedControlContainer>
                                                            <dx:ASPxTextBox ID="txtSubAltura" runat="server" NullText="Altura" Width="100%" CssClass="form-control altura-textbox">
                                                            </dx:ASPxTextBox>
                                                        </dx:LayoutItemNestedControlContainer>
                                                    </LayoutItemNestedControlCollection>
                                                </dx:LayoutItem>

                                                <dx:LayoutItem Caption="Observación">
                                                    <LayoutItemNestedControlCollection>
                                                        <dx:LayoutItemNestedControlContainer>
                                                            <dx:ASPxTextBox ID="txtSubObservacion" runat="server" NullText="Observación" Width="100%" CssClass="form-control altura-textbox">
                                                            </dx:ASPxTextBox>
                                                        </dx:LayoutItemNestedControlContainer>
                                                    </LayoutItemNestedControlCollection>
                                                </dx:LayoutItem>

                                                <dx:LayoutItem ShowCaption="False">
                                                    <LayoutItemNestedControlCollection>
                                                        <dx:LayoutItemNestedControlContainer>
                                                            <dx:ASPxButton ID="btnAgregar" runat="server" Theme="Moderno" Text="Agregar" AutoPostBack="false">
                                                                <ClientSideEvents Click="function(s,e){ LoadingPanel.Show(); callback_agregar.PerformCallback();}" />
                                                            </dx:ASPxButton>

                                                            <dx:ASPxLabel ID="lblMsg" runat="server" Text="" Visible="false" />

                                                        </dx:LayoutItemNestedControlContainer>
                                                    </LayoutItemNestedControlCollection>
                                                </dx:LayoutItem>

                                                <dx:LayoutItem ShowCaption="False">
                                                    <LayoutItemNestedControlCollection>
                                                        <dx:LayoutItemNestedControlContainer>
                                                            <dx:ASPxCallbackPanel ID="CallbackPanelMSG" EnableCallbackAnimation="true" SettingsLoadingPanel-Enabled="true" runat="server" ClientInstanceName="CallbackPanelMSG">
                                                                <PanelCollection>
                                                                </PanelCollection>
                                                            </dx:ASPxCallbackPanel>
                                                        </dx:LayoutItemNestedControlContainer>
                                                    </LayoutItemNestedControlCollection>
                                                </dx:LayoutItem>

                                            </Items>
                                        </dx:LayoutGroup>



                                    </Items>
                                </dx:ASPxFormLayout>

                                <dx:ASPxCallback ID="callback_agregar" runat="server" ClientInstanceName="callback_agregar" OnCallback="callback_agregar_Callback">
                                    <ClientSideEvents BeginCallback="function(s,e){ }" CallbackComplete="function(s,e){gv_datos.PerformCallback();CallbackPanelMSG.PerformCallback(); }" EndCallback="function(s,e){  gv_datos.PerformCallback();LoadingPanel.Hide();CallbackPanelMSG.PerformCallback();  }" />
                                </dx:ASPxCallback>


                                <dx:ASPxCallback ID="callback_quitar" runat="server" ClientInstanceName="callback_quitar">
                                    <ClientSideEvents CallbackComplete="function(s,e){ hf_idRef_quitar.Set('idSubFuente', e.parameter.toString());}" EndCallback="function(s,e){popup_confirmQuitar.Show(); CallbackPanelMSG.PerformCallback();}" />
                                </dx:ASPxCallback>

                                <dx:ASPxCallback ID="callback_quitar_guarda" runat="server" ClientInstanceName="callback_quitar_guarda" OnCallback="callback_quitar_guarda_Callback">
                                    <ClientSideEvents EndCallback="function(s,e){popup_confirmQuitar.Hide();CallbackPanelMSG.PerformCallback();}" />
                                </dx:ASPxCallback>


                                <dx:ASPxPopupControl ID="popup_confirmQuitar" runat="server" ClientInstanceName="popup_confirmQuitar" Height="83px" Modal="True" CloseAction="CloseButton" Width="207px" AllowDragging="True" ShowHeader="false" PopupHorizontalAlign="WindowCenter" PopupVerticalAlign="WindowCenter">
                                    <ClientSideEvents Shown="function(s, e) {LoadingPanel.Hide(); CallbackPanelQuitar.PerformCallback(); }" Closing="function(s, e){ gv_datos.PerformCallback();  }"></ClientSideEvents>
                                    <ContentCollection>
                                        <dx:PopupControlContentControl runat="server">
                                            ¿Está seguro?
                                            <br />
                                            <br />

                                            <dx:ASPxHiddenField ID="hf_idRef_quitar" ClientInstanceName="hf_idRef_quitar" runat="server">
                                            </dx:ASPxHiddenField>
                                            <dx:ASPxCallbackPanel ID="CallbackPanelQuitar" EnableCallbackAnimation="false" SettingsLoadingPanel-Enabled="true" runat="server" Width="100%" ClientInstanceName="CallbackPanelQuitar">
                                                <PanelCollection>
                                                    <dx:PanelContent>
                                                        <dx:PopupControlContentControl>
                                                        </dx:PopupControlContentControl>
                                                    </dx:PanelContent>
                                                </PanelCollection>
                                            </dx:ASPxCallbackPanel>
                                            <table style="border: none">
                                                <tr>
                                                    <td>
                                                        <dx:ASPxButton ID="btnConfirmSi" runat="server" Theme="Material" AutoPostBack="False" Text="OK" Width="80px">
                                                            <ClientSideEvents Click="function(s, e) {   callback_quitar_guarda.PerformCallback();       }" />
                                                        </dx:ASPxButton>
                                                    </td>
                                                    <td>
                                                        <dx:ASPxButton ID="btnConfirmNo" runat="server" AutoPostBack="False" ClientInstanceName="btnCancel" Text="Cancel" Width="80px">
                                                            <ClientSideEvents Click="function(s, e) { popup_confirmQuitar.Hide(); }" />
                                                        </dx:ASPxButton>
                                                    </td>
                                                </tr>
                                            </table>
                                        </dx:PopupControlContentControl>
                                    </ContentCollection>
                                </dx:ASPxPopupControl>

                            </div>
                        </div>


                        <%--Mapa de Google Maps--%>

                        <div class="card-body col-lg-3">
                            <div action="#">

                                <h5 class="mt-0 mb-1">Mapa de <span class="text-decoration-underline">Sub-Fuentes</span>
                                </h5>

                                <div id="mapSF" class="w-90 border border-secondary rounded-2 border-width-2" style="height: 320px;"></div>
                                <%--<p class="text-body-secondary text-center">Ctrl + rueda de desplazamiento acerca/aleja
                                    el mapa.<br />
                                    Doble clic permite fijar un punto o dirección específica.</p>--%>
                            </div>
                        </div>

                        <div class="card-body col-lg-1">
                            <div action="#">

                                <div class="row mt-5">
                                    <div class="col-lg-12 p-0">
                                        <dx:ASPxTextBox ID="txtLatSF" ClientInstanceName="txtLat1" CssClass="form-control altura-textbox" ReadOnly="true" NullText="Lat." AutoCompleteType="Disabled" runat="server">
                                        </dx:ASPxTextBox>
                                    </div>
                                </div>
                                <div class="row mt-1">
                                    <div class="col-lg-12 p-0">
                                        <dx:ASPxTextBox ID="txtLngSF" ClientInstanceName="txtLng1" CssClass="form-control altura-textbox" ReadOnly="true" NullText="Lon." AutoCompleteType="Disabled" runat="server">
                                        </dx:ASPxTextBox>
                                    </div>
                                </div>

                            </div>
                        </div>

                        <div class="card-body col-lg-11">
                            <div action="#">

                                <dx:ASPxGridView ID="gv_datos" runat="server" OnDataBinding="gv_datos_DataBinding" EnableRowsCache="false" ViewStateMode="Disabled" DataSourceForceStandardPaging="false" EnableViewState="false" Theme="MaterialCompact" Width="100%" ClientInstanceName="gv_datos" AutoGenerateColumns="False" KeyFieldName="Id">
                                    <ClientSideEvents EndCallback="function(s,e){LoadingPanel.Hide();}" />

                                    <SettingsBehavior AllowFocusedRow="True" AllowSelectByRowClick="True" ColumnResizeMode="NextColumn" AllowSelectSingleRowOnly="true" FilterRowMode="Auto" AllowDragDrop="false" AllowEllipsisInText="true" />
                                    <SettingsPager PageSize="20" AlwaysShowPager="true"></SettingsPager>

                                    <SettingsText CommandEdit="Opciones" />
                                    <SettingsAdaptivity AdaptivityMode="HideDataCellsWindowLimit" AllowOnlyOneAdaptiveDetailExpanded="true">
                                    </SettingsAdaptivity>


                                    <Styles>
                                        <Cell Wrap="False" />
                                        <FocusedRow BackColor="#C3C3C3" ForeColor="White">
                                        </FocusedRow>
                                    </Styles>

                                    <Settings ShowFilterRow="true" ShowFilterRowMenu="true" VerticalScrollBarMode="Hidden" />
                                    <Columns>

                                        <dx:GridViewDataColumn Caption="Id" FieldName="Id" Visible="false">
                                            <Settings AllowAutoFilter="false" />
                                            <DataItemTemplate>
                                                <dx:ASPxLabel ID="ASPxLabel411" runat="server" Text='<%# Eval("Id") %>'></dx:ASPxLabel>
                                            </DataItemTemplate>
                                        </dx:GridViewDataColumn>

                                        <dx:GridViewDataTextColumn Caption="Código" Settings-AllowEllipsisInText="true" FieldName="Codigo" HeaderStyle-HorizontalAlign="Center" CellStyle-HorizontalAlign="Center" Width="10%">
                                            <Settings AllowAutoFilter="false" />
                                            <DataItemTemplate>
                                                <dx:ASPxLabel ID="ASPxLabedddsdl12413" runat="server" Text='<%# Eval("Codigo")   %>'>
                                                </dx:ASPxLabel>
                                            </DataItemTemplate>
                                        </dx:GridViewDataTextColumn>

                                        <dx:GridViewDataTextColumn Caption="Descripción" Settings-AllowEllipsisInText="true" FieldName="Descripcion" HeaderStyle-HorizontalAlign="Center" CellStyle-HorizontalAlign="Center">
                                            <Settings AllowAutoFilter="false" />
                                            <DataItemTemplate>
                                                <dx:ASPxLabel ID="ASPxLabel12413" runat="server" Text='<%# Eval("Descripcion")  %>'>
                                                </dx:ASPxLabel>
                                            </DataItemTemplate>
                                        </dx:GridViewDataTextColumn>

                                        <dx:GridViewDataTextColumn Caption="Calle" Settings-AllowEllipsisInText="true" FieldName="Calle" HeaderStyle-HorizontalAlign="Center" CellStyle-HorizontalAlign="Center">
                                            <Settings AllowAutoFilter="false" />
                                            <DataItemTemplate>
                                                <dx:ASPxLabel ID="lbCalle" runat="server" Text='<%# Eval("Calle")  %>'></dx:ASPxLabel>
                                            </DataItemTemplate>
                                        </dx:GridViewDataTextColumn>

                                        <dx:GridViewDataTextColumn Caption="Altura" Settings-AllowEllipsisInText="true" FieldName="Altura" HeaderStyle-HorizontalAlign="Center" CellStyle-HorizontalAlign="Center">
                                            <Settings AllowAutoFilter="false" />
                                            <DataItemTemplate>
                                                <dx:ASPxLabel ID="lbAltura" runat="server" Text='<%# Eval("Altura")  %>'></dx:ASPxLabel>
                                            </DataItemTemplate>
                                        </dx:GridViewDataTextColumn>

                                        <dx:GridViewDataTextColumn Caption="Observación" Settings-AllowEllipsisInText="true" FieldName="Observacion" HeaderStyle-HorizontalAlign="Center" CellStyle-HorizontalAlign="Center">
                                            <Settings AllowAutoFilter="false" />
                                            <DataItemTemplate>
                                                <dx:ASPxLabel ID="lbObservacion" runat="server" Text='<%# Eval("Observacion")  %>'>
                                                </dx:ASPxLabel>
                                            </DataItemTemplate>
                                        </dx:GridViewDataTextColumn>

                                        <dx:GridViewDataColumn FieldName="Activa" Caption="Activo" HeaderStyle-HorizontalAlign="Center" CellStyle-HorizontalAlign="Center" Settings-AllowEllipsisInText="False" Width="11%">
                                            <DataItemTemplate>
                                                <dx:ASPxImage ID="ASPxImage1" runat="server" Visible='<%# Eval("Activa")  %>' ShowLoadingImage="true" ImageUrl="~/App_Themes/Tema1/img/check.png" Width="30px"></dx:ASPxImage>
                                                <dx:ASPxImage ID="ASPxImage2" runat="server" Visible='<%# !Convert.ToBoolean(Eval("Activa"))  %>' ShowLoadingImage="true" ImageUrl="~/App_Themes/Tema1/img/no.png" Width="30px">
                                                </dx:ASPxImage>
                                            </DataItemTemplate>
                                        </dx:GridViewDataColumn>

                                        <dx:GridViewDataColumn Caption="" HeaderStyle-HorizontalAlign="Center" CellStyle-HorizontalAlign="Center" Settings-AllowEllipsisInText="False" Width="13%">
                                            <DataItemTemplate>

                                                <dx:ASPxButton ID="btnAnularActivar" runat="server" RenderMode="Link" CssClass="fw-medium text-danger text-decoration-none" Visible='true' Text="Anular/Activar" OnInit="btnAnularActivar_Init" CommandArgument='<%# Eval("Id" )%>' AutoPostBack="false" ImagePosition="Right">
                                                    <ClientSideEvents Click="function(s,e) { LoadingPanel.Show(); mostrarMensaje(); }" />
                                                </dx:ASPxButton>

                                            </DataItemTemplate>
                                        </dx:GridViewDataColumn>

                                    </Columns>
                                </dx:ASPxGridView>
                                <script type="text/javascript">
                                    // <![CDATA[
                                    ASPxClientControl.GetControlCollection().ControlsInitialized.AddHandler(function (s, e) {
                                        UpdateGridHeight();
                                    });
                                    ASPxClientControl.GetControlCollection().BrowserWindowResized.AddHandler(function (s, e) {
                                        UpdateGridHeight();
                                    });
                                    ASPxClientControl.GetControlCollection().EndCallback.AddHandler(function (s, e) {
                                        UpdateGridHeight();
                                    });
                                    // ]]>
                                </script>


                                <%----BOTONES----%>

                                <div class="row d-flex justify-content-end">
                                    <div class="col-lg-4 mt-5">
                                        <dx:ASPxButton ID="btnGrabar" runat="server" CssClass="btn btn-secondary fw-semibold ms-5 px-0 py-1" OnClick="btnGrabar_Click" ClientInstanceName="btnGrabar" AutoPostBack="False" CausesValidation="false" Text="Grabar">
                                            <ClientSideEvents Click="function(s,e) { MostrarLoading(); }" />
                                        </dx:ASPxButton>

                                        <dx:ASPxButton ID="btnVolver" runat="server" Style="float: right" CssClass="btn text-white fw-semibold px-0 py-1" BackColor="Brown" ClientInstanceName="btnVolver" AutoPostBack="False" CausesValidation="false" Text="Volver">
                                            <ClientSideEvents Click="function(s,e) { volverButton(); }" />
                                        </dx:ASPxButton>

                                        <dx:ASPxButton ID="btnVolverEditado" runat="server" CssClass="btn text-white fw-semibold px-0 py-1 ms-3" BackColor="Brown" ClientInstanceName="btnVolverEditado" AutoPostBack="False" CausesValidation="false" Text="Volver">
                                            <ClientSideEvents Click="function(s,e){ volverButtonEditado(); }" />
                                        </dx:ASPxButton>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card-body col-lg-1"></div>

                    </div>
                </div>
            </div>
        </div>
    </div>

</asp:Content>


<asp:Content ID="Content3" ContentPlaceHolderID="footer" runat="server">

    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDZCRSlDF2kaGvZusltpUYmNDczpmVcz_8&callback=initMap" async defer></script>

    <script>

        var selectedLatLng;
        var previousMarker = null;

        function initMap() {
            var map = new google.maps.Map(document.getElementById('map'), {
                center: { lat: -38.9499, lng: -68.0655 },
                zoom: 13,
                disableDoubleClickZoom: true
            });

            google.maps.event.addListener(map, 'dblclick', function (event) {

                if (previousMarker) {
                    previousMarker.setMap(null);
                }

                selectedLatLng = event.latLng;

                txtLat.SetText(selectedLatLng.lat());
                txtLng.SetText(selectedLatLng.lng());

                var marker = new google.maps.Marker({
                    position: selectedLatLng,
                    map: map,
                });

                previousMarker = marker;
            });
        };

       /* document.addEventListener("DOMContentLoaded", function () {
            initMap();
        });
        */
        var selectedLatLngSF;
        var previousMarkerSF = null;

        function initMapSF() {
            var mapSF = new google.maps.Map(document.getElementById('mapSF'), {
                center: { lat: -38.9499, lng: -68.0655 },
                zoom: 13,
                disableDoubleClickZoom: true
            });

            google.maps.event.addListener(mapSF, 'dblclick', function (event) {

                if (previousMarkerSF) {
                    previousMarkerSF.setMap(null);
                }

                selectedLatLngSF = event.latLng;

                txtLatSF.SetText(selectedLatLngSF.lat());
                txtLngSF.SetText(selectedLatLngSF.lng());

                var markerSF = new google.maps.Marker({
                    position: selectedLatLngSF,
                    map: mapSF,
                });

                previousMarkerSF = markerSF;
            });
        };

        document.addEventListener("DOMContentLoaded", function () {
            initMap();
            initMapSF();
        });

    </script>

</asp:Content>
