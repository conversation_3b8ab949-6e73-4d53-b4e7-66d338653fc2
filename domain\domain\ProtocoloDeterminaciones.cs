//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 7/12/2023 14:05:29
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.ProtocoloDeterminaciones, Domain in the schema.
    /// </summary>
    public partial class ProtocoloDeterminaciones {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for ProtocoloDeterminaciones constructor in the schema.
        /// </summary>
        public ProtocoloDeterminaciones()
        {
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Resultado in the schema.
        /// </summary>
        public virtual System.Nullable<decimal> Resultado
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Signo in the schema.
        /// </summary>
        public virtual string Signo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Observacion in the schema.
        /// </summary>
        public virtual string Observacion
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for IdMetodosaMedir in the schema.
        /// </summary>
        public virtual System.Nullable<int> IdMetodosaMedir
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Determinaciones in the schema.
        /// </summary>
        public virtual Determinaciones Determinaciones
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Protocolos in the schema.
        /// </summary>
        public virtual Protocolos Protocolos
        {
            get;
            set;
        }
    }

}
