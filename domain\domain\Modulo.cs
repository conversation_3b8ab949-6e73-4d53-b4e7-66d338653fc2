﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 23/10/2023 15:15:08
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.Modulo, Domain in the schema.
    /// </summary>
    public partial class Modulo {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for Modulo constructor in the schema.
        /// </summary>
        public Modulo()
        {
            this.RolModulos = new HashSet<RolModulo>();
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Icon in the schema.
        /// </summary>
        public virtual string Icon
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for NodoPadre in the schema.
        /// </summary>
        public virtual int NodoPadre
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Nodo in the schema.
        /// </summary>
        public virtual int Nodo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Descripcion in the schema.
        /// </summary>
        public virtual string Descripcion
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Url in the schema.
        /// </summary>
        public virtual string Url
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Orden in the schema.
        /// </summary>
        public virtual System.Nullable<int> Orden
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for RolModulos in the schema.
        /// </summary>
        public virtual ISet<RolModulo> RolModulos
        {
            get;
            set;
        }
    }

}
