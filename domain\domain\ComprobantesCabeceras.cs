﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 10/8/2023 15:55:09
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.ComprobantesCabeceras, Domain in the schema.
    /// </summary>
    public partial class ComprobantesCabeceras {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for ComprobantesCabeceras constructor in the schema.
        /// </summary>
        public ComprobantesCabeceras()
        {
            this.ComprobantesItems = new HashSet<ComprobantesItems>();
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for IdComprobante in the schema.
        /// </summary>
        public virtual int IdComprobante
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Cliente in the schema.
        /// </summary>
        public virtual string Cliente
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Domicilio in the schema.
        /// </summary>
        public virtual string Domicilio
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Localidad in the schema.
        /// </summary>
        public virtual string Localidad
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Iva in the schema.
        /// </summary>
        public virtual string Iva
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Cuit in the schema.
        /// </summary>
        public virtual string Cuit
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for NroCliente in the schema.
        /// </summary>
        public virtual string NroCliente
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Vuelto in the schema.
        /// </summary>
        public virtual decimal Vuelto
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for EtiquetaIvaRI in the schema.
        /// </summary>
        public virtual string EtiquetaIvaRI
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for EtiquetaIvaRNI in the schema.
        /// </summary>
        public virtual string EtiquetaIvaRNI
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ImporteMonedaDePago in the schema.
        /// </summary>
        public virtual System.Nullable<decimal> ImporteMonedaDePago
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ValorMonedaDePago in the schema.
        /// </summary>
        public virtual System.Nullable<decimal> ValorMonedaDePago
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Zona in the schema.
        /// </summary>
        public virtual string Zona
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for IdIVACondicion in the schema.
        /// </summary>
        public virtual int IdIVACondicion
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for IdMonedaDePago in the schema.
        /// </summary>
        public virtual System.Nullable<int> IdMonedaDePago
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Comprobantes in the schema.
        /// </summary>
        public virtual Comprobantes Comprobantes
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ComprobantesItems in the schema.
        /// </summary>
        public virtual ISet<ComprobantesItems> ComprobantesItems
        {
            get;
            set;
        }
    }

}
