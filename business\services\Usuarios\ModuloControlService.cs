﻿using DAO;
using Domain;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Usuarios
{
    public class ModuloControlService
    {
        public static IList<ModuloControl> GetAll()
        {
            IList<ModuloControl> listaAux;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                listaAux = (IList<ModuloControl>)new ModuloControlRepository(sess).GetAll();
                sess.Close();
                sess.Dispose();
            }

            return (listaAux);
        }

        public static ModuloControl getById(int id)
        {
            ModuloControl u;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                u = new ModuloControlRepository(sess).GetByKey(id);

                if (u != null)
                {
                    NHibernateUtil.Initialize(u.RolModulo);

                }
                sess.Close();
                sess.Dispose();
                return u;
            }
        }

        public static IList<ModuloControl> getByIdModulo(int idModulo)
        {
            IList<ModuloControl> u;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                u = new ModuloControlRepository(sess).GetAllByModulo(idModulo);

               if (u != null)
                {
               foreach(ModuloControl mc in u)
                    {
               //         NHibernateUtil.Initialize(mc.TipoPermiso);
                    }  

                }
                sess.Close();
                sess.Dispose();
                return u;
            }
        }


        public static IList<ModuloControl> getByIdRolModulo(int idRolModulo)
        {
            IList<ModuloControl> u;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                u = new ModuloControlRepository(sess).GetAllByIdRolModulo(idRolModulo);

                if (u != null)
                {
                    //foreach (ModuloControl mc in u)
                    //{
                    //    NHibernateUtil.Initialize(mc.TipoPermiso);
                    //}

                }
                sess.Close();
                sess.Dispose();
                return u;
            }
        }

        public static void SaveOrUpdate(ModuloControl u)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new ModuloControlRepository(sess).Add(u);

                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }

            }
        }

        public static void Delete(ModuloControl u)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new ModuloControlRepository(sess).Remove(u);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }

            }
        }
    }
}
