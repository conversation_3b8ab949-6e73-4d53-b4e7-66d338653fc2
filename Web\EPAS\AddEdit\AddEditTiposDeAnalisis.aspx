﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="AddEditTiposDeAnalisis.aspx.cs" Inherits="Web.EPAS.AddEdit.AddEditTiposDeAnalisis" %>

<%@ Register Assembly="DevExpress.Web.v18.1" Namespace="DevExpress.Web" TagPrefix="dx" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">

    <script type="text/javascript">

        function volverButton() {

            if (confirm) {
                Swal.fire({
                    text: "No se guardaron los cambios. Desea volver de todos modos?",
                    showCancelButton: true,
                    confirmButtonText: "Volver"
                }).then((result) => {
                    if (result.isConfirmed) {

                        window.location.href = "/SIGeLab/EPAS/Lista/TiposDeAnalisis.aspx";

                    }
                });
            }
        }

        function volverButtonEditado() {

            if (confirm) {

                window.location.href = "/SIGeLab/EPAS/Lista/TiposDeAnalisis.aspx";

            }
        }

    </script>

</asp:Content>


<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

    <div class="page-header page-header-light shadow">
        <div class="page-header-content d-lg-flex border-top">
            <div class="d-flex">
                <div class="breadcrumb py-2">
                    <a href="<%= Web.Util.Helpers.GetApplicationPath() %>/Inicio.aspx" class="breadcrumb-item"><i class="icon-home2 mr-2"></i>Inicio</a>
                    <a href="<%= Web.Util.Helpers.GetApplicationPath() %>/EPAS/Lista/TiposDeAnalisis.aspx" class="breadcrumb-item"><i class="icon-display me-2"></i>Mantenimiento</a>
                    <a href="<%= Web.Util.Helpers.GetApplicationPath() %>/EPAS/Lista/TiposDeAnalisis.aspx" class="breadcrumb-item">Tipos de Análisis</a>
                    <span class="breadcrumb-item active">
                        <asp:Literal ID="ltr_nombreBreadcrumb" runat="server">Agregar/Editar Tipos de Análisis</asp:Literal></span>
                </div>

                <a href="#" class="header-elements-toggle text-default d-md-none"><i class="icon-more"></i></a>
            </div>
        </div>
    </div>

    <div class="row gx-1">

        <div class="col-lg-12"">

            <div class="navbar navbar-expand-xl bg-secondary navbar-dark rounded-top mt-1">
                <div class="container-fluid">                           
                    <div class="nav-item dropdown">
                        <h5 class="mt-2 mb-1 fs-5" data-bs-toggle="tab">Tipos de Análisis - Mantenimiento</h5>
                    </div>                        
                </div>
            </div>

            <!-- Card Cliente -->

            <div class="tab-content border border-1 rounded-bottom p-1">

                <div class="card">
                    <div class="card-body col-lg-5">
                        <div action="#">

                            <div class="row mb-1">
                                <dx:ASPxTextBox ID ="TxtID" Visible="false" CssClass="form-cpntrol altura-textbox" AutoCompleteType="Disabled"  runat="server">
                                </dx:ASPxTextBox>
                                <label class="col-lg-2 col-form-label pe-0 fw-semibold btn-buscar">Código</label>
                                <div class="col-lg-2 p-0">
                                    <dx:ASPxTextBox ID="txtcodigo" ClientInstanceName="txtcodigo" CssClass="form-control altura-textbox" AutoCompleteType="Disabled" runat="server">
                                    </dx:ASPxTextBox>
                                </div>
                            </div>

                            <div class="row mb-1">
                                <label class="col-lg-2 col-form-label ps-1 pe-0 fw-semibold btn-buscar">Descripción</label>
                                <div class="col-lg-5 p-0">
                                    <dx:ASPxTextBox ID="txtDescripcion" ClientInstanceName="txtDescripcion" CssClass="form-control altura-textbox" AutoCompleteType="Disabled" runat="server">
                                    </dx:ASPxTextBox>
                                </div>
                            </div>
                                <div class="row mb-1">
                                    <label class="col-lg-2 col-form-label ps-1 pe-0 fw-semibold btn-buscar">Activo</label>
                                    <div class="col-lg-10 p-0">
                                    <dx:ASPxCheckBox ID="CB_Activo" runat="server" ToggleSwitchDisplayMode="Always">
                   
                                        </dx:ASPxCheckBox>

                                        <dx:ASPxLabel ID="lblMsg" runat="server" cssclass="text-danger" ></dx:ASPxLabel>
                                    </div>
                                </div>

							<div class="row mt-3 d-flex justify-content-end">
                                <div class="col-lg-7">
                                    <dx:ASPxButton ID="btnGrabar" runat="server" CssClass="btn btn-secondary fw-semibold px-0 py-1" OnClick="btnGrabar_Click" ClientInstanceName="btnGrabar" AutoPostBack="False" CausesValidation="false" Text="Grabar">
                                        <ClientSideEvents Click="function(s,e) { MostrarLoading(); }" />
                                    </dx:ASPxButton>

                                    <dx:ASPxButton ID="btnVolver" runat="server" CssClass="btn text-white fw-semibold px-0 py-1 ms-3" BackColor="Brown" ClientInstanceName="btnVolver" AutoPostBack="False" CausesValidation="false" Text="Volver" >
                                        <ClientSideEvents Click="function(s,e){ volverButton();}" />
                                    </dx:ASPxButton>

                                    <dx:ASPxButton ID="btnVolverEditado" runat="server" CssClass="btn text-white fw-semibold px-0 py-1 ms-3" BackColor="Brown" ClientInstanceName="btnVolverEditado" AutoPostBack="False" CausesValidation="false" Text="Volver" >
                                        <ClientSideEvents Click="function(s,e){ volverButtonEditado();}" />
                                    </dx:ASPxButton>
                                </div>
                            </div>                                

                        </div>
                    </div>
                    <div class="card-body col-lg-7"></div>
                </div>

            </div>
        </div>
    </div>

</asp:Content>


<asp:Content ID="Content3" ContentPlaceHolderID="footer" runat="server">
</asp:Content>
