//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 25/9/2023 14:39:43
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.Retenciones, Domain in the schema.
    /// </summary>
    public partial class Retenciones {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for Retenciones constructor in the schema.
        /// </summary>
        public Retenciones()
        {
            this.ComprobantesRetenciones = new HashSet<ComprobantesRetenciones>();
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Descripcion in the schema.
        /// </summary>
        public virtual string Descripcion
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Abreviatura in the schema.
        /// </summary>
        public virtual string Abreviatura
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Cuenta in the schema.
        /// </summary>
        public virtual int Cuenta
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ComprobantesRetenciones in the schema.
        /// </summary>
        public virtual ISet<ComprobantesRetenciones> ComprobantesRetenciones
        {
            get;
            set;
        }
    }

}
