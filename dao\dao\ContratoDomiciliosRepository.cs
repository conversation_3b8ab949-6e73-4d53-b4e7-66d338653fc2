//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 3/11/2023 09:44:13
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO {
    public partial class ContratoDomiciliosRepository : NHibernateRepository<Domain.ContratoDomicilios>, IContratoDomiciliosRepository {
        public ContratoDomiciliosRepository(ISession session) : base(session) {
        }

        public virtual ICollection<Domain.ContratoDomicilios> GetAll() {
            return session.CreateQuery(string.Format("from ContratoDomicilios")).List<Domain.ContratoDomicilios>();
        }

        public virtual Domain.ContratoDomicilios GetByKey(int _Id) {
            return session.Get<Domain.ContratoDomicilios>(_Id);
        }
    }
}
