//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 28/11/2023 16:48:49
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class ProtocoloDeterminacionesRepository : NHibernateRepository<Domain.ProtocoloDeterminaciones>, IProtocoloDeterminacionesRepository
    {
        public ProtocoloDeterminacionesRepository(ISession session) : base(session)
        {
        }

        public virtual ICollection<Domain.ProtocoloDeterminaciones> GetAll()
        {
            return session.CreateQuery(string.Format("from ProtocoloDeterminaciones")).List<Domain.ProtocoloDeterminaciones>();
        }

        public virtual IList<Domain.ProtocoloDeterminaciones> GetByIDProtocolo(int idProtocolo)
        {
            return session.CreateQuery(string.Format("from ProtocoloDeterminaciones where Protocolos.Id=:idProtocolo"))
                .SetParameter("idProtocolo",idProtocolo)
                .List<Domain.ProtocoloDeterminaciones>();
        }

        public virtual Domain.ProtocoloDeterminaciones GetByIDProtocoloAndIDDeterminacion(int idProtocolo,int idDeterminacion)
        {
            return session.CreateQuery(string.Format("from ProtocoloDeterminaciones where Protocolos.Id=:idProtocolo and Determinaciones.Id=:idDeterminacion"))
                .SetParameter("idProtocolo",idProtocolo)
                .SetParameter("idDeterminacion",idDeterminacion)
                .UniqueResult<Domain.ProtocoloDeterminaciones>();
        }

        public virtual Domain.ProtocoloDeterminaciones GetByKey(int _Id)
        {
            return session.Get<Domain.ProtocoloDeterminaciones>(_Id);
        }
    }
}
