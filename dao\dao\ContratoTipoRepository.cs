//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 10/11/2023 18:03:20
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class ContratoTipoRepository : NHibernateRepository<Domain.ContratoTipo>, IContratoTipoRepository
    {
        public ContratoTipoRepository(ISession session) : base(session)
        {
        }

        public virtual IList<Domain.ContratoTipo> GetAll()
        {
            return session.CreateQuery(string.Format("from ContratoTipo")).List<Domain.ContratoTipo>();
        }

        public virtual Domain.ContratoTipo GetByKey(int _Id)
        {
            return session.Get<Domain.ContratoTipo>(_Id);
        }

        public virtual ICollection<Domain.ContratoTipo> GetByContratoTipo(bool Activo) {
            string hql = "from ContratoTipo order BY Descripcion asc";

            IQuery q = session.CreateQuery(hql);
            return q.List<Domain.ContratoTipo>();

        }
    }
}
