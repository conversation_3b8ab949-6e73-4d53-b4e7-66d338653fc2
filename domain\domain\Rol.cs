//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 8/11/2023 09:53:25
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.Rol, Domain in the schema.
    /// </summary>
    public partial class Rol {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for Rol constructor in the schema.
        /// </summary>
        public Rol()
        {
            this.RolDeterminaciones = new HashSet<RolDeterminaciones>();
            this.RolModulos = new HashSet<RolModulo>();
            this.Usuarios = new HashSet<Usuario>();
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Codigo in the schema.
        /// </summary>
        public virtual string Codigo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Nombre in the schema.
        /// </summary>
        public virtual string Nombre
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Observacion in the schema.
        /// </summary>
        public virtual string Observacion
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Eliminable in the schema.
        /// </summary>
        public virtual bool Eliminable
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Renombrable in the schema.
        /// </summary>
        public virtual bool Renombrable
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Editable in the schema.
        /// </summary>
        public virtual bool Editable
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Superadmin in the schema.
        /// </summary>
        public virtual bool Superadmin
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Admin in the schema.
        /// </summary>
        public virtual System.Nullable<bool> Admin
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for IdRolModulo in the schema.
        /// </summary>
        public virtual System.Nullable<int> IdRolModulo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Determinaciones in the schema.
        /// </summary>
        public virtual Determinaciones Determinaciones
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for RolDeterminaciones in the schema.
        /// </summary>
        public virtual ISet<RolDeterminaciones> RolDeterminaciones
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for RolModulos in the schema.
        /// </summary>
        public virtual ISet<RolModulo> RolModulos
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Usuarios in the schema.
        /// </summary>
        public virtual ISet<Usuario> Usuarios
        {
            get;
            set;
        }
    }

}
