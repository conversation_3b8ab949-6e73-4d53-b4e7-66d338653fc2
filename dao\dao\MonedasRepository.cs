﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 10/8/2023 15:55:09
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class MonedasRepository : NHibernateRepository<Domain.Monedas>, IMonedasRepository
    {
        public MonedasRepository(ISession session) : base(session)
        {
        }

        public virtual ICollection<Domain.Monedas> GetAll()
        {
            return session.CreateQuery(string.Format("from Monedas")).List<Domain.Monedas>();
        }

        public virtual Domain.Monedas GetByKey(int _Id)
        {
            return session.Get<Domain.Monedas>(_Id);
        }

        public virtual Domain.Monedas GetBySimbolo(string Simbolo)
        {
            string hql = "from Monedas  WHERE Simbolo = :Simbolo";

            IQuery q = session.CreateQuery(hql);
            q.SetParameter("Simbolo", Simbolo);
            return q.UniqueResult<Domain.Monedas>();

        }
    }
}
