﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 20/10/2023 15:17:33
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Collections.Generic;

namespace Domain
{
    public partial interface IFuentesTiposRepository : IRepository<FuentesTipos>
    {
        ICollection<FuentesTipos> GetAll();
        FuentesTipos GetByKey(int _Id);
    }
}
