﻿using Business.Services.Usuarios;
using DevExpress.Export;
using DevExpress.Web;
using Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web.Config
{
    public partial class Roles : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                if (!IsPostBack)
                {
                   // Literal ltr_nombreBreadcrumb = (Literal)Master.FindControl("ltr_nombreBreadcrumb");
                    ltr_nombreBreadcrumb.Text = "Lista de áreas";

                   // ltr_Titulo.Text = "Roles del sistema";

                   // iconPage.Attributes.Add("class", "icon-key mr2");

                }

                gv_areas.DataSource = RolService.GetAll();
                gv_areas.DataBind();

            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al cargar la página, consulte al administrador del sistema', 'error');", true);

            }
        }

        protected void Grid_ToolbarItemClick(object source, ASPxGridToolbarItemClickEventArgs e)
        {
            ASPxGridView grid = (ASPxGridView)source;
            switch (e.Item.Name)
            {
                case "CustomExportToXLS":
                gv_areas.ExportXlsToResponse(new DevExpress.XtraPrinting.XlsExportOptionsEx { ExportType = ExportType.WYSIWYG });
                    break;
                case "CustomExportToXLSX":
                gv_areas.ExportXlsxToResponse(new DevExpress.XtraPrinting.XlsxExportOptionsEx { ExportType = ExportType.WYSIWYG });
                    break;
                default:
                    break;
            }
        }



        protected void btn_agregar_Click(object sender, EventArgs e)
        {



            Response.Redirect(Global.ApplicationPath + "/Config/AddEditRoles.aspx", false);
        }

        protected void btnEditar_Click(object sender, EventArgs e)
        {
            try
            {
                ASPxButton btn = (ASPxButton)sender;
                GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer)btn.NamingContainer;
                var ind = DataBinder.Eval(c.DataItem, "Id");

                Response.Redirect(Global.ApplicationPath + "/Config/AddEditRoles.aspx?id=" + Util.Helpers.Encrypt(ind.ToString()), false);
            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al tratar de editar un elemento, consulte al administrador del sistema', 'error');", true);

            }
        }

        protected void btnEliminar_Click(object sender, EventArgs e)
        {
            try
            {
                ASPxButton btn = (ASPxButton)sender;
                GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer)btn.NamingContainer;
                var ind = DataBinder.Eval(c.DataItem, "Id");
                Rol r = RolService.getById(Convert.ToInt32(ind));
                try
                {
                    RolService.Delete(r);
                }catch(Exception ex)
                {
                    ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('No puede eliminar, tiene usuarios asiganados a esta área.', 'error');", true);

                }

            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al tratar de eliminar un elemento, consulte al administrador del sistema', 'error');", true);

            }
        }

    }
}