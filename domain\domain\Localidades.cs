//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 3/11/2023 09:44:12
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.Localidades, Domain in the schema.
    /// </summary>
    public partial class Localidades {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for Localidades constructor in the schema.
        /// </summary>
        public Localidades()
        {
            this.Fuentes = new HashSet<Fuentes>();
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Nombre in the schema.
        /// </summary>
        public virtual string Nombre
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Abreviatura in the schema.
        /// </summary>
        public virtual string Abreviatura
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for CodPostal in the schema.
        /// </summary>
        public virtual System.Nullable<short> CodPostal
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Activo in the schema.
        /// </summary>
        public virtual bool Activo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Codigo in the schema.
        /// </summary>
        public virtual string Codigo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Departamentos in the schema.
        /// </summary>
        public virtual Departamentos Departamentos
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Fuentes in the schema.
        /// </summary>
        public virtual ISet<Fuentes> Fuentes
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Provincias in the schema.
        /// </summary>
        public virtual Provincias Provincias
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Zonas in the schema.
        /// </summary>
        public virtual Zonas Zonas
        {
            get;
            set;
        }


        //Metodo para ver el codigo en el nombre de la localidad, agregado a mano
        public virtual string NombreCodigo => $"({Codigo}) {Nombre}";
    }

}
