﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 11/03/2019 14:47:12
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class RolRepository : NHibernateRepository<Domain.Rol>, IRolRepository
    {
        public RolRepository(ISession session) : base(session)
        {
        }

        public virtual IList<Domain.Rol> GetAll()
        {
            return session.CreateQuery(string.Format("from Rol r order by r.Nombre")).List<Domain.Rol>();
        }

        public virtual Domain.Rol GetByKey(int _Id)
        {
            return session.Get<Domain.Rol>(_Id);
        }

        public virtual Domain.Rol GetByCodigo(string codigo)
        {
            string hql = "";

            hql = "from Rol m where upper(m.Codigo) = :codigo";


            IQuery q = session.CreateQuery(hql);
            q.SetParameter("codigo", codigo.ToUpper());


            Rol ma = q.UniqueResult<Domain.Rol>();
            return ma;
        }

        public virtual Domain.Rol GetByNombre(int idRol, Rol m)
        {
            string hql = "";
            if (idRol == -1)
                hql = "from Rol m where m.Nombre = :Descripcion";
            else
                hql = "from Rol m where m.Nombre = :Descripcion and m.Id != :IdRol";

            IQuery q = session.CreateQuery(hql);
            q.SetParameter("Descripcion", m.Nombre);
            if (idRol != -1)
                q.SetParameter("IdRol", idRol);

            Rol ma = q.UniqueResult<Domain.Rol>();
            return ma;
        }
    }
}
