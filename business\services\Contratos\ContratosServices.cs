﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Collections;
using DAO;
using Domain;
using NHibernate;
using NPOI.Util;
using NPOI.SS.Formula.Functions;


namespace Business.Services.Contratos
{

    public class ContratosServices
    {

        public static void SaveOrUpdateContratos(Domain.Contratos contratos) {

            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                using(NHibernate.ITransaction tx = sess.BeginTransaction()) {
                    try {
                        new ContratosRepository(sess).Add(contratos);
                        tx.Commit();
                    } catch(Exception e) {
                        tx.Rollback();
                        throw e;
                    } finally {
                        sess.Close();
                        sess.Dispose();
                    }
                }
            }
        }

        public static Domain.Contratos GetById(int id)
        {
            Domain.Contratos c;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                c = (Domain.Contratos)new ContratosRepository(sess).GetByKey(id);
                if (c != null )
                {
                    NHibernateUtil.Initialize(c.ContratoContactos);

                    foreach(ContratoContactos b in c.ContratoContactos) {

                        //NHibernateUtil.Initialize(b.Sexos);
                        NHibernateUtil.Initialize(b.ContratoContactosEmails);
                        NHibernateUtil.Initialize(b.ContratoContactosTels);
                    }

                    NHibernateUtil.Initialize(c.ContratoDomicilios);

                    foreach(ContratoDomicilios d in c.ContratoDomicilios) {

                        NHibernateUtil.Initialize(d.DomiciliosTipos);
                        NHibernateUtil.Initialize(d.Calles.Localidades.Provincias);
                    }

                }
                sess.Close();
                sess.Dispose();
                return c;
            }
        }
       
        public static IList<Domain.Contratos> GetByCamposBusqueda(int Campobusqueda, string valor)
        {
            IList<Domain.Contratos> c=null;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
               

                switch (Campobusqueda)
                {
                    case 0:
                        int val;
                        if(int.TryParse(valor, out val))
                        {
                            c = new ContratosRepository(sess).GetByNroContrato(Convert.ToInt32(val));
                        }
                       
                        break;
                    case 1:
                        c = new ContratosRepository(sess).GetByIdentificacionTributaria(valor);
                       
                        break;
                    case 2:

                        c = new ContratosRepository(sess).GetByRazonSocialONombre(valor);
                        
                       
                        break;
                                  
                    default:
                      
                        break;
                }
                if(c != null) {
                    foreach(Domain.Contratos contratos in c) {

                        NHibernateUtil.Initialize(contratos.DocTipos);
                        NHibernateUtil.Initialize(contratos.IVACondiciones);
                        NHibernateUtil.Initialize(contratos.TelefonoTipo_IdTelefonoTipo);
                        NHibernateUtil.Initialize(contratos.TelefonoTipo_IdTelefonoTipoAlter);

                    }
                }
                sess.Close();
                sess.Dispose();
                return c;
                

            }
           
        }

        public static bool verificarContrato(string contrato) {
            bool existe = false;
            IList<Domain.Contratos> c;
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                c = new ContratosRepository(sess).verificarContrato(contrato);
                sess.Close();
                sess.Dispose();
                if(c.Count > 0)
                    existe = true;
            }
            return existe;
        }

        public static IList<Domain.Contratos> GetByTomador(bool EsTomador) {
            IList<Domain.Contratos> c = null;
            using(ISession sess = NHibernateSessionProvider.GetSession()) {
                // Consulta todos los contratos activos o inactivos según el parámetro 'Activo'
                c = sess.Query<Domain.Contratos>().Where(contrato => contrato.EsTomador == EsTomador).ToList();

                if(c != null) {
                    foreach(Domain.Contratos contrato in c) {
                        NHibernateUtil.Initialize(contrato.DocTipos);
                        NHibernateUtil.Initialize(contrato.IVACondiciones);
                        NHibernateUtil.Initialize(contrato.TelefonoTipo_IdTelefonoTipo);
                        NHibernateUtil.Initialize(contrato.TelefonoTipo_IdTelefonoTipoAlter);
                    }
                }
                sess.Close();
                sess.Dispose();
                return c;
            }
        }


    }
}
