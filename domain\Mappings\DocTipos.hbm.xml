<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="DocTipos" table="DocTipos">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="TipoDoc" type="String">
      <column name="TipoDoc" not-null="false" length="32" sql-type="nvarchar" />
    </property>
    <property name="Abreviatura" type="String">
      <column name="Abreviatura" not-null="false" length="6" sql-type="nvarchar" />
    </property>
    <property name="Mascara" type="String">
      <column name="Mascara" not-null="false" length="20" sql-type="nvarchar" />
    </property>
    <property name="MascaraDeFormato" type="String">
      <column name="MascaraDeFormato" not-null="false" length="20" sql-type="nvarchar" />
    </property>
    <property name="AbreviaturaAImprimir" type="String">
      <column name="AbreviaturaAImprimir" not-null="false" length="5" sql-type="nvarchar" />
    </property>
    <property name="Activo" type="Boolean">
      <column name="Activo" not-null="false" sql-type="bit" />
    </property>
    <set name="ContratoContactos" inverse="true" cascade="delete" generic="true">
      <key>
        <column name="IdDocTipo" />
      </key>
      <one-to-many class="ContratoContactos" />
    </set>
    <set name="Contratos" inverse="true" generic="true">
      <key>
        <column name="IdDocTipo" />
      </key>
      <one-to-many class="Contratos" />
    </set>
  </class>
</hibernate-mapping>