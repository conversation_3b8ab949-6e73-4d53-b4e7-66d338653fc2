﻿using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Domain.DTO {


    [DataContractAttribute(IsReference = true)]
    public partial class ContratoContactosDTO {
        #region Constructors

        public ContratoContactosDTO() {
        }

        public ContratoContactosDTO(int id,string nombre,string apellido,string numeroDoc,System.Nullable<System.DateTime> fechaNac,System.Nullable<bool> activo,int IdContrato,string Email,int IdTelefonoTipo,string Tel,string TelefonoTipoDescripcion,string documentosTipo,int idDocumentosTipo,string sexoDescripcion,int idSexo) {

            this.Id = id;
            this.Nombre = nombre;
            this.Apellido = apellido;
            this.DocumentosTipo = documentosTipo;
            this.NumeroDoc = numeroDoc;
            this.FechaNac = fechaNac;
            this.Activo = activo;
            this.IdDocumentosTipo = idDocumentosTipo;
            this.SexoDescripcion = sexoDescripcion;
            this.IdSexo = idSexo;
            this.IdContrato = IdContrato;
            this.Email = Email;
            this.TelefonoTipoDescripcion = TelefonoTipoDescripcion;
            this.IdTelefonoTipo = IdTelefonoTipo;
            this.Tel = Tel;
        }

        #endregion

        #region Properties

        [DataMember]
        public int Id { get; set; }


        [DataMember]
        public int IdContrato { get; set; }


        [DataMember]
        public int IdTelefonoTipo { get; set; }



        [DataMember]
        public string Nombre { get; set; }

        [DataMember]
        public string Email { get; set; }

        [DataMember]
        public string Tel { get; set; }

        [DataMember]
        public string DocumentosTipo { get; set; }

        [DataMember]
        public int IdDocumentosTipo { get; set; }

        [DataMember]
        public string TelefonoTipoDescripcion { get; set; }

        [DataMember]
        public int IdSexo { get; set; }

        [DataMember]
        public string SexoDescripcion { get; set; }

        [DataMember]
        public string Apellido { get; set; }

        [DataMember]
        public string NumeroDoc { get; set; }

        [DataMember]
        public System.Nullable<System.DateTime> FechaNac { get; set; }

        [DataMember]
        public System.Nullable<bool> Activo { get; set; }



        #endregion

        #region Navigation Properties

        //[DataMember]
        //public DocTiposDTO DocTipos { get; set; }


        //[DataMember]
        //public SexosDTO Sexos { get; set; }

        #endregion
    }

}
