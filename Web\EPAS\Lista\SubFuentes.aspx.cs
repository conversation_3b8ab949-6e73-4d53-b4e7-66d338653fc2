﻿using Business.Services.Epas;
using DevExpress.Web;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web.EPAS.Lista
{
    public partial class SubFuentes : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            CargarDatosEnGripV_SubFuentes();
            if (!IsPostBack)
            {

            }
        }

        private void CargarDatosEnGripV_SubFuentes()
        {


            try
            {
                List<Domain.SubFuentes> fuentes = (List<Domain.SubFuentes>)
                   SubFuentesService.GetAll();
                GripV_SubFuentes.DataSource = fuentes;
                GripV_SubFuentes.DataBind();
            }
            catch (Exception ex)
            {
                string g = ex.Message;
            }

        }

        protected void btnEditar_Click(object sender, EventArgs e)
        {

            try
            {
                ASPxButton btn = (ASPxButton)sender;
                if (btn != null)
                {
                    GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer)btn.NamingContainer;
                    if (c != null)
                    {
                        var ind = DataBinder.Eval(c.DataItem, "Id");
                        if (ind != null)
                        {

                            Response.Redirect(Global.ApplicationPath + "/EPAS/AddEdit/AddEditSubFuentes.aspx?id=" +
                                Util.Helpers.Encrypt(ind.ToString()), false);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al tratar de editar un elemento, consulte al administrador del sistema', 'error');", true);
            }
        }

        //protected void btnEliminar_Click(object sender, EventArgs e)
        //{
        //    try
        //    {
        //        ASPxButton btn = (ASPxButton)sender;
        //        GridViewDataItemTemplateContainer f = (GridViewDataItemTemplateContainer)btn.NamingContainer;
        //        var ind = DataBinder.Eval(f.DataItem, "Id");

        //        Domain.Fuentes fuentes = FuentesService.GetById(Convert.ToInt32(ind));

        //        FuentesService.SaveOrUpdate(fuentes);
        //        ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "GripV_Fuentes.PerformCallback();", true);


        //    }
        //    catch (Exception ex)
        //    {
        //        ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al tratar de eliminar un elemento, consulte al administrador del sistema', 'error');", true);

        //    }




        //}





    }
}