﻿using Common.DDL;
using DAO;
using Domain;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Caja
{
    public class ComprobanteServices
    {
        public static IList<Comprobantes> GetAll()
        {
            IList<Comprobantes> listaAux;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                listaAux = (IList<Comprobantes>)new ComprobantesRepository(sess).GetAll();
                sess.Close();
                sess.Dispose();
            }

            return (listaAux);
        }

        public static Comprobantes getById(int id)
        {
            Comprobantes c;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                c = new ComprobantesRepository(sess).GetByKey(id);

                if (c != null)
                {
                    NHibernateUtil.Initialize(id);

                }
                sess.Close();
                sess.Dispose();
                return c;
            }
        }
        public static void SaveOrUpdate(Comprobantes c)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new ComprobantesRepository(sess).Add(c);

                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }
            }
        }
        public static Comprobantes getByIdContrato(int id)
        {

            Comprobantes c;

            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                c = (Comprobantes)new ComprobantesRepository(sess).GetByIdContrato(id);
                {

                    sess.Close();
                    sess.Dispose();
                    return c;
                }
            }


        }
        public static void Delete(Domain.Comprobantes c)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new ComprobantesRepository(sess).Remove(c);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }

            }

        }
    }
}

