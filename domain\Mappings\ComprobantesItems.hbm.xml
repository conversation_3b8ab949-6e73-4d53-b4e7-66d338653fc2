﻿<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="ComprobantesItems" table="ComprobantesItems">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Cantidad" type="Int32">
      <column name="Cantidad" not-null="true" precision="10" scale="0" sql-type="int" />
    </property>
    <property name="PrecioSinIva" type="Decimal">
      <column name="PrecioSinIva" not-null="true" precision="19" scale="4" sql-type="money" />
    </property>
    <property name="Iva" type="Decimal">
      <column name="Iva" not-null="true" precision="19" scale="4" sql-type="money" />
    </property>
    <property name="SobreTasa" type="Decimal">
      <column name="SobreTasa" default="0" not-null="true" precision="19" scale="4" sql-type="money" />
    </property>
    <property name="Descripcion" type="String">
      <column name="Descripcion" not-null="false" sql-type="nvarchar" />
    </property>
    <property name="IdIVAAlicuota" type="Decimal">
      <column name="IdIVAAlicuota" not-null="false" precision="18" scale="0" sql-type="decimal" />
    </property>
    <property name="AlicuotaSobretasa" type="Decimal">
      <column name="AlicuotaSobretasa" not-null="false" precision="19" scale="4" sql-type="money" />
    </property>
    <property name="DiferenciaIVA" type="Decimal">
      <column name="DiferenciaIVA" not-null="false" precision="38" scale="6" sql-type="decimal" />
    </property>
    <property name="DiferenciaSobreTasa" type="Decimal">
      <column name="DiferenciaSobreTasa" not-null="false" precision="38" scale="6" sql-type="decimal" />
    </property>
    <property name="IdArticulo" type="Int32">
      <column name="IdArticulo" default="-1" not-null="false" precision="10" scale="0" sql-type="int" />
    </property>
    <many-to-one name="ComprobantesCabeceras" class="ComprobantesCabeceras">
      <column name="IdComprobante" not-null="true" precision="10" scale="0" sql-type="int" />
    </many-to-one>
  </class>
</hibernate-mapping>