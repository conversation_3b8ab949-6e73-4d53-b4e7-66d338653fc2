﻿<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{B06495BC-887B-4922-A904-31E7C12935C7}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Web</RootNamespace>
    <AssemblyName>Web</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Antlr3.Runtime, Version=*******, Culture=neutral, PublicKeyToken=eb42632606e9261f, processorArchitecture=MSIL">
      <HintPath>..\packages\Antlr3.Runtime.3.5.2-rc1\lib\net40-client\Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.Web, Version=13.0.4000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Data.v18.1">
      <HintPath>..\packages\DevExpress18\DevExpress.Data.v18.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Printing.v18.1.Core">
      <HintPath>..\packages\DevExpress18\DevExpress.Printing.v18.1.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.RichEdit.v18.1.Core, Version=18.1.4.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\DevExpress18\DevExpress.RichEdit.v18.1.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.RichEdit.v18.1.Export">
      <HintPath>..\packages\DevExpress18\DevExpress.RichEdit.v18.1.Export.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Utils.v18.1">
      <HintPath>..\packages\DevExpress18\DevExpress.Utils.v18.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Utils.v18.1.UI">
      <HintPath>..\packages\DevExpress18\DevExpress.Utils.v18.1.UI.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Web.ASPxHtmlEditor.v18.1, Version=18.1.4.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\DevExpress18\DevExpress.Web.ASPxHtmlEditor.v18.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Web.ASPxPivotGrid.v18.1, Version=18.1.4.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\DevExpress18\DevExpress.Web.ASPxPivotGrid.v18.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Web.ASPxRichEdit.v18.1, Version=18.1.4.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\DevExpress18\DevExpress.Web.ASPxRichEdit.v18.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Web.ASPxThemes.v18.1">
      <HintPath>..\packages\DevExpress18\DevExpress.Web.ASPxThemes.v18.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Web.ASPxTreeList.v18.1, Version=18.1.4.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\DevExpress18\DevExpress.Web.ASPxTreeList.v18.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Web.Resources.v18.1">
      <HintPath>..\packages\DevExpress18\DevExpress.Web.Resources.v18.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Web.v18.1">
      <HintPath>..\packages\DevExpress18\DevExpress.Web.v18.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraCharts.v18.1.Web, Version=18.1.4.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\DevExpress18\DevExpress.XtraCharts.v18.1.Web.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraReports.v18.1.Web.WebForms, Version=18.1.4.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\DevExpress18\DevExpress.XtraReports.v18.1.Web.WebForms.dll</HintPath>
    </Reference>
    <Reference Include="Iesi.Collections, Version=4.0.0.4000, Culture=neutral, PublicKeyToken=aa95f207798dfdb4, processorArchitecture=MSIL">
      <HintPath>..\packages\Iesi.Collections.4.0.4\lib\net461\Iesi.Collections.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=2.0.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NHibernate, Version=5.3.0.0, Culture=neutral, PublicKeyToken=aa95f207798dfdb4, processorArchitecture=MSIL">
      <HintPath>..\packages\NHibernate.5.3.3\lib\net461\NHibernate.dll</HintPath>
    </Reference>
    <Reference Include="NHibernate.Caches.SysCache, Version=5.7.0.0, Culture=neutral, PublicKeyToken=6876f2ea66c9f443, processorArchitecture=MSIL">
      <HintPath>..\packages\NHibernate.Caches.SysCache.5.7.0\lib\net461\NHibernate.Caches.SysCache.dll</HintPath>
    </Reference>
    <Reference Include="NHibernate.Caches.SysCache2, Version=5.7.0.0, Culture=neutral, PublicKeyToken=6876f2ea66c9f443, processorArchitecture=MSIL">
      <HintPath>..\packages\NHibernate.Caches.SysCache2.5.7.0\lib\net461\NHibernate.Caches.SysCache2.dll</HintPath>
    </Reference>
    <Reference Include="Remotion.Linq, Version=2.2.0.0, Culture=neutral, PublicKeyToken=fee00910d6e5f53b, processorArchitecture=MSIL">
      <HintPath>..\packages\Remotion.Linq.2.2.0\lib\net45\Remotion.Linq.dll</HintPath>
    </Reference>
    <Reference Include="Remotion.Linq.EagerFetching, Version=2.2.0.0, Culture=neutral, PublicKeyToken=fee00910d6e5f53b, processorArchitecture=MSIL">
      <HintPath>..\packages\Remotion.Linq.EagerFetching.2.2.0\lib\net45\Remotion.Linq.EagerFetching.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Xml.Linq" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="App_Themes\Tema1\assets\css\AgregarCliente\AgregarCliente.css" />
    <Content Include="App_Themes\Tema1\assets\css\all.min.css" />
    <Content Include="App_Themes\Tema1\assets\css\bootstrap.css" />
    <Content Include="App_Themes\Tema1\assets\css\bootstrap.min.css" />
    <Content Include="App_Themes\Tema1\assets\css\bootstrap_limitless.css" />
    <Content Include="App_Themes\Tema1\assets\css\bootstrap_limitless.min.css" />
    <Content Include="App_Themes\Tema1\assets\css\BuscarContrato\BuscarContrato.css" />
    <Content Include="App_Themes\Tema1\assets\css\CargaDePagos\CargaDePagos.css" />
    <Content Include="App_Themes\Tema1\assets\css\colors.min.css" />
    <Content Include="App_Themes\Tema1\assets\css\components.css" />
    <Content Include="App_Themes\Tema1\assets\css\components.min.css" />
    <Content Include="App_Themes\Tema1\assets\css\css_CargaPagos\CargaDePagos.css" />
    <Content Include="App_Themes\Tema1\assets\css\extras\animate.min.css" />
    <Content Include="App_Themes\Tema1\assets\css\icons\fontawesome\fonts\fontawesome-webfont.svg" />
    <Content Include="App_Themes\Tema1\assets\css\icons\fontawesome\styles.min.css" />
    <Content Include="App_Themes\Tema1\assets\css\icons\icomoon\fonts\icomoon.svg" />
    <Content Include="App_Themes\Tema1\assets\css\icons\icomoon\styles.min.css" />
    <Content Include="App_Themes\Tema1\assets\css\icons\phosphor\fonts\Phosphor.svg" />
    <Content Include="App_Themes\Tema1\assets\css\icons\phosphor\styles.min.css" />
    <Content Include="App_Themes\Tema1\assets\css\layout.css" />
    <Content Include="App_Themes\Tema1\assets\css\layout.min.css" />
    <Content Include="App_Themes\Tema1\assets\css\SiteMaster.css" />
    <Content Include="App_Themes\Tema1\img\favicon - EPAS %281%29.jpg" />
    <Content Include="App_Themes\Tema1\img\logo_white_epas.png" />
    <Content Include="App_Themes\Tema1\img\Navbar-Img_-_EPAS.jpg" />
    <Content Include="App_Themes\Tema1\sweetalerts\sweetalert.min.css" />
    <Content Include="App_Themes\Tema1\assets\js\app.js" />
    <Content Include="App_Themes\Tema1\assets\js\bootstrap.bundle.min.js" />
    <Content Include="App_Themes\Tema1\assets\js\custom.js" />
    <Content Include="App_Themes\Tema1\assets\js\jquery.min.js" />
    <Content Include="App_Themes\Tema1\assets\js\js_CargaPagos\demo_configurator.js" />
    <Content Include="App_Themes\Tema1\assets\js\js_CargaPagos\prism.min.js" />
    <Content Include="App_Themes\Tema1\sweetalerts\extra_sweetalert.js" />
    <Content Include="App_Themes\Tema1\img\check.png" />
    <Content Include="App_Themes\Tema1\img\favicon - EPAS.jpg" />
    <Content Include="App_Themes\Tema1\img\favicon.png" />
    <Content Include="App_Themes\Tema1\img\icon_file.png" />
    <Content Include="App_Themes\Tema1\img\DitycLogo.jpg" />
    <Content Include="App_Themes\Tema1\img\logo1.jpg" />
    <Content Include="App_Themes\Tema1\img\logo_white.png" />
    <Content Include="App_Themes\Tema1\img\Navbar-Img - EPAS.jpg" />
    <Content Include="App_Themes\Tema1\img\no.png" />
    <Content Include="App_Themes\Tema1\img\pdf_icon.png" />
    <Content Include="App_Themes\Tema1\img\sinimagen.jpg" />
    <Content Include="App_Themes\Tema1\sweetalerts\sweet_alert.min.js" />
    <Content Include="bin\hibernate.cfg.xml" />
    <Content Include="CheckPwd.aspx" />
    <Content Include="Clientes\AddEditContrato.aspx" />
    <Content Include="Clientes\BuscarContrato.aspx" />
    <Content Include="Clientes\Guarda.aspx" />
    <Content Include="Config\AddEditRoles.aspx" />
    <Content Include="Config\AddEditUsuario.aspx" />
    <Content Include="Config\Roles.aspx" />
    <Content Include="Config\Usuarios.aspx" />
    <Content Include="EPAS\AddEdit\AddEditProtocolos.aspx" />
    <Content Include="EPAS\AddEdit\AddEditSubFuentes.aspx" />
    <Content Include="EPAS\AddEdit\AddEditTiposDeFuentes.aspx" />
    <Content Include="EPAS\AddEdit\AddEditDepartamentos.aspx" />
    <Content Include="EPAS\AddEdit\AddEditMetodosdeMedicion.aspx" />
    <Content Include="EPAS\AddEdit\AddEditProtocolosTipo.aspx" />
    <Content Include="EPAS\AddEdit\AddEditTiposDeAnalisis.aspx" />
    <Content Include="EPAS\AddEdit\AddEditFuentes.aspx" />
    <Content Include="EPAS\AddEdit\AddEditLocalidades.aspx" />
    <Content Include="EPAS\AddEdit\AddEditSolicitantes.aspx" />
    <Content Include="EPAS\AddEdit\AddEditDeterminaciones.aspx" />
    <Content Include="EPAS\AddEdit\AddEditZonas.aspx" />
    <Content Include="EPAS\AddEdit\AddEditPlantilla.aspx" />
    <Content Include="EPAS\Lista\ProtocolosAprobados.aspx" />
    <Content Include="EPAS\Lista\Provincias.aspx" />
    <Content Include="EPAS\Lista\Solicitantes.aspx" />
    <Content Include="EPAS\Lista\MetodosdeMedicion.aspx" />
    <Content Include="EPAS\Lista\SubFuentes.aspx" />
    <Content Include="EPAS\Lista\TiposDeAnalisis.aspx" />
    <Content Include="EPAS\Lista\ProtocolosTipo.aspx" />
    <Content Include="EPAS\Lista\Determinaciones.aspx" />
    <Content Include="EPAS\Lista\Protocolos.aspx" />
    <Content Include="EPAS\Lista\Plantilla.aspx" />
    <Content Include="EPAS\Lista\ProtocolosRevision.aspx" />
    <Content Include="EPAS\Lista\Zonas.aspx" />
    <Content Include="EPAS\Lista\TiposDeFuentes.aspx" />
    <Content Include="EPAS\Lista\Fuentes.aspx" />
    <Content Include="EPAS\Lista\Departamentos.aspx" />
    <Content Include="EPAS\Lista\Localidades.aspx" />
    <Content Include="Global.asax">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="GrillaAgustin.aspx" />
    <Content Include="Impresion.aspx" />
    <Content Include="Index.aspx" />
    <Content Include="index.js" />
    <Content Include="InterfazCaja\CargaDePagos.aspx" />
    <Content Include="Login.aspx" />
    <Content Include="Logout.aspx" />
    <Content Include="PRUEBA.aspx" />
    <Content Include="ResetPwd.aspx" />
    <Content Include="Util\UploadedFilesContainer_files.ascx" />
    <Content Include="Util\UploadedFilesContainer_certificado.ascx" />
    <Content Include="Util\UploadedFilesContainer_imagenes.ascx" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CheckPwd.aspx.cs">
      <DependentUpon>CheckPwd.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="CheckPwd.aspx.designer.cs">
      <DependentUpon>CheckPwd.aspx</DependentUpon>
    </Compile>
    <Compile Include="Clientes\AddEditContrato.aspx.cs">
      <DependentUpon>AddEditContrato.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Clientes\AddEditContrato.aspx.designer.cs">
      <DependentUpon>AddEditContrato.aspx</DependentUpon>
    </Compile>
    <Compile Include="Clientes\BuscarContrato.aspx.cs">
      <DependentUpon>BuscarContrato.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Clientes\BuscarContrato.aspx.designer.cs">
      <DependentUpon>BuscarContrato.aspx</DependentUpon>
    </Compile>
    <Compile Include="Clientes\Guarda.aspx.cs">
      <DependentUpon>Guarda.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Clientes\Guarda.aspx.designer.cs">
      <DependentUpon>Guarda.aspx</DependentUpon>
    </Compile>
    <Compile Include="Config\AddEditRoles.aspx.cs">
      <DependentUpon>AddEditRoles.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Config\AddEditRoles.aspx.designer.cs">
      <DependentUpon>AddEditRoles.aspx</DependentUpon>
    </Compile>
    <Compile Include="Config\AddEditUsuario.aspx.cs">
      <DependentUpon>AddEditUsuario.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Config\AddEditUsuario.aspx.designer.cs">
      <DependentUpon>AddEditUsuario.aspx</DependentUpon>
    </Compile>
    <Compile Include="Config\Roles.aspx.cs">
      <DependentUpon>Roles.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Config\Roles.aspx.designer.cs">
      <DependentUpon>Roles.aspx</DependentUpon>
    </Compile>
    <Compile Include="Config\Usuarios.aspx.cs">
      <DependentUpon>Usuarios.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Config\Usuarios.aspx.designer.cs">
      <DependentUpon>Usuarios.aspx</DependentUpon>
    </Compile>
    <Compile Include="EPAS\AddEdit\AddEditProtocolos.aspx.cs">
      <DependentUpon>AddEditProtocolos.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="EPAS\AddEdit\AddEditProtocolos.aspx.designer.cs">
      <DependentUpon>AddEditProtocolos.aspx</DependentUpon>
    </Compile>
    <Compile Include="EPAS\AddEdit\AddEditSubFuentes.aspx.cs">
      <DependentUpon>AddEditSubFuentes.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="EPAS\AddEdit\AddEditSubFuentes.aspx.designer.cs">
      <DependentUpon>AddEditSubFuentes.aspx</DependentUpon>
    </Compile>
    <Compile Include="EPAS\AddEdit\AddEditTiposDeFuentes.aspx.cs">
      <DependentUpon>AddEditTiposDeFuentes.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="EPAS\AddEdit\AddEditTiposDeFuentes.aspx.designer.cs">
      <DependentUpon>AddEditTiposDeFuentes.aspx</DependentUpon>
    </Compile>
    <Compile Include="EPAS\AddEdit\AddEditDepartamentos.aspx.cs">
      <DependentUpon>AddEditDepartamentos.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="EPAS\AddEdit\AddEditDepartamentos.aspx.designer.cs">
      <DependentUpon>AddEditDepartamentos.aspx</DependentUpon>
    </Compile>
    <Compile Include="EPAS\AddEdit\AddEditMetodosdeMedicion.aspx.cs">
      <DependentUpon>AddEditMetodosdeMedicion.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="EPAS\AddEdit\AddEditMetodosdeMedicion.aspx.designer.cs">
      <DependentUpon>AddEditMetodosdeMedicion.aspx</DependentUpon>
    </Compile>
    <Compile Include="EPAS\AddEdit\AddEditProtocolosTipo.aspx.cs">
      <DependentUpon>AddEditProtocolosTipo.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="EPAS\AddEdit\AddEditProtocolosTipo.aspx.designer.cs">
      <DependentUpon>AddEditProtocolosTipo.aspx</DependentUpon>
    </Compile>
    <Compile Include="EPAS\AddEdit\AddEditTiposDeAnalisis.aspx.cs">
      <DependentUpon>AddEditTiposDeAnalisis.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="EPAS\AddEdit\AddEditTiposDeAnalisis.aspx.designer.cs">
      <DependentUpon>AddEditTiposDeAnalisis.aspx</DependentUpon>
    </Compile>
    <Compile Include="EPAS\AddEdit\AddEditFuentes.aspx.cs">
      <DependentUpon>AddEditFuentes.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="EPAS\AddEdit\AddEditFuentes.aspx.designer.cs">
      <DependentUpon>AddEditFuentes.aspx</DependentUpon>
    </Compile>
    <Compile Include="EPAS\AddEdit\AddEditLocalidades.aspx.cs">
      <DependentUpon>AddEditLocalidades.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="EPAS\AddEdit\AddEditLocalidades.aspx.designer.cs">
      <DependentUpon>AddEditLocalidades.aspx</DependentUpon>
    </Compile>
    <Compile Include="EPAS\AddEdit\AddEditSolicitantes.aspx.cs">
      <DependentUpon>AddEditSolicitantes.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="EPAS\AddEdit\AddEditSolicitantes.aspx.designer.cs">
      <DependentUpon>AddEditSolicitantes.aspx</DependentUpon>
    </Compile>
    <Compile Include="EPAS\AddEdit\AddEditDeterminaciones.aspx.cs">
      <DependentUpon>AddEditDeterminaciones.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="EPAS\AddEdit\AddEditDeterminaciones.aspx.designer.cs">
      <DependentUpon>AddEditDeterminaciones.aspx</DependentUpon>
    </Compile>
    <Compile Include="EPAS\AddEdit\AddEditZonas.aspx.cs">
      <DependentUpon>AddEditZonas.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="EPAS\AddEdit\AddEditZonas.aspx.designer.cs">
      <DependentUpon>AddEditZonas.aspx</DependentUpon>
    </Compile>
    <Compile Include="EPAS\AddEdit\AddEditPlantilla.aspx.cs">
      <DependentUpon>AddEditPlantilla.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="EPAS\AddEdit\AddEditPlantilla.aspx.designer.cs">
      <DependentUpon>AddEditPlantilla.aspx</DependentUpon>
    </Compile>
    <Compile Include="EPAS\Lista\ProtocolosAprobados.aspx.cs">
      <DependentUpon>ProtocolosAprobados.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="EPAS\Lista\ProtocolosAprobados.aspx.designer.cs">
      <DependentUpon>ProtocolosAprobados.aspx</DependentUpon>
    </Compile>
    <Compile Include="EPAS\Lista\Provincias.aspx.cs">
      <DependentUpon>Provincias.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="EPAS\Lista\Provincias.aspx.designer.cs">
      <DependentUpon>Provincias.aspx</DependentUpon>
    </Compile>
    <Compile Include="EPAS\Lista\Solicitantes.aspx.cs">
      <DependentUpon>Solicitantes.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="EPAS\Lista\Solicitantes.aspx.designer.cs">
      <DependentUpon>Solicitantes.aspx</DependentUpon>
    </Compile>
    <Compile Include="EPAS\Lista\MetodosdeMedicion.aspx.cs">
      <DependentUpon>MetodosdeMedicion.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="EPAS\Lista\MetodosdeMedicion.aspx.designer.cs">
      <DependentUpon>MetodosdeMedicion.aspx</DependentUpon>
    </Compile>
    <Compile Include="EPAS\Lista\SubFuentes.aspx.cs">
      <DependentUpon>SubFuentes.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="EPAS\Lista\SubFuentes.aspx.designer.cs">
      <DependentUpon>SubFuentes.aspx</DependentUpon>
    </Compile>
    <Compile Include="EPAS\Lista\TiposDeAnalisis.aspx.cs">
      <DependentUpon>TiposDeAnalisis.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="EPAS\Lista\TiposDeAnalisis.aspx.designer.cs">
      <DependentUpon>TiposDeAnalisis.aspx</DependentUpon>
    </Compile>
    <Compile Include="EPAS\Lista\ProtocolosTipo.aspx.cs">
      <DependentUpon>ProtocolosTipo.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="EPAS\Lista\ProtocolosTipo.aspx.designer.cs">
      <DependentUpon>ProtocolosTipo.aspx</DependentUpon>
    </Compile>
    <Compile Include="EPAS\Lista\Determinaciones.aspx.cs">
      <DependentUpon>Determinaciones.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="EPAS\Lista\Determinaciones.aspx.designer.cs">
      <DependentUpon>Determinaciones.aspx</DependentUpon>
    </Compile>
    <Compile Include="EPAS\Lista\Protocolos.aspx.cs">
      <DependentUpon>Protocolos.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="EPAS\Lista\Protocolos.aspx.designer.cs">
      <DependentUpon>Protocolos.aspx</DependentUpon>
    </Compile>
    <Compile Include="EPAS\Lista\Plantilla.aspx.cs">
      <DependentUpon>Plantilla.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="EPAS\Lista\Plantilla.aspx.designer.cs">
      <DependentUpon>Plantilla.aspx</DependentUpon>
    </Compile>
    <Compile Include="EPAS\Lista\ProtocolosRevision.aspx.cs">
      <DependentUpon>ProtocolosRevision.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="EPAS\Lista\ProtocolosRevision.aspx.designer.cs">
      <DependentUpon>ProtocolosRevision.aspx</DependentUpon>
    </Compile>
    <Compile Include="EPAS\Lista\Zonas.aspx.cs">
      <DependentUpon>Zonas.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="EPAS\Lista\Zonas.aspx.designer.cs">
      <DependentUpon>Zonas.aspx</DependentUpon>
    </Compile>
    <Compile Include="EPAS\Lista\TiposDeFuentes.aspx.cs">
      <DependentUpon>TiposDeFuentes.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="EPAS\Lista\TiposDeFuentes.aspx.designer.cs">
      <DependentUpon>TiposDeFuentes.aspx</DependentUpon>
    </Compile>
    <Compile Include="EPAS\Lista\Fuentes.aspx.cs">
      <DependentUpon>Fuentes.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="EPAS\Lista\Fuentes.aspx.designer.cs">
      <DependentUpon>Fuentes.aspx</DependentUpon>
    </Compile>
    <Compile Include="EPAS\Lista\Departamentos.aspx.cs">
      <DependentUpon>Departamentos.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="EPAS\Lista\Departamentos.aspx.designer.cs">
      <DependentUpon>Departamentos.aspx</DependentUpon>
    </Compile>
    <Compile Include="EPAS\Lista\Localidades.aspx.cs">
      <DependentUpon>Localidades.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="EPAS\Lista\Localidades.aspx.designer.cs">
      <DependentUpon>Localidades.aspx</DependentUpon>
    </Compile>
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
    <Compile Include="GrillaAgustin.aspx.cs">
      <DependentUpon>GrillaAgustin.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="GrillaAgustin.aspx.designer.cs">
      <DependentUpon>GrillaAgustin.aspx</DependentUpon>
    </Compile>
    <Compile Include="Impresion.aspx.cs">
      <DependentUpon>Impresion.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Impresion.aspx.designer.cs">
      <DependentUpon>Impresion.aspx</DependentUpon>
    </Compile>
    <Compile Include="Index.aspx.cs">
      <DependentUpon>Index.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Index.aspx.designer.cs">
      <DependentUpon>Index.aspx</DependentUpon>
    </Compile>
    <Compile Include="InterfazCaja\CargaDePagos.aspx.cs">
      <DependentUpon>CargaDePagos.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="InterfazCaja\CargaDePagos.aspx.designer.cs">
      <DependentUpon>CargaDePagos.aspx</DependentUpon>
    </Compile>
    <Compile Include="Login.aspx.cs">
      <DependentUpon>Login.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Login.aspx.designer.cs">
      <DependentUpon>Login.aspx</DependentUpon>
    </Compile>
    <Compile Include="Logout.aspx.cs">
      <DependentUpon>Logout.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Logout.aspx.designer.cs">
      <DependentUpon>Logout.aspx</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Settings.resx</DependentUpon>
    </Compile>
    <Compile Include="PRUEBA.aspx.cs">
      <DependentUpon>PRUEBA.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="PRUEBA.aspx.designer.cs">
      <DependentUpon>PRUEBA.aspx</DependentUpon>
    </Compile>
    <Compile Include="ResetPwd.aspx.cs">
      <DependentUpon>ResetPwd.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ResetPwd.aspx.designer.cs">
      <DependentUpon>ResetPwd.aspx</DependentUpon>
    </Compile>
    <Compile Include="Site.Master.cs">
      <DependentUpon>Site.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Site.Master.designer.cs">
      <DependentUpon>Site.Master</DependentUpon>
    </Compile>
    <Compile Include="Util\Helpers.cs" />
    <Compile Include="Util\UploadedFilesContainer_files.ascx.cs">
      <DependentUpon>UploadedFilesContainer_files.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Util\UploadedFilesContainer_files.ascx.designer.cs">
      <DependentUpon>UploadedFilesContainer_files.ascx</DependentUpon>
    </Compile>
    <Compile Include="Util\UploadedFilesContainer_certificado.ascx.cs">
      <DependentUpon>UploadedFilesContainer_certificado.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Util\UploadedFilesContainer_certificado.ascx.designer.cs">
      <DependentUpon>UploadedFilesContainer_certificado.ascx</DependentUpon>
    </Compile>
    <Compile Include="Util\UploadedFilesContainer_imagenes.ascx.cs">
      <DependentUpon>UploadedFilesContainer_imagenes.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Util\UploadedFilesContainer_imagenes.ascx.designer.cs">
      <DependentUpon>UploadedFilesContainer_imagenes.ascx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Content Include="App_Themes\Tema1\assets\css\icons\fontawesome\fonts\fontawesome-webfont.eot" />
    <Content Include="App_Themes\Tema1\assets\css\icons\fontawesome\fonts\fontawesome-webfont.ttf" />
    <Content Include="App_Themes\Tema1\assets\css\icons\fontawesome\fonts\fontawesome-webfont.woff" />
    <Content Include="App_Themes\Tema1\assets\css\icons\fontawesome\fonts\fontawesome-webfont.woff2" />
    <Content Include="App_Themes\Tema1\assets\css\icons\fontawesome\fonts\FontAwesome.otf" />
    <Content Include="App_Themes\Tema1\assets\css\icons\icomoon\fonts\icomoon.eot" />
    <Content Include="App_Themes\Tema1\assets\css\icons\icomoon\fonts\icomoon.ttf" />
    <Content Include="App_Themes\Tema1\assets\css\icons\icomoon\fonts\icomoon.woff" />
    <Content Include="package.json" />
    <Content Include="libman.json" />
    <Content Include="App_Themes\Tema1\assets\css\icons\phosphor\fonts\Phosphor.ttf" />
    <Content Include="App_Themes\Tema1\assets\css\icons\phosphor\fonts\Phosphor.woff" />
    <Content Include="App_Themes\Tema1\assets\css\icons\phosphor\selection.json" />
    <None Include="packages.config">
      <SubType>Designer</SubType>
    </None>
    <Content Include="Site.Master" />
    <None Include="Properties\PublishProfiles\FolderProfile.pubxml" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
    <Folder Include="App_Themes\Tema1\menu\" />
    <Folder Include="Archivos\Certificados\" />
    <Folder Include="Archivos\Files\" />
    <Folder Include="Archivos\Imagenes\" />
    <Folder Include="TEMP\" />
    <Folder Include="uploads\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\business\Business.csproj">
      <Project>{f02fe28f-4ae8-40a4-b318-3eb39dff7156}</Project>
      <Name>Business</Name>
    </ProjectReference>
    <ProjectReference Include="..\common\Common.csproj">
      <Project>{b240bc08-fbe1-46b6-93e1-f1146a829c4c}</Project>
      <Name>Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\domain\Domain.csproj">
      <Project>{a1e15de3-73e9-42a5-8e4c-41753f59dbe4}</Project>
      <Name>Domain</Name>
    </ProjectReference>
    <ProjectReference Include="..\Reportes\Reportes.csproj">
      <Project>{EBE38D8F-B25F-453E-A73C-72EAA5C302E6}</Project>
      <Name>Reportes</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\licenses.licx" />
    <EmbeddedResource Include="Properties\Settings.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <PlatformTarget>x86</PlatformTarget>
    <OutputPath>bin\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <PlatformTarget>x86</PlatformTarget>
    <OutputPath>bin\</OutputPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>3800</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:3800/</IISUrl>
          <OverrideIISAppRootUrl>True</OverrideIISAppRootUrl>
          <IISAppRootUrl>http://localhost:3800/</IISAppRootUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>Este proyecto hace referencia a los paquetes NuGet que faltan en este equipo. Use la restauración de paquetes NuGet para descargarlos. Para obtener más información, consulte http://go.microsoft.com/fwlink/?LinkID=322105. El archivo que falta es {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>