﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 6/11/2023 16:03:54
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Collections.Generic;

namespace Domain
{
    public partial interface IMetodosdeMedicionRepository : IRepository<MetodosdeMedicion>
    {
        ICollection<MetodosdeMedicion> GetAll();
        MetodosdeMedicion GetByKey(int _Id);
    }
}
