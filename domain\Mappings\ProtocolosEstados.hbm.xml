﻿<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="ProtocolosEstados" table="Protocolos_Estados">
    <id name="Id" type="Int32">
      <column name="id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Codigo" type="String">
      <column name="codigo" not-null="false" length="50" sql-type="nvarchar" />
    </property>
    <property name="Nombre" type="String">
      <column name="nombre" not-null="false" length="150" sql-type="nvarchar" />
    </property>
    <property name="Activo" type="Boolean">
      <column name="activo" default="1" not-null="false" sql-type="bit" />
    </property>
    <set name="Protocolos" inverse="true" generic="true">
      <key>
        <column name="idEstado" />
      </key>
      <one-to-many class="Protocolos" />
    </set>
  </class>
</hibernate-mapping>