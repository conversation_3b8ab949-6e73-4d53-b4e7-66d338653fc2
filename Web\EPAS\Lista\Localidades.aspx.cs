﻿using Business.Provincias;
using Business.Services.Epas;
using Business.Services.Provincias;
using DevExpress.Web;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web.EPAS.Lista
{
    public partial class Localidades : System.Web.UI.Page
    {
       

        protected void Page_Load(object sender,EventArgs e)
        {
            try 
            {

                //CargarDatosEnGv_Localidades();
                LlenadoDrop_Provincias();

                if (!IsPostBack) 
                {

                }
            }
            catch(Exception ex) 
            {
                string g = ex.Message;
            }
        }

        protected void LlenadoDrop_Provincias()
        {
            cmbProvincias.DataSource = ProvinciasService.GetAll();
            cmbProvincias.TextField = "Descripcion";
            cmbProvincias.ValueField = "Id";
            cmbProvincias.DataBind();
            cmbProvincias.Items.Insert(0, new ListEditItem());

        }

        protected void cmbProvincias_SelectedIndexChanged(object sender, EventArgs e)
        {
            // Obtén el valor seleccionado del DropDownList
            string provinciaId = cmbProvincias.Value.ToString();

            // Si el valor no es nulo o vacío, carga las localidades según la provincia seleccionada
            if (!string.IsNullOrEmpty(provinciaId))
            {
                CargarLocalidadesPorProvincia(provinciaId);
            }
            else
            {
                // Puedes manejar una lógica diferente si el valor de la provincia es nulo o vacío
                // Por ejemplo, limpiar la GridView o mostrar un mensaje de error.
                gv_Localidades.DataSource = null;
                gv_Localidades.DataBind();
            }
        }

        private void CargarLocalidadesPorProvincia(string provinciaId)
        {
            // Obtén y carga las localidades de la provincia seleccionada
            List<Domain.Localidades> localidades = (List<Domain.Localidades>)Business.Services.Provincias.LocalidadesService.GetByProvincias(Convert.ToInt32(provinciaId));
            gv_Localidades.DataSource = localidades;
            gv_Localidades.DataBind();
        }

        private void CargarDatosEnGv_Localidades()
        {
            try {

                List<Domain.Localidades> localidades = (List<Domain.Localidades>)
                    Business.Services.Provincias.LocalidadesService.GetAll();
                gv_Localidades.DataSource = localidades;
                gv_Localidades.DataBind();
                
            } catch(Exception ex) {
                string g = ex.Message;
            }

        }


        protected void btnEditar_Click(object sender,EventArgs e) {

            try {
                ASPxButton btn = (ASPxButton) sender;
                if(btn != null) {
                    GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer) btn.NamingContainer;
                    if(c != null) {
                        var ind = DataBinder.Eval(c.DataItem,"Id");
                        if(ind != null) {

                            Domain.Localidades localidad = LocalidadesService.GetById(Convert.ToInt32(ind));

                            if(localidad.Activo == false)
                            {
                                string message = string.Format("La Localidad {0} no se puede editar ya que está anulada.",localidad.Nombre);
                                ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('" + message + "')",true);

                            } else
                            {
                                Response.Redirect(Global.ApplicationPath + "/EPAS/AddEdit/AddEditLocalidades.aspx?id=" + Util.Helpers.Encrypt(ind.ToString()),false);
                            }

                        }
                    }
                }
            } catch(Exception ex) {
                ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('Error al tratar de editar un elemento, consulte al administrador del sistema', 'error');",true);
            }
        }

        protected void btn_filtro_Click(object sender, EventArgs e)
        {

        }

        protected void btn_resetFiltro_Click(object sender, EventArgs e)
        {
            Session.Remove("filtro_localidad");

            Response.Redirect(Global.ApplicationPath + "/EPAS/Lista/Localidades.aspx", false);
        }
    }
}