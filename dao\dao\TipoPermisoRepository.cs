﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 18/03/2019 13:19:19
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class TipoPermisoRepository : NHibernateRepository<Domain.TipoPermiso>, ITipoPermisoRepository
    {
        public TipoPermisoRepository(ISession session) : base(session)
        {
        }

        public virtual IList<Domain.TipoPermiso> GetAll()
        {
            return session.CreateQuery(string.Format("from TipoPermiso")).List<Domain.TipoPermiso>();
        }

        public virtual Domain.TipoPermiso GetByCodigo(string codigo)
        {
            return session.CreateQuery(string.Format("from TipoPermiso where Codigo=:codigo")).SetParameter("codigo",codigo).UniqueResult<Domain.TipoPermiso>();
        }

        public virtual Domain.TipoPermiso GetByKey(int _Id)
        {
            return session.Get<Domain.TipoPermiso>(_Id);
        }
    }
}
