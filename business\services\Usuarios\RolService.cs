using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;
using DAO;
using Common.DDL;
using Domain;
using NHibernate;

namespace Business.Services.Usuarios
{
    public class RolService
    {

        public static IList<Rol> GetAll()
        {
            IList<Rol> listaAux;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                listaAux = (IList<Rol>)new RolRepository(sess).GetAll();
                sess.Close();
                sess.Dispose();
            }

            return (listaAux);
        }

        public static Rol getById(int id)
        {
            Rol u;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                u = new RolRepository(sess).GetByKey(id);

                if (u != null)
                {
                    NHibernateUtil.Initialize(u.Usuarios);
                    NHibernateUtil.Initialize(u.RolModulos);
                }
                sess.Close();
                sess.Dispose();
                return u;
            }
        }

        public static Rol getByCodigo(string code)
        {
            Rol u;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                u = new RolRepository(sess).GetByCodigo(code);

                if (u != null)
                {
                 
              
                }
                sess.Close();
                sess.Dispose();
                return u;
            }
        }

        public static List<ItemCombo> getAllDDL()
        {
            IList<Rol> lista;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                lista = (IList<Rol>)new RolRepository(sess).GetAll();
                sess.Close();
                sess.Dispose();
            }

            ItemCombo item;
            ListaCombo listaS = new ListaCombo();
            foreach (Rol p in lista)
            {
                item = new ItemCombo();
                item.ID = p.Id.ToString();
                item.Descripcion = p.Nombre;
                listaS.Add(item);
            }
           
            return (listaS);
        }

        public static int GetByNombre(int idRol, Rol m)
        {
            Rol mar;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                mar = new RolRepository(sess).GetByNombre(idRol, m);
                sess.Close();
                sess.Dispose();
                if (mar != null)
                {
                    return 1;//Descripcion existente
                }
                else
                {
                    return 0;
                }
               
            }
        }

        public static int agregar_Rol(int idRol, Rol m)
        {
            int value_Return = 0;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                switch (GetByNombre(idRol, m))
                {
                    case 0:
                        if (idRol != -1)
                        {
                            
                            m.Id = idRol;
                        }
                        else
                        {
                            Modulo a = ModuloService.getById(1);
                            RolModulo ra = new RolModulo();
                            ra.Modulo = a;
                            m.RolModulos.Add(ra);
                        }
                        m.Editable = true;
                        m.Eliminable = true;
                        m.Renombrable = true;
                        RolService.SaveOrUpdate(m);
                        break;
                    case 1:
                        value_Return = 1;
                        break;
                }
                sess.Close();
                sess.Dispose();
                return value_Return;
            }

        }

        public static void SaveOrUpdate(Rol u)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new RolRepository(sess).Add(u);

                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }

            }
        }

        public static void Delete(Rol u)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new RolRepository(sess).Remove(u);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }

            }
        }

    }

}
