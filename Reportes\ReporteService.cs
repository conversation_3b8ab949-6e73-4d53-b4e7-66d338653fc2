﻿
using Business.Services.Epas;
using Business.Services.Usuarios;
using Domain;
using iTextSharp.text;
using iTextSharp.text.pdf;
using Org.BouncyCastle.Asn1.Cms;
using QRCoder;
using Reportes.xsd;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web;


namespace Reportes
{
    public class ReporteService
    {
        public static MemoryStream GetProtocolo(int idProtocolos)
        {
            MemoryStream _ms = new MemoryStream();
            try
            {
                Protocolos eg = ProtocolosService.GetById(idProtocolos); 

                if (eg != null)
                {
                    ds_protocolo ds = new ds_protocolo();
                    string subfuente = string.Empty;
                    if (eg.SubFuentes != null)
                    {
                        subfuente = eg.SubFuentes.Descripcion;
                    }
                    string nombreTomador = string.Empty;
                    if (eg.Contratos_IdContratoTomador != null)
                    {
                        nombreTomador = eg.Contratos_IdContratoTomador.Nombre;
                    }
                    ds.Protocolo.AddProtocoloRow(eg.Id,Convert.ToInt32(eg.NroProtocolo), Convert.ToDateTime(eg.FechaToma), Convert.ToDateTime(eg.RecepcionToma),  eg.ObservacionSitioToma, eg.Expediente, eg.Fuentes.NroDeFuente.ToString(), eg.Fuentes.Descripcion, subfuente, eg.Fuentes.Localidades.Nombre, eg.Fuentes.Localidades.Departamentos.Descripcion, eg.ProtocolosEstados.Nombre, eg.Contratos_IdContratoSolicitante.Nombre, nombreTomador, eg.TipoAnalisis.Descripcion);

                    if (eg.ProtocoloDeterminaciones != null && eg.ProtocoloDeterminaciones.Count > 0)
                    {
                        int count = 1;
                        foreach (ProtocoloDeterminaciones eh in eg.ProtocoloDeterminaciones )
                        {

                            ds.Determinaciones.AddDeterminacionesRow(eh.Id, Convert.ToInt32(eh.Determinaciones.NroDeterminacion), 
                                eh.Determinaciones.Descripcion, eh.Determinaciones.UnidadPrimaria, eh.Determinaciones.UnidadSecundaria, 
                                Convert.ToDecimal(eh.Determinaciones.FactorConversionPrefijo), 
                                Convert.ToDecimal(eh.Determinaciones.FactorConversionSufijo), 
                                eh.Determinaciones.ValorReferencia, Convert.ToInt32(eh.Determinaciones.DecimalesMostrar), 
                                Convert.ToBoolean(eh.Determinaciones.Activo), Convert.ToDecimal(eh.Resultado), eh.Signo, 
                                eh.Protocolos.TipoAnalisis.Descripcion, eh.Observacion);
                              
                             
                                    count++;
                                
                            
                        }
                    }


                    Reportes.rptProtocolo rpt_checklist = new Reportes.rptProtocolo();
                    rpt_checklist.SetDataSource(ds);

                    rpt_checklist.SummaryInfo.ReportTitle = "Protocolo-" + eg.NroProtocolo + "-" + eg.Contratos_IdContratoSolicitante + ".pdf";



                    rpt_checklist.ExportToStream(CrystalDecisions.Shared.ExportFormatType.PortableDocFormat).CopyTo(_ms);

                    rpt_checklist.Close();
                    rpt_checklist.Dispose();
                }
                else
                {
                    throw new Exception("Protocolo no encontrado");
                }

            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
            return _ms;
        }



    }
}
