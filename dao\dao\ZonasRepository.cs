﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 26/9/2023 16:47:39
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;
using NHibernate.Mapping;

namespace DAO
{
    public partial class ZonasRepository : NHibernateRepository<Domain.Zonas>, IZonasRepository
    {
        public ZonasRepository(ISession session) : base(session)
        {
        }

        public virtual List<Domain.Zonas> GetAll()
        {
            return (List<Zonas>)session.CreateQuery(string.Format("from Zonas ORDER BY Descripcion asc")).List<Domain.Zonas>();
        }

        public virtual Domain.Zonas GetByKey(int _Id)
        {
            return session.Get<Domain.Zonas>(_Id);
        }

        public IList<Zonas> GetByLocalidades(int idLocalidades)
        {
            return session.CreateQuery(string.Format("from Zonas where Localidades.Id=:idLocalidades ORDER BY Nombre asc"))
                .SetParameter("idLocalidades", idLocalidades)
                .List<Domain.Zonas>();
        }

        public virtual Domain.Zonas ExisteCodigo(string stringCodigo)
        {
            string hql = "from Zonas where  upper(Codigo)=:stringCodigo  and Activo=true";
            return session.CreateQuery(hql)

          .SetParameter("stringCodigo", stringCodigo.ToUpper())
          .UniqueResult<Domain.Zonas>();
        }
        public virtual bool ExisteNombre(string Descripcion)
        {
            string hql = "select count(*) from Zonas where upper(Descripcion) = :Descripcion and Activo = true";
            long count = session.CreateQuery(hql)
                .SetParameter("Descripcion", Descripcion.ToUpper())
                .UniqueResult<long>();

            return count > 0;
        }
        ICollection<Zonas> IZonasRepository.GetAll()
        {
            throw new NotImplementedException();
        }
    }
}


