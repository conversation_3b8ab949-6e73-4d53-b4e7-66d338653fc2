﻿<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="Recibos" table="Recibos">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Descripcion" type="String">
      <column name="Descripcion" not-null="false" sql-type="nvarchar" />
    </property>
    <property name="Importe" type="Decimal">
      <column name="Importe" not-null="false" precision="19" scale="4" sql-type="money" />
    </property>
    <property name="ValorOtraMoneda" type="Decimal">
      <column name="ValorOtraMoneda" not-null="false" precision="19" scale="4" sql-type="money" />
    </property>
    <property name="NombrePagador" type="String">
      <column name="NombrePagador" not-null="false" length="20" sql-type="nvarchar" />
    </property>
    <property name="Vuelto" type="Decimal">
      <column name="Vuelto" not-null="true" precision="19" scale="4" sql-type="money" />
    </property>
    <property name="Intereses" type="Decimal">
      <column name="Intereses" not-null="false" precision="19" scale="4" sql-type="money" />
    </property>
    <property name="Recargos" type="Decimal">
      <column name="Recargos" not-null="false" precision="19" scale="4" sql-type="money" />
    </property>
    <property name="ModificadoManual" type="Boolean">
      <column name="ModificadoManual" not-null="false" sql-type="bit" />
    </property>
    <property name="IdMonedaDePago" type="Int32">
      <column name="IdMonedaDePago" not-null="false" precision="10" scale="0" sql-type="int" />
    </property>
    <property name="IdTipoRecibo" type="Int32">
      <column name="IdTipoRecibo" not-null="true" precision="10" scale="0" sql-type="int" />
    </property>
    <many-to-one name="Comprobantes" class="Comprobantes">
      <column name="IdComprobante" not-null="true" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <many-to-one name="Cajas" class="Cajas">
      <column name="IdCaja" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <set name="Transferencias" inverse="true" generic="true">
      <key>
        <column name="IdRecibo" />
      </key>
      <one-to-many class="Transferencias" />
    </set>
  </class>
</hibernate-mapping>