//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 25/9/2023 14:39:43
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.Monedas, Domain in the schema.
    /// </summary>
    public partial class Monedas {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for Monedas constructor in the schema.
        /// </summary>
        public Monedas()
        {
            this.Cheques = new HashSet<Cheques>();
            this.Comprobantes = new HashSet<Comprobantes>();
            this.Contratos = new HashSet<Contratos>();
            this.CuponesTarjetas = new HashSet<CuponesTarjetas>();
            this.Efectivos = new HashSet<Efectivo>();
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Descripcion in the schema.
        /// </summary>
        public virtual string Descripcion
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Simbolo in the schema.
        /// </summary>
        public virtual string Simbolo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for MonedaNacional in the schema.
        /// </summary>
        public virtual System.Nullable<short> MonedaNacional
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for DescripcionPlural in the schema.
        /// </summary>
        public virtual string DescripcionPlural
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Cheques in the schema.
        /// </summary>
        public virtual ISet<Cheques> Cheques
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Comprobantes in the schema.
        /// </summary>
        public virtual ISet<Comprobantes> Comprobantes
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Contratos in the schema.
        /// </summary>
        public virtual ISet<Contratos> Contratos
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for CuponesTarjetas in the schema.
        /// </summary>
        public virtual ISet<CuponesTarjetas> CuponesTarjetas
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Efectivos in the schema.
        /// </summary>
        public virtual ISet<Efectivo> Efectivos
        {
            get;
            set;
        }
    }

}
