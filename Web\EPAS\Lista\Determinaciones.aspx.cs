﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Business.Services.Epas;
using Business.Services.Provincias;
using Business.Services.Usuarios;
using DevExpress.Web;

namespace Web.EPAS.Lista
{
    public partial class Determinaciones : System.Web.UI.Page
    {
        protected void Page_Load(object sender,EventArgs e)
        {
            try
            {

                CargarDatosEnGv_Determinaciones();
                if (!IsPostBack)
                {

                }

            }
            catch (Exception ex)
            {
                string g = ex.Message;
            }

        }

        private void CargarDatosEnGv_Determinaciones()
        {


            try
            {

                List<Domain.Determinaciones > departamentos = (List<Domain.Determinaciones>)
                Business.Services.Epas.DeterminacionesService.GetAll();
                gv_Determinaciones.DataSource = departamentos;
                gv_Determinaciones.DataBind();
            }
            catch (Exception ex)
            {
                string g = ex.Message;
            }

        }
        protected void btnEditar_Click(object sender, EventArgs e)
        {
            try
            {
                ASPxButton btn = (ASPxButton)sender;
                if (btn != null)
                {
                    GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer)btn.NamingContainer;
                    if (c != null)
                    {
                        var ind = DataBinder.Eval(c.DataItem, "Id");
                        if (ind != null)
                        {
                            Domain.Determinaciones deter = DeterminacionesService.GetById(Convert.ToInt32(ind));

                            if(deter.Activo == false)
                            {
                                string message = string.Format("La Determinación {0} no se puede editar ya que está anulada.",deter.Descripcion);
                                ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('" + message + "')",true);

                            } else
                            {
                                Response.Redirect(Global.ApplicationPath + "/EPAS/AddEdit/AddEditDeterminaciones.aspx?id=" +
                                    Util.Helpers.Encrypt(ind.ToString()), false);
                            }

                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al tratar de editar un elemento, consulte al administrador del sistema', 'error');", true);
            }

        }
        protected void btnAnularActivar_Click(object sender, EventArgs e)
        {
            try
            {
                ASPxButton btn = (ASPxButton)sender;
                GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer)btn.NamingContainer;
                var ind = DataBinder.Eval(c.DataItem, "Id");

                Domain.Determinaciones DE = DeterminacionesService.GetById(Convert.ToInt32(ind));
                DE.Activo = !DE.Activo;

                DeterminacionesService.SaveOrUpdate(DE);
                CargarDatosEnGv_Determinaciones();

            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al tratar de eliminar un elemento, consulte al administrador del sistema', 'error');", true);

            }
        }
    }
}