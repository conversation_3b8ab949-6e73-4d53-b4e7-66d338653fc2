﻿using System;
using Business.Services.Epas;
using DevExpress.Web;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Business.Services.Provincias;

namespace Web.EPAS.Lista
{
    public partial class ProtocolosTipo : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            CargarDatosEnGv_ProtocolosTipo();
            if (!IsPostBack)
            {

            }
        }
        private void CargarDatosEnGv_ProtocolosTipo()
        {
            try
            {
                List<Domain.PlantillaProtocolos> ProtoTipo = (List<Domain.PlantillaProtocolos>)PlantillaProtocolosService.GetAll();
                gv_ProtocolosTipo.DataSource = ProtoTipo;
                gv_ProtocolosTipo.DataBind();
            }
            catch (Exception ex)
            {
                string g = ex.Message;
            }

        }
        protected void btnEditar_Click(object sender, EventArgs e)
        {
            try
            {
                ASPxButton btn = (ASPxButton)sender;
                if (btn != null)
                {
                    GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer)btn.NamingContainer;
                    if (c != null)
                    {
                        var ind = DataBinder.Eval(c.DataItem, "Id");

                        if (ind != null)
                        {
                            Domain.PlantillaProtocolos PP = PlantillaProtocolosService.GetById(Convert.ToInt32(ind));

                            if(PP.Activo == false)
                            {
                                string message = string.Format("La Plantilla {0} no se puede editar ya que está anulada.",PP.Descripcion);
                                ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('" + message + "')",true);

                            } else
                            {
                                Response.Redirect(Global.ApplicationPath + "/EPAS/AddEdit/AddEditProtocolosTipo.aspx?id=" + Util.Helpers.Encrypt(ind.ToString()), false);
                            }

                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al tratar de editar un elemento, consulte al administrador del sistema', 'error');", true);
            }

        }

        protected void btnAnularActivar_Click(object sender, EventArgs e)
        {
            try
            {
                ASPxButton btn = (ASPxButton)sender;
                GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer)btn.NamingContainer;
                var ind = DataBinder.Eval(c.DataItem, "Id");

                Domain.PlantillaProtocolos PP = PlantillaProtocolosService.GetById(Convert.ToInt32(ind));
                PP.Activo = !PP.Activo;

                PlantillaProtocolosService.SaveOrUpdate(PP);
                CargarDatosEnGv_ProtocolosTipo();

            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al tratar de eliminar un elemento, consulte al administrador del sistema', 'error');", true);

            }
        }
    }
}