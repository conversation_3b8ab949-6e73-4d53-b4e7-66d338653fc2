﻿using DAO;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Epas
{
    public class PlantillaProtocolosService
    {
        //Trae todo
        public static IList<Domain.PlantillaProtocolos> GetAll()
        {

            IList<Domain.PlantillaProtocolos> listaAux;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                listaAux = (IList<Domain.PlantillaProtocolos>)new PlantillaProtocolosRepository(sess).GetAll();
                sess.Close();
                sess.Dispose();
            }
            return (listaAux);

        }
        
        //obtener por id
        public static Domain.PlantillaProtocolos GetById(int id)
        {

            Domain.PlantillaProtocolos p;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {

                p = (Domain.PlantillaProtocolos) new PlantillaProtocolosRepository(sess).GetByKey(id);

                NHibernateUtil.Initialize(p.Determinaciones);
                sess.Close();
                sess.Dispose();
                return p;
            }

        }

        public static ICollection<Domain.PlantillaProtocolos> GetByPlantillaActiva(bool Activo)
        {
            ICollection<Domain.PlantillaProtocolos> plantillasActivas;
            using(ISession sess = NHibernateSessionProvider.GetSession())
            {
                plantillasActivas = new PlantillaProtocolosRepository(sess).GetByPlantillaActiva(true)
                                        .Where(pp => pp.Activo == Activo)
                                        .ToList();
            }
            return plantillasActivas;
        }

        public static bool ExisteCodigo(int numero)
        {

            Domain.PlantillaProtocolos p;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {

                p = (Domain.PlantillaProtocolos)new PlantillaProtocolosRepository(sess).ExisteCodigo(numero);
                sess.Close();
                sess.Dispose();
                return p != null;

            }

        }

        public static bool ExisteNombre(string Descripcion)
        {
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                return new PlantillaProtocolosRepository(sess).ExisteNombre(Descripcion);
            }
        }


        //agregar y actualizar
        public static void SaveOrUpdate(Domain.PlantillaProtocolos p)
        {

            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {

                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {

                    try
                    {

                        new PlantillaProtocolosRepository(sess).Add(p);

                        tx.Commit();
                    }
                    catch (Exception e)
                    {

                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {

                        sess.Close();
                        sess.Dispose();
                    }

                }

            }

        }

        //borrar
        public static void Delete(Domain.PlantillaProtocolos p)
        {
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new PlantillaProtocolosRepository(sess).Remove(p);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }

                }

            }

        }

    }
}

