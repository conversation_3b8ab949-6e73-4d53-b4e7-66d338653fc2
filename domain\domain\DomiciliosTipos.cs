//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 6/11/2023 09:33:14
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.DomiciliosTipos, Domain in the schema.
    /// </summary>
    public partial class DomiciliosTipos {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for DomiciliosTipos constructor in the schema.
        /// </summary>
        public DomiciliosTipos()
        {
            this.ContratoDomicilios = new HashSet<ContratoDomicilios>();
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Descripcion in the schema.
        /// </summary>
        public virtual string Descripcion
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Georeferenciado in the schema.
        /// </summary>
        public virtual System.Nullable<bool> Georeferenciado
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Codigo in the schema.
        /// </summary>
        public virtual System.Nullable<int> Codigo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ContratoDomicilios in the schema.
        /// </summary>
        public virtual ISet<ContratoDomicilios> ContratoDomicilios
        {
            get;
            set;
        }
    }

}
