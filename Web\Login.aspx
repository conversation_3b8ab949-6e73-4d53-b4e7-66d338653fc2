﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Login.aspx.cs" Inherits="Web.Login" %>

<%@ Register Assembly="DevExpress.Web.v18.1" Namespace="DevExpress.Web" TagPrefix="dx" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">

    <meta charset="utf-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
	<title>Login - SiGeLab</title>

	<link rel="icon" href="App_Themes/Tema1/img/favicon - EPAS (1).jpg"/>

	   <!-- Global stylesheets -->
  
    <link href="https://fonts.googleapis.com/css?family=Roboto:400,300,100,500,700,900" rel="stylesheet" type="text/css" />
    <link href="App_Themes/Tema1/assets/css/icons/fontawesome/styles.min.css" rel="stylesheet" />
    <link href="App_Themes/Tema1/assets/css/icons/phosphor/styles.min.css" rel="stylesheet" />
    <link href="App_Themes/Tema1/assets/css/icons/icomoon/styles.min.css" rel="stylesheet" />
    <link href="App_Themes/Tema1/assets/css/all.min.css" rel="stylesheet" />
    <!-- /Global Stylesheets -->

    <!-- Sweet Alerts CSS files -->
    <link href="App_Themes/Tema1/sweetalerts/sweetalert.min.css" rel="stylesheet" />
    <!-- /Sweet Alerts CSS files -->

    <!-- Core JS files -->
    <script src="/App_Themes/Tema1/assets/js/jquery.min.js"></script>
    <script src="/App_Themes/Tema1/assets/js/bootstrap.bundle.min.js"></script>
    <!-- /Core JS files -->

    <!-- Theme JS files -->
    <script src="/App_Themes/Tema1/assets/js/app.js"></script>
    <!-- /Theme JS files -->

    <!-- Sweet Alerts JS files -->    
    <script src="/App_Themes/Tema1/sweetalerts/sweet_alert.min.js"></script>
    <!-- /Sweet Alerts JS files -->

	

</head>

<body class="bg-dark">
	
	<!-- Page content -->
	<div class="page-content">

		<!-- Main content -->
		<div class="content-wrapper">

			<!-- Content area -->
			<div class="content d-flex justify-content-center align-items-center">

				<!-- Login card -->
				<dx:ASPxCallbackPanel runat="server" ID="CallbackPanelLogin"   ClientInstanceName="CallbackPanelLogin">
    
    <PanelCollection>
        <dx:PanelContent ID="PanelContent3" runat="server">


				<form class="" runat="server">					
					<div class="card mb-0">
						<div class="card-body">
							<div class="text-center mb-3 pb-1">
								<img src="App_Themes/Tema1/img/Navbar-Img_-_EPAS.jpg" width="200" />
								
								
								<%--<h4 class="mb-2 fw-bold">SiGeLab</h4>--%>
								<h6> <span class="fw-bold fs-5">Si.Ge.Lab.</span> <br /> Sistema de Gestión de Laboratorio <br /> </h6>
							</div>
                           
							<div class="form-group form-control-feedback form-control-feedback-start">
								<dx:ASPxTextBox ID="txtUsername" CssClass="form-control" NullText="Nombre de Usuario" runat="server"></dx:ASPxTextBox>
								<div class="form-control-feedback-icon mt-1">
									<i class="ph-user-circle text-muted"></i>
								</div>
							</div>

							<div class="form-group form-control-feedback form-control-feedback-start mt-3 mb-4">
								<dx:ASPxTextBox ID="txtPassword" Password="true" NullText="Contraseña" CssClass="form-control" runat="server" ClientInstanceName="passwordTextBox"></dx:ASPxTextBox>

								<div id="locked" runat="server" class="form-control-feedback-icon mt-1">
									<i class="ph-lock text-muted"></i>
								</div>
								<div id="unlocked" runat="server" class="form-control-feedback-icon mt-1" visible="false">
									<i class="ph-lock-key-open text-muted"></i>
								</div>
							</div>

							<div class="d-flex justify-content-end">
								<dx:ASPxLabel ID="lblPassword" runat="server" Text="Mostrar Contraseña" CssClass="col-form-label me-1 text-secondary fw-normal"></dx:ASPxLabel>
								<dx:ASPxCheckBox ID="chbMostrarPassword" CssClass="mt-1" Theme="Moderno" OnCheckedChanged="chbMostrarPassword_CheckedChanged" runat="server" ClientInstanceName="chbMostrarPassword" AutoPostBack="true">
                                </dx:ASPxCheckBox>
							</div>

                            <div class="form-group d-flex align-items-center">
								<dx:ASPxLabel ID="lblError" Visible="false" runat="server" Text="" CssClass="alert alert-danger"></dx:ASPxLabel>
                            </div>

							<div class="form-group text-end my-1">                               
								<a href="ResetPwd.aspx" class="">¿Olvidó su contraseña?</a>
							</div>

							<div class="form-group form-group-feedback-left text-center mt-4 pt-1">
                                <dx:ASPxButton ID="btnEntrar" Theme="Material" CssClass="rounded-2 py-1" HorizontalAlign="Center" OnClick="btnEntrar_Click" runat="server" Text="Entrar" >
                                    <%--<ClientSideEvents Click="function(s,e){}" />--%>
                                </dx:ASPxButton>
							</div>

							
							
						</div>
					</div>
					
				</form>

			</dx:PanelContent>
                    </PanelCollection>
                </dx:ASPxCallbackPanel>


				<!-- /login card -->

			</div>
			<!-- /content area -->

		</div>
		<!-- /main content -->

	</div>
	<!-- /page content -->

      <dx:ASPxCallback ID="ASPxCallback2" ClientIDMode="Static" runat="server">
            <ClientSideEvents CallbackComplete="function(s, e) {  }" />
        </dx:ASPxCallback>

    <dx:ASPxLoadingPanel Theme="Moderno" ID="ASPxLoadingPanel2" Modal="true" ForeColor="#106488" ClientInstanceName="LoadingPanel" runat="server" Text="Por favor espere...">
        </dx:ASPxLoadingPanel>
      
</body>
</html>
