﻿using DAO;
using Domain;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Collections;
using Common.DDL;

namespace Business.Provincias
{
    public class ProvinciasService
    {
        //Trae todo
        public static ICollection<Domain.Provincias> GetAll()
        {

            ICollection<Domain.Provincias> listaAux;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                listaAux = (ICollection<Domain.Provincias>)new ProvinciasRepository(sess).GetAll();

               
                sess.Close();
                sess.Dispose();
            }
            return (listaAux);

        }
        //trae solo de neuquen 

        public virtual ICollection<Domain.Provincias> GetAllNqn(bool Activo)
        {
            Domain.Provincias p;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                p = (Domain.Provincias) new ProvinciasRepository(sess).GetAllNeuquen(true);
                sess.Close();
                sess.Dispose();
                return (ICollection<Domain.Provincias>)p;
            }

        }

        //obtener por id
        public static Domain.Provincias GetById(int id)
        {
            Domain.Provincias p;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                p = (Domain.Provincias)new ProvinciasRepository(sess).GetByKey(id);
            
                sess.Close();
                sess.Dispose();
                return p;
            }
        }


        //obterner activos y id 

        public static IList<Domain.Provincias> GetByBuscarProvincias(int Id, bool Activo)
        {
            IList<Domain.Provincias> listaAux;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                listaAux = (IList<Domain.Provincias>)new ProvinciasRepository(sess).GetByBuscarProvincias(Id,Activo);
                sess.Close();
                sess.Dispose();
            }

            ItemCombo item;
            ListaCombo lista = new ListaCombo();
            foreach (Domain.Provincias p in listaAux)
            {

                item = new ItemCombo();
                item.ID = p.Id.ToString();
                item.Descripcion = p.Descripcion;
                lista.Add(item);
            }
            return (listaAux);
        }



        //agregar y actualizar
        public static void SaveOrUpdate(Domain.Provincias p)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new ProvinciasRepository(sess).Add(p);

                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }

            }
        }

        //borrar
        public static void Delete(Domain.Provincias p)
        {
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new ProvinciasRepository(sess).Remove(p);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }

            }
        }



    }

}
       
        
        
        
        
        
        
        
        
        
        
        
        
        
        










