﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="SubFuentes.aspx.cs" Inherits="Web.EPAS.Lista.SubFuentes" %>

<%@ Register Assembly="DevExpress.Web.v18.1" Namespace="DevExpress.Web" TagPrefix="dx" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">

    <script type="text/javascript">

        function OnToolbarItemClick(s, e) {
            if (IsCustomExportToolbarCommand(e.item.name)) {
                e.processOnServer = true;
                e.usePostBack = true;
            } else {

            }
        }
        function IsCustomExportToolbarCommand(command) {
            return command == "CustomExportToXLS" || command == "CustomExportToXLSX";
        }

    </script>

</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

             <!-- Page header -->
<div class="page-header page-header-light shadow">
    <div class="page-header-content d-lg-flex border-top">
        <div class="d-flex">
            <div class="breadcrumb py-2">
                <a href="<%= Web.Util.Helpers.GetApplicationPath() %>/Index.aspx" class="breadcrumb-item"><i class="icon-home2 mr-2"></i>Inicio</a>
                <a href="<%= Web.Util.Helpers.GetApplicationPath() %>/EPAS/Lista/Fuentes.aspx" class="breadcrumb-item"><i class="icon-display me-2"></i>Mantenimiento</a>
                <span class="breadcrumb-item active">
                    <asp:Literal ID="ltr_nombreBreadcrumb" runat="server">Fuentes</asp:Literal></span>
            </div>

            <a href="#" class="header-elements-toggle text-default d-md-none"><i class="icon-more"></i></a>
        </div>
    </div>
</div>
<!-- /page header -->
    <div class="row gx-1" runat="server">

        <div class="col-lg-12"">

            <div class="navbar navbar-expand-xl bg-secondary navbar-dark rounded-top mt-1">
                <div class="container-fluid">                           
                    <div class="nav-item dropdown">
                        <h5 class="mt-2 mb-1 fs-5" data-bs-toggle="tab">Mantenimiento de Fuentes</h5>
                    </div>                        
                </div>
            </div>

            <!-- Card Cliente -->

        <div class="col-lg-12 navbar navbar-expand-xl bg-secondary navbar-dark rounded-top mt-1 py-0">
            <div class="container-fluid">                           
                <div class="nav-item dropdown">
                    <h5 class="mt-2 mb-1 fs-5" data-bs-toggle="tab""></h5>
                </div>                        
            </div>
        </div>


        <!-- Datos Titular -->

        <div class="row gx-3">           
            
            <div class="card col-lg-12">

                <div class="row gx-1">

                    <!-- Campos de Contrato -->
                    
                    <div class="card-body col-lg-6">
                        <div action="#">

                            <div class="row ms-3">
                                <dx:ASPxGridView ID="GripV_SubFuentes" runat="server" CssClass="border border-black rounded" AutoGenerateColumns="False">
                                        <Toolbars>
                                          <dx:GridViewToolbar ItemAlign="Right" >
                                               <%--<SettingsAdaptivity Enabled="true" EnableCollapseRootItemsToIcons="true" />--%>
                                              <Items>
                                                  <dx:GridViewToolbarItem BeginGroup="true">
                                                      <Template>
                                                          <dx:ASPxButtonEdit ID="tbToolbarSearch" runat="server" NullText="Buscar..." Height="100%" Theme="Moderno">
                                                              <Buttons>
                                                                  <dx:SpinButtonExtended Image-IconID="find_find_16x16gray" />
                                                              </Buttons>
                                                          </dx:ASPxButtonEdit>
                                                      </Template>
                                                  </dx:GridViewToolbarItem>
                                                  <dx:GridViewToolbarItem Command="Refresh" BeginGroup="true" />
                                              </Items>
                                          </dx:GridViewToolbar>
                                      </Toolbars>
                                      <SettingsSearchPanel CustomEditorID="tbToolbarSearch" />


                                    <Columns>

                                        <dx:GridViewDataTextColumn FieldName="Codigo" caption="Código" CellStyle-Border-BorderColor="Black" CellStyle-HorizontalAlign="Center" Width="100px">
                                            <HeaderStyle HorizontalAlign="Center" />
                                        </dx:GridViewDataTextColumn>

                                        <dx:GridViewDataTextColumn FieldName="Descripcion" caption="Descripción" CellStyle-Border-BorderColor="Black" CellStyle-HorizontalAlign="Center" >
                                            <HeaderStyle HorizontalAlign="Center" />
                                        </dx:GridViewDataTextColumn>

                                        <dx:GridViewDataColumn Caption="" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center" Width="60px">
                                            <DataItemTemplate>
                                                <dx:aspxbutton id="btnEditar" runat="server" rendermode="Link" text="Editar" onclick="btnEditar_Click"
                                                    cssclass="fw-semibold text-decoration-none text-secondary" font-size="Small" theme="Moderno" commandargument='<%# Eval("Id")%>' autopostback="false">
                                                    <clientsideevents click="function (s,e){MostrarLoading();}" />
                                                </dx:aspxbutton>
                                            </DataItemTemplate>
                                        </dx:GridViewDataColumn>

                                                <%-- <dx:GridViewDataColumn Caption="" CellStyle-Border-BorderColor="Black" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center" Width="60px">
                                                   <DataItemTemplate>
                                                        <dx:ASPxButton runat="server" ID="btnEliminar" RenderMode="Link" Text="Eliminar" OnClick="btnEliminar_Click"
                                                           CommandArgument='<%# Eval("Id" )%>' AutoPostBack="false">
                                                               <ClientSideEvents Click="function(s,e){e.processOnServer ==confirm('¿Desea eliminar?');}" />
                                                       </dx:ASPxButton>
                                                   </DataItemTemplate>
                                               </dx:GridViewDataColumn>
                                            --%>
                                    </Columns>
                                </dx:ASPxGridView>
                            </div>

                            <!-- BOTONES -->
                            <div class="row my-5 ms-5">

                                <div class="col-lg-1 ps-1 form-control-feedback form-control-feedback-start flex-grow-1 d-flex justify-content-center">

                                  
                                    <asp:Button ID="Btn_imprimir" runat="server" Text="IMPRIMIR" CssClass="btn btn-secondary fw-semibold" />
                                     <ClientSideEvents Click="function(s,e){MostrarLoading();}" />
                                    <asp:Button ID="Btn_excel" runat="server" Text="EXCEL" CssClass="btn btn-secondary fw-semibold ms-3"/>
                                     <ClientSideEvents Click="function(s,e){MostrarLoading();}" />

                                  <dx:ASPxHyperLink ID="btn_Nuevo" runat="server" CssClass="text-decoration-none btn btn-secondary fw-semibold ms-3 text-white" 
                                      Cursor="pointer" Text="NUEVO" Font-Size="Small" Theme="Moderno" NavigateUrl="~/EPAS/AddEdit/AddEditSubFuentes.aspx">
                                      <ClientSideEvents Click="function(s,e){MostrarLoading();}" />
                                  </dx:ASPxHyperLink>

                                </div>                                
                            </div>
                        </div>
                    </div>

                    
                </div>
            </div>
        </div>
    </div>
    </div>
</asp:Content>


<asp:Content ID="Content3" ContentPlaceHolderID="footer" runat="server">
    
</asp:Content>