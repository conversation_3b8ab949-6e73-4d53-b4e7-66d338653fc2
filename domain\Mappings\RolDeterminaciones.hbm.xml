<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="RolDeterminaciones" table="RolDeterminaciones">
    <id name="Id" type="Int32">
      <column name="id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Visible" type="Boolean">
      <column name="Visible" not-null="false" sql-type="bit" />
    </property>
    <many-to-one name="Rol" class="Rol">
      <column name="IdRol" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <many-to-one name="Determinaciones" class="Determinaciones">
      <column name="IdDeterminación" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
  </class>
</hibernate-mapping>