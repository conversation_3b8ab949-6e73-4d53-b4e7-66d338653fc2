﻿<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="Cajas" table="Cajas">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Descripcion" type="String">
      <column name="Descripcion" not-null="true" length="50" sql-type="nvarchar" />
    </property>
    <property name="Terminal" type="String">
      <column name="Terminal" not-null="true" length="50" sql-type="nvarchar" />
    </property>
    <property name="FechaEnCero" type="DateTime">
      <column name="FechaEnCero" not-null="false" sql-type="datetime" />
    </property>
    <property name="Nula" type="Int16">
      <column name="Nula" not-null="false" precision="5" scale="0" sql-type="smallint" />
    </property>
    <property name="IdUsuario" type="Int32">
      <column name="IdUsuario" not-null="false" precision="10" scale="0" sql-type="int" />
    </property>
    <set name="Recibos" inverse="true" generic="true">
      <key>
        <column name="IdCaja" />
      </key>
      <one-to-many class="Recibos" />
    </set>
    <set name="Usuarios" inverse="true" generic="true">
      <key>
        <column name="IdCaja" />
      </key>
      <one-to-many class="Usuario" />
    </set>
  </class>
</hibernate-mapping>