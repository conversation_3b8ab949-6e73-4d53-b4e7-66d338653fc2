﻿using Business.Services.Epas;
using Domain;
using Moq;
using System;
using Xunit;

namespace TestUnit_Business.Services.Epas
{
    /// <summary>
    /// Prueba unitaria para la clase ProtocolosService
    /// </summary>
    public class ProtocolosServiceTests
    {
        private MockRepository mockRepository;


        public ProtocolosServiceTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Strict);
        }
               
        [Fact]
        public void GetById_StateUnderTest_ExpectedBehavior()
        {
            // Arrange
          
            int id = 0;

            // Act
            var result = ProtocolosService.GetById(
                id);

            // Assert
            Assert.True(false);
            this.mockRepository.VerifyAll();
        }

        [Fact]
        public void GetAll_StateUnderTest_ExpectedBehavior()
        {
            // Arrange
       

            // Act
            var result = ProtocolosService.GetAll();

            // Assert
            Assert.True(false);
            this.mockRepository.VerifyAll();
        }

        [Fact]
        public void GetActive_StateUnderTest_ExpectedBehavior()
        {
            // Arrange
         

            // Act
            var result = ProtocolosService.GetActive();

            // Assert
            Assert.True(false);
            this.mockRepository.VerifyAll();
        }

        [Fact]
        public void SaveOrUpdate_StateUnderTest_ExpectedBehavior()
        {
            // Arrange
        
            Protocolos protocolos = null;

            // Act
            ProtocolosService.SaveOrUpdate(protocolos);

            // Assert
            Assert.True(false);
            this.mockRepository.VerifyAll();
        }

        [Fact]
        public void Delete_StateUnderTest_ExpectedBehavior()
        {
            // Arrange
          
            Protocolos P = null;

            // Act
            ProtocolosService.Delete(
                P);

            // Assert
            Assert.True(false);
            this.mockRepository.VerifyAll();
        }
    }
}
