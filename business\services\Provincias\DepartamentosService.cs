﻿using DAO;
using Domain;
using NHibernate;
using NHibernate.Mapping;
using NPOI.HPSF;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls.WebParts;

namespace Business.Services.Provincias
{
    public class DepartamentosService
    {
        //Trae todo
        public static IList<Domain.Departamentos> GetAll()
        {
            IList<Domain.Departamentos> listaAux;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {

                listaAux = (IList<Domain.Departamentos>)new DepartamentosRepository(sess).GetAll();

                foreach (Domain.Departamentos l in listaAux)
                {
                    NHibernateUtil.Initialize(l.Provincias);
                }

                sess.Close();
                sess.Dispose();
            }
            return (listaAux);

        }

        public static IList<Domain.Departamentos> GetByProvincias(int idProvincia)
        {

            IList<Domain.Departamentos> listaAux;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {

                listaAux = (IList<Domain.Departamentos>)new DepartamentosRepository(sess).GetByProvincia(idProvincia);
                sess.Close();
                sess.Dispose();
            }
            return (listaAux);

        }




        //obtener por id
        public static Domain.Departamentos GetById(int id)
        {

            Domain.Departamentos p;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {

                p = (Domain.Departamentos)new DepartamentosRepository(sess).GetByKey(id);
                sess.Close();
                sess.Dispose();
                return p;
            }

        }


        public static bool ExisteCodigo(string codigo)
        {

            Domain.Departamentos p;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {

                p = (Domain.Departamentos)new DepartamentosRepository(sess).ExisteCodigo(codigo);
                sess.Close();
                sess.Dispose();
                return p != null;

            }

        }


        public static bool ExisteNombre(string Descripcion)
        {
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                return new DepartamentosRepository(sess).ExisteNombre(Descripcion);
            }
        }


        //agregar y actualizar
        public static void SaveOrUpdate(Domain.Departamentos p)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new DepartamentosRepository(sess).Add(p);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {

                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {

                        sess.Close();
                        sess.Dispose();
                    }

                }

            }

        }

        //borrar
        public static void Delete(Domain.Departamentos p)
        {
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new DepartamentosRepository(sess).Remove(p);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }

                }

            }

        }
    }

    
}

