﻿using Business.Services.Epas;
using Business.Services.Provincias;
using DevExpress.Web;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web.EPAS.Lista
{
    public partial class Metodos : System.Web.UI.Page
    {
        protected void Page_Load(object sender,EventArgs e)
        {

            CargarDatosEnGv_Metodos();

            if (!IsPostBack)
            {
              
            }
        }

        protected void CargarDatosEnGv_Metodos()
        {
            try
            {
                List<Domain.MetodosdeMedicion> me = (List<Domain.MetodosdeMedicion>) MetodosdeMedicionService.GetAll();
                gv_Metodos.DataSource = me;
                gv_Metodos.DataBind();
            }
            catch (Exception ex)
            {
                string g = ex.Message;
            }
        }
        protected void btnEditar_Click(object sender, EventArgs e)
        {
            try
            {
                ASPxButton btn = (ASPxButton)sender;
                if (btn != null)
                {
                    GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer)btn.NamingContainer;
                    if (c != null)
                    {
                        var ind = DataBinder.Eval(c.DataItem, "Id");

                        if (ind != null)
                        {
                            Domain.MetodosdeMedicion metodoMedicion = MetodosdeMedicionService.GetById(Convert.ToInt32(ind));

                            if(metodoMedicion.Activo == false)
                            {
                                string message = string.Format("El Método de Medición {0} no se puede editar ya que está anulado.",metodoMedicion.Descripcion);
                                ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('" + message + "')",true);

                            } else
                            {
                                Response.Redirect(Global.ApplicationPath + "/EPAS/AddEdit/AddEditMetodosdeMedicion.aspx?id=" + Util.Helpers.Encrypt(ind.ToString()), false);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al tratar de editar un elemento, consulte al administrador del sistema', 'error');", true);
            }

        }

        protected void btnAnularActivar_Click(object sender, EventArgs e)
        {
            try
            {
                ASPxButton btn = (ASPxButton)sender;
                GridViewDataItemTemplateContainer f = (GridViewDataItemTemplateContainer)btn.NamingContainer;
                var ind = DataBinder.Eval(f.DataItem, "Id");

                Domain.MetodosdeMedicion ME = MetodosdeMedicionService.GetById(Convert.ToInt32(ind));
                ME.Activo = !ME.Activo;

                MetodosdeMedicionService.SaveOrUpdate(ME);
                CargarDatosEnGv_Metodos();

            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al tratar de eliminar un elemento, consulte al administrador del sistema', 'error');", true);

            }

        }
    }
}