﻿using Common.Encrypt;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Web.Util
{
    public class Helpers
    {
        public static string GetApplicationPath()
        {
            
            return Global.ApplicationPath;

        }


        public static string Encrypt(string str)
        {
            //Encripto el ID
            SimpleAES sp = new SimpleAES();

            return sp.EncryptToString(str);

        }


        public static string Decrypt(string str)
        {
            //Desencripto el ID
            SimpleAES sp = new SimpleAES();

            return sp.DecryptString(str);

        }

        public static string getBadgePorEstado(object codigo)
        {
            string salida = "badge bg-pill bg-light";
            if(codigo != null)
            {
                switch(codigo.ToString())
                {
                    case "A":
                    salida = "badge bg-pill bg-success";
                    break;
                    case "ER":
                    salida = "badge bg-pill bg-info";
                    break;
                    case "P":
                    salida = "badge bg-pill bg-warning";
                    break;
                    case "B":
                    salida = "badge bg-pill bg-danger";
                    break;

                }
            }
            return salida;
        }
    }
}