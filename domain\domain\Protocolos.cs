//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 12/12/2023 11:06:57
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.Protocolos, Domain in the schema.
    /// </summary>
    public partial class Protocolos {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for Protocolos constructor in the schema.
        /// </summary>
        public Protocolos()
        {
            this.ProtocoloDeterminaciones = new HashSet<ProtocoloDeterminaciones>();
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for NroProtocolo in the schema.
        /// </summary>
        public virtual System.Nullable<int> NroProtocolo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for FechaCreado in the schema.
        /// </summary>
        public virtual System.Nullable<System.DateTime> FechaCreado
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for FechaToma in the schema.
        /// </summary>
        public virtual System.Nullable<System.DateTime> FechaToma
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for RecepcionToma in the schema.
        /// </summary>
        public virtual System.Nullable<System.DateTime> RecepcionToma
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Modulo in the schema.
        /// </summary>
        public virtual System.Nullable<int> Modulo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for MontoTotal in the schema.
        /// </summary>
        public virtual System.Nullable<decimal> MontoTotal
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ObservacionSitioToma in the schema.
        /// </summary>
        public virtual string ObservacionSitioToma
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Expediente in the schema.
        /// </summary>
        public virtual string Expediente
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ImprimirLeyendas in the schema.
        /// </summary>
        public virtual System.Nullable<bool> ImprimirLeyendas
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for UnidadesPrimarias in the schema.
        /// </summary>
        public virtual System.Nullable<bool> UnidadesPrimarias
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Laboratorio in the schema.
        /// </summary>
        public virtual string Laboratorio
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for OpcionesResultados in the schema.
        /// </summary>
        public virtual string OpcionesResultados
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Informes in the schema.
        /// </summary>
        public virtual string Informes
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Observaciones in the schema.
        /// </summary>
        public virtual string Observaciones
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Veredicto in the schema.
        /// </summary>
        public virtual string Veredicto
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ObservacionVeredicto in the schema.
        /// </summary>
        public virtual string ObservacionVeredicto
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for FechaAprobado in the schema.
        /// </summary>
        public virtual System.Nullable<System.DateTime> FechaAprobado
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ProtocoloDeterminaciones in the schema.
        /// </summary>
        public virtual ISet<ProtocoloDeterminaciones> ProtocoloDeterminaciones
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ProtocolosEstados in the schema.
        /// </summary>
        public virtual ProtocolosEstados ProtocolosEstados
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Usuario_IdUsuario in the schema.
        /// </summary>
        public virtual Usuario Usuario_IdUsuario
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Usuario_IdUsuarioAprobo in the schema.
        /// </summary>
        public virtual Usuario Usuario_IdUsuarioAprobo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Contratos_IdContratoSolicitante in the schema.
        /// </summary>
        public virtual Contratos Contratos_IdContratoSolicitante
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Contratos_IdContratoTomador in the schema.
        /// </summary>
        public virtual Contratos Contratos_IdContratoTomador
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Fuentes in the schema.
        /// </summary>
        public virtual Fuentes Fuentes
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for SubFuentes in the schema.
        /// </summary>
        public virtual SubFuentes SubFuentes
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for TipoAnalisis in the schema.
        /// </summary>
        public virtual TipoAnalisis TipoAnalisis
        {
            get;
            set;
        }
    }

}
