//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 3/11/2023 09:44:13
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class ContratoContactosRepository : NHibernateRepository<Domain.ContratoContactos>, IContratoContactosRepository
    {
        public ContratoContactosRepository(ISession session) : base(session)
        {
        }

        public virtual ICollection<Domain.ContratoContactos> GetAll()
        {
            return session.CreateQuery(string.Format("from ContratoContactos")).List<Domain.ContratoContactos>();
        }

        public virtual ICollection<Domain.ContratoContactos> GetAllByIdContrato(int IdContrato) {
            string hql = "from ContratoContactos Where Activo = :Activo and Contratos.Id = :IdContrato Order BY Id asc";

            IQuery q = session.CreateQuery(hql);
            q.SetParameter("Activo",true);
            q.SetParameter("IdContrato",IdContrato);
            return q.List<Domain.ContratoContactos>();
        }

        public virtual Domain.ContratoContactos GetByKey(int _Id)
        {
            return session.Get<Domain.ContratoContactos>(_Id);
        }
    }
}
