﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 26/9/2023 16:47:39
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class TipoAnalisisRepository : NHibernateRepository<Domain.TipoAnalisis>, ITipoAnalisisRepository
    {
        public TipoAnalisisRepository(ISession session) : base(session)
        {
        }

        public virtual IList<Domain.TipoAnalisis> GetAll()
        {
            return session.CreateQuery(string.Format("from TipoAnalisis")).List<Domain.TipoAnalisis>();
        }

        public virtual Domain.TipoAnalisis GetByKey(int _Id)
        {
            return session.Get<Domain.TipoAnalisis>(_Id);
        }

        public virtual Domain.TipoAnalisis ExisteCodigo(string stringCodigo)
        {
            string hql = "from TipoAnalisis where  upper(Codigo)=:stringCodigo  and Activo=true";
            return session.CreateQuery(hql)

          .SetParameter("stringCodigo", stringCodigo.ToUpper())
          .UniqueResult<Domain.TipoAnalisis>();
        }
        public virtual bool ExisteNombre(string Descripcion)
        {
            string hql = "select count(*) from TipoAnalisis where upper(Descripcion) = :Descripcion and Activo = true";
            long count = session.CreateQuery(hql)
                .SetParameter("Descripcion", Descripcion.ToUpper())
                .UniqueResult<long>();

            return count > 0;
        }
        public virtual ICollection<Domain.TipoAnalisis> GetByAnalisisTipo(bool Activo) {
            string hql = "from TipoAnalisis order BY Descripcion asc";

            IQuery q = session.CreateQuery(hql);
            return q.List<Domain.TipoAnalisis>();

        }
    }
}
