﻿<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="TipoPermiso" table="TipoPermiso">
    <id name="Id" type="Int32">
      <column name="id" not-null="true" precision="10" scale="0" sql-type="INT" />
      <generator class="identity" />
    </id>
    <property name="Nombre" type="String">
      <column name="nombre" not-null="false" length="50" sql-type="NVARCHAR(50)" />
    </property>
    <property name="Codigo" type="String">
      <column name="codigo" not-null="false" length="50" sql-type="NVARCHAR(50)" />
    </property>
    <set name="ModuloControls" inverse="true" generic="true">
      <key>
        <column name="idTipoPermiso" />
      </key>
      <one-to-many class="ModuloControl" />
    </set>
  </class>
</hibernate-mapping>