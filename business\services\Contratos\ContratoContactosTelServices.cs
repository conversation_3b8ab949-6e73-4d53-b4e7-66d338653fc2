﻿using DAO;
using Domain;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Contratos {
    public class ContratoContactosTelServices {

        public static void SaveOrUpdate(ContratoContactosTel contratoContactosTel) {

            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                using(NHibernate.ITransaction tx = sess.BeginTransaction()) {
                    try {
                        new ContratoContactosTelRepository(sess).Add(contratoContactosTel);
                        tx.Commit();
                    } catch(Exception e) {
                        tx.Rollback();
                        throw e;
                    } finally {
                        sess.Close();
                        sess.Dispose();
                    }
                }

            }
        }


        public static ContratoContactosTel GetByContratoContacto(int idContratoContacto) {
            ContratoContactosTel EE;
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                EE = new ContratoContactosTelRepository(sess).GetByContratoContacto(idContratoContacto);

                if(EE != null) {
                    NHibernateUtil.Initialize(EE);

                }
                sess.Close();
                sess.Dispose();
                return EE;
            }
        }

        public static IList<ContratoContactosTel> GetAll() {
            IList<ContratoContactosTel> listaAux;
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                listaAux = (IList<ContratoContactosTel>) new ContratoContactosTelRepository(sess).GetAll();
                sess.Close();
                sess.Dispose();
            }

            return (listaAux);
        }

        public static ContratoContactosTel GetById(int id) {
            ContratoContactosTel TT;
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                TT = new ContratoContactosTelRepository(sess).GetByKey(id);

                if(TT != null) {
                    NHibernateUtil.Initialize(TT);

                }
                sess.Close();
                sess.Dispose();
                return TT;
            }
        }

        public static void Delete(ContratoContactosTel TT) {
            using(NHibernate.ISession sess = NHibernateSessionProvider.GetSession()) {
                using(NHibernate.ITransaction tx = sess.BeginTransaction()) {
                    try {
                        new ContratoContactosTelRepository(sess).Remove(TT);
                        tx.Commit();
                    } catch(Exception e) {
                        tx.Rollback();
                        throw e;
                    } finally {
                        sess.Close();
                        sess.Dispose();
                    }
                }
            }
        }
    }
}
