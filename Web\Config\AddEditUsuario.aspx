﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="AddEditUsuario.aspx.cs" Inherits="Web.Config.AddEditUsuario" %>

<%@ Register Assembly="DevExpress.Web.v18.1" Namespace="DevExpress.Web" TagPrefix="dx" %>


<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
      <script>
        

          //Occurs after a different item in the list box has been selected/unselected
          function ProcessSelection(s, e) {
              if (e.isSelected) {
                  var selectedIndices = lbRoles.GetSelectedIndices();
                //  alert(selectedIndices);
              }
           
          }
          function DeSelection(s, e) {
              
                  var selectedIndices = lbRoles.GetSelectedIndices();
                 // alert(selectedIndices);
            

          }

      </script> 
</asp:Content>


<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">


      <!-- Page header -->
  <div class="page-header page-header-light shadow">
     <div class="page-header-content d-lg-flex border-top">
         <div class="d-flex">
             <div class="breadcrumb py-2">
                    <a href="<%= Web.Util.Helpers.GetApplicationPath() %>/Index.aspx" class="breadcrumb-item"><i class="icon-home2 mr-2"></i>Inicio</a>
                    <a href="<%= Web.Util.Helpers.GetApplicationPath() %>/Config/Usuarios.aspx" class="breadcrumb-item"><i class="icon-gear mr-2"></i>Usuarios</a>
                    <span class="breadcrumb-item active">
                        <asp:Literal ID="ltr_nombreBreadcrumb" runat="server">Agregar ó editar</asp:Literal></span>
                </div>

                <a href="#" class="header-elements-toggle text-default d-md-none"><i class="icon-more"></i></a>
            </div>

            <%--    <div class="header-elements d-none">
                    <div class="breadcrumb justify-content-center">

                        <div class="breadcrumb-elements-item dropdown p-0">
                            <a href="#" class="breadcrumb-elements-item dropdown-toggle" data-toggle="dropdown">Dropdown
                            </a>

                            <div class="dropdown-menu dropdown-menu-right">
                                <a href="#" class="dropdown-item">Action</a>
                                <a href="#" class="dropdown-item">Another action</a>
                                <a href="#" class="dropdown-item">One more action</a>
                                <div class="dropdown-divider"></div>
                                <a href="#" class="dropdown-item">Separate action</a>
                            </div>
                        </div>
                    </div>
                </div>--%>
        </div>


    </div>
    <!-- /page header -->
    
    <!-- /page header -->

        <dx:ASPxCallback ID="callback_cargarReferentes" runat="server" ClientInstanceName="callback_cargarReferentes">
                            <ClientSideEvents CallbackComplete="function(s,e){ }" EndCallback="function(s,e){cbReferente.PerformCallback();LoadingPanel.Show(); }" />
                        </dx:ASPxCallback>
      <!-- Page content -->
    <div class="page-content pt-0">

        <!-- Main content -->
        <div class="content-wrapper">

            <!-- Content area -->
            <div class="content">

                <!-- Basic card -->
                <div class="card">
                    <div class="card-header header-elements-inline">
                        <h5 class="card-title"></h5>
                        <div class="header-elements">
                            <div class="list-icons">
                                <a class="list-icons-item" data-action="collapse"></a>

                            </div>
                        </div>
                    </div>

                    <div class="card-body">
                           <div id="ResizedDiv" style="width: 100%;">
      <dx:ASPxFormLayout ID="ASPxFormLayout1" runat="server" Width="100%" ClientInstanceName="FormLayout">
                            <Items>
                                <dx:LayoutGroup Width="100%" Caption="Datos del usuario" ColumnCount="3">
                    <GridSettings StretchLastItem="true" WrapCaptionAtWidth="660">
                        <Breakpoints>
                            <dx:LayoutBreakpoint MaxWidth="500" ColumnCount="1" Name="S" />
                            <dx:LayoutBreakpoint MaxWidth="800" ColumnCount="2" Name="M" />
                        </Breakpoints>
                    </GridSettings>
                    <Items>
                        <dx:LayoutItem Caption="Nombre" VerticalAlign="Middle">
                            <SpanRules>
                                <dx:SpanRule ColumnSpan="1" RowSpan="1" BreakpointName="S"></dx:SpanRule>
                                <dx:SpanRule ColumnSpan="2" RowSpan="1" BreakpointName="M"></dx:SpanRule>
                            </SpanRules>
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer>
                                    <dx:ASPxTextBox ID="txt_nombre" runat="server" Width="100%" >
                                         <ValidationSettings SetFocusOnError="True" ErrorText="Campo requerido" Display="Dynamic" >
                        <RequiredField IsRequired="True" ErrorText="Campo requerido" />
                    </ValidationSettings>
                                    </dx:ASPxTextBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>

                        <dx:LayoutItem Caption="Apellido" VerticalAlign="Middle">
                            <SpanRules>
                                <dx:SpanRule ColumnSpan="1" RowSpan="1" BreakpointName="S"></dx:SpanRule>
                                <dx:SpanRule ColumnSpan="1" RowSpan="1" BreakpointName="M"></dx:SpanRule>
                            </SpanRules>
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer>
                                    <dx:ASPxTextBox ID="txt_apellido" runat="server" Width="100%" >
                                        <ValidationSettings SetFocusOnError="True" ErrorTextPosition="Bottom" ErrorText="Campo requerido" Display="Dynamic" >
                        <RequiredField IsRequired="True" ErrorText="Campo requerido" />
                    </ValidationSettings></dx:ASPxTextBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                         
                        
                          <dx:LayoutItem Caption="E-mail" VerticalAlign="Middle">
                            <SpanRules>
                                <dx:SpanRule ColumnSpan="1" RowSpan="1" BreakpointName="S"></dx:SpanRule>
                                <dx:SpanRule ColumnSpan="2" RowSpan="1" BreakpointName="M"></dx:SpanRule>
                            </SpanRules>
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer>
                                    <dx:ASPxTextBox runat="server" ID="txt_email" Width="100%">
                                    
                                           <ValidationSettings SetFocusOnError="True" ErrorText="Campo requerido" ErrorTextPosition="Bottom" Display="Dynamic" >
                        <RequiredField IsRequired="True" ErrorText="Campo requerido" />
                                              <RegularExpression ErrorText="E-mail inválido" ValidationExpression="\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*" />
                                      </ValidationSettings>
                                    </dx:ASPxTextBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>

                         <dx:LayoutItem Caption="Nombre de usuario" VerticalAlign="Middle">
                            <SpanRules>
                                <dx:SpanRule ColumnSpan="1" RowSpan="1" BreakpointName="S"></dx:SpanRule>
                                <dx:SpanRule ColumnSpan="1" RowSpan="1" BreakpointName="M"></dx:SpanRule>
                            </SpanRules>
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer>
                                       <dx:ASPxTextBox ID="txt_username" runat="server" Width="100%"  >
                                             <ValidationSettings SetFocusOnError="True" ErrorTextPosition="Bottom" ErrorText="Campo requerido" Display="Dynamic" >
                        <RequiredField IsRequired="True" ErrorText="Campo requerido" />
                                                 <RegularExpression ErrorText="Sólo minusculas, letras y números" ValidationExpression="\w+([-+.']\w+)*" />
                    </ValidationSettings>
                                       </dx:ASPxTextBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>


                        <dx:LayoutItem Caption="Teléfono" VerticalAlign="Middle">
                            <SpanRules>
                                <dx:SpanRule ColumnSpan="1" RowSpan="1" BreakpointName="S"></dx:SpanRule>
                                <dx:SpanRule ColumnSpan="1" RowSpan="1" BreakpointName="M"></dx:SpanRule>
                            </SpanRules>
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer>
                                    <dx:ASPxTextBox ID="txt_telefono" runat="server" Width="100%" />
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>

                        <dx:LayoutItem Caption="Dirección" VerticalAlign="Middle">
                            <SpanRules>
                                <dx:SpanRule ColumnSpan="1" RowSpan="1" BreakpointName="S"></dx:SpanRule>
                                <dx:SpanRule ColumnSpan="1" RowSpan="1" BreakpointName="M"></dx:SpanRule>
                            </SpanRules>
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer>
                                    <dx:ASPxTextBox ID="txt_direccion" runat="server" Width="100%" />
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>

                        <dx:LayoutItem Caption="Password" VerticalAlign="Middle">
                            <SpanRules>
                                <dx:SpanRule ColumnSpan="1" RowSpan="1" BreakpointName="S"></dx:SpanRule>
                                <dx:SpanRule ColumnSpan="1" RowSpan="1" BreakpointName="M"></dx:SpanRule>
                            </SpanRules>
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer>
                                    <dx:ASPxTextBox ID="txt_password" runat="server" Width="100%" >
                                          <ValidationSettings SetFocusOnError="True" ErrorText="Campo requerido" Display="Dynamic" >
                        <RequiredField IsRequired="True" ErrorText="Campo requerido" />
                    </ValidationSettings>
                                    </dx:ASPxTextBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>



                           <dx:LayoutItem Caption="Activo" ColumnSpan="2">
                            <SpanRules>
                                <dx:SpanRule ColumnSpan="1" RowSpan="1" BreakpointName="S"></dx:SpanRule>
                                <dx:SpanRule ColumnSpan="1" RowSpan="1" BreakpointName="M"></dx:SpanRule>
                            </SpanRules>
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer>
                                    <dx:ASPxCheckBox ID="ch_activo" runat="server"></dx:ASPxCheckBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                         
                         <dx:LayoutItem ShowCaption="false" RowSpan="1">
                            <SpanRules>
                                <dx:SpanRule ColumnSpan="1" RowSpan="1" BreakpointName="S"></dx:SpanRule>
                                <dx:SpanRule ColumnSpan="1" RowSpan="2" BreakpointName="M"></dx:SpanRule>
                            </SpanRules>
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer>
                                  
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem Caption="Áreas" VerticalAlign="Middle" ColumnSpan="3" >
                            <SpanRules>
                                <dx:SpanRule ColumnSpan="1" RowSpan="1" BreakpointName="S"></dx:SpanRule>
                                <dx:SpanRule ColumnSpan="1" RowSpan="1" BreakpointName="M"></dx:SpanRule>
                            </SpanRules>
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer>
                                    <dx:ASPxListBox ID="lb_roles" EnableSelectAll="true"  runat="server" Height="200px" ClientInstanceName="lbRoles"  ValueField="ID" ValueType="System.String" 
                                        TextField="Descripcion" SelectionMode="CheckColumn"
                                        Caption="Puede seleccionar más de uno:">
                                        <ClientSideEvents SelectedIndexChanged="function(s,e){ProcessSelection(s,e); }" LostFocus="function(s,e){DeSelection(s,e); }"  />
                                         <CaptionSettings Position="Top" />
                                          <ValidationSettings SetFocusOnError="True" ErrorText="Campo requerido" Display="Dynamic">
                        <RequiredField IsRequired="True" ErrorText="Campo requerido" />
                    </ValidationSettings>
                                        </dx:ASPxListBox>
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                    
                      
                      

                         
                         
                    



                          <dx:LayoutItem ShowCaption="False" HorizontalAlign="Center" VerticalAlign="Middle" Paddings-PaddingTop="20px" CssClass="lastItem">
                            <SpanRules>
                                <dx:SpanRule ColumnSpan="1" RowSpan="1" BreakpointName="S"></dx:SpanRule>
                                <dx:SpanRule ColumnSpan="1" RowSpan="1" BreakpointName="M"></dx:SpanRule>
                            </SpanRules>
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer>
                                    <dx:ASPxButton runat="server" ID="btn_volver" Text="Volver"  Theme="Material" CssClass="bg-secondary w-50 me-5" AutoPostBack="true" OnClick="btn_volver_Click" Image-IconID="navigation_backward_16x16office2013" />
                                    <dx:ASPxButton runat="server" ID="btn_guardar" Text="Guardar" Theme="Material" OnClick="btn_guardar_Click" Image-IconID="actions_save_16x16devav" AutoPostBack="false" />
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>


                    </Items>
                </dx:LayoutGroup>
                            </Items>
                        </dx:ASPxFormLayout>
  </div>
                        </div>
                </div>
                <!-- /basic card -->
            </div>
        </div>

    </div>
     
</asp:Content>


<asp:Content ID="Content3" ContentPlaceHolderID="footer" runat="server">
 
</asp:Content>
