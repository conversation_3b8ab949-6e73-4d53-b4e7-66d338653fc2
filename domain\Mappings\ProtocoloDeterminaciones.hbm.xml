<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="ProtocoloDeterminaciones" table="ProtocoloDeterminaciones">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Resultado" type="Decimal">
      <column name="Resultado" not-null="false" precision="18" scale="8" sql-type="decimal" />
    </property>
    <property name="Signo" type="String">
      <column name="Signo" not-null="false" length="2" sql-type="nvarchar" />
    </property>
    <property name="Observacion" type="String">
      <column name="Observacion" not-null="false" sql-type="nvarchar" />
    </property>
    <property name="IdMetodosaMedir" type="Int32">
      <column name="IdMetodosaMedir" not-null="false" precision="10" scale="0" sql-type="int" />
    </property>
    <many-to-one name="Determinaciones" class="Determinaciones">
      <column name="IdDeterminacion" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <many-to-one name="Protocolos" class="Protocolos">
      <column name="IdProtocolo" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
  </class>
</hibernate-mapping>