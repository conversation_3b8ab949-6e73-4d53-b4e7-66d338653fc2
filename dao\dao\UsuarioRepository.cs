﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 11/03/2019 14:47:12
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class UsuarioRepository : NHibernateRepository<Domain.Usuario>, IUsuarioRepository
    {
        public UsuarioRepository(ISession session) : base(session)
        {
        }

        public virtual Domain.Usuario GetUsername(string username)
        {
            Usuario lista;
            lista = session.CreateQuery(string.Format("from Usuario where  Eliminado='False' and upper(Username) = :username "))
                .SetParameter("username", username).UniqueResult<Domain.Usuario>();
            if (lista != null)
            {
             
           
                foreach (Rol ra in lista.Rols)
                {
                    NHibernateUtil.Initialize(ra.RolModulos);
                }
            }
            return lista;
        }

        public virtual Domain.Usuario GetEmail(string email)
        {
            Usuario lista;
            lista = session.CreateQuery(string.Format("from Usuario where  upper(Email) = :email ")).SetParameter("email", email).UniqueResult<Domain.Usuario>();
            if (lista != null)
            {


                foreach (Rol ra in lista.Rols)
                {
                    NHibernateUtil.Initialize(ra.RolModulos);
                }
            }
            return lista;
        }

        public virtual IList<Domain.Usuario> GetAll()
        {
            return session.CreateQuery(string.Format("from Usuario")).List<Domain.Usuario>();
        }

        public virtual Domain.Usuario GetByKey(int _Id)
        {
            return session.Get<Domain.Usuario>(_Id);
        }

        public virtual Domain.Usuario Login(string username, string password)
        {
            var hql = "from Usuario v left join fetch v.Rol.RolAccesos where v.Username = :username and v.Password = :password and v.Activo = true and v.Eliminado = false  ";

            var q = session.CreateQuery(hql);
            q.SetParameter("username", username);
            q.SetParameter("password", password);

            return q.UniqueResult<Domain.Usuario>();
        }

        public virtual Domain.Usuario existe_UserName(int idUsuario, Usuario m)
        {
            string hql = "";
            if (idUsuario == -1)
                hql = "from Usuario m where m.Username = :Username  and  m.Eliminado='False'";
            else
                hql = "from Usuario m where m.Username = :Username  and m.Eliminado='False' and m.Id!= :IdUsuario";

            IQuery q = session.CreateQuery(hql);
            q.SetParameter("Username", m.Username);
            if (idUsuario != -1)
                q.SetParameter("IdUsuario", idUsuario);

            Usuario ma = q.UniqueResult<Domain.Usuario>();
            return ma;
        }
    }
}
