<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="Numeraciones" table="Numeraciones">
    <id name="Id" type="Int32">
      <column name="id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Codigo" type="String">
      <column name="codigo" not-null="false" length="50" sql-type="nvarchar" />
    </property>
    <property name="Descripcion" type="String">
      <column name="descripcion" not-null="false" length="50" sql-type="nvarchar" />
    </property>
    <property name="Numero" type="Int32">
      <column name="numero" not-null="false" precision="10" scale="0" sql-type="int" />
    </property>
    <property name="Año" type="Int32">
      <column name="año" not-null="false" precision="10" scale="0" sql-type="int" />
    </property>
  </class>
</hibernate-mapping>