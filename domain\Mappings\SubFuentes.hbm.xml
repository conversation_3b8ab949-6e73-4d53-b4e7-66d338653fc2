﻿<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="SubFuentes" table="SubFuentes">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Codigo" type="String">
      <column name="Codigo" not-null="false" length="10" sql-type="nvarchar" />
    </property>
    <property name="Descripcion" type="String">
      <column name="Descripcion" not-null="false" sql-type="nvarchar" />
    </property>
    <property name="Activa" type="Boolean">
      <column name="Activa" default="1" not-null="false" sql-type="bit" />
    </property>
    <property name="Lantitud" type="String">
      <column name="Lantitud" not-null="false" sql-type="nvarchar" />
    </property>
    <property name="Longitud" type="String">
      <column name="Longitud" not-null="false" sql-type="nvarchar" />
    </property>
    <property name="Calle" type="String">
      <column name="Calle" not-null="false" sql-type="nvarchar" />
    </property>
    <property name="Altura" type="Int32">
      <column name="Altura" not-null="false" precision="10" scale="0" sql-type="int" />
    </property>
    <property name="Observacion" type="String">
      <column name="Observacion" not-null="false" sql-type="nvarchar" />
    </property>
    <many-to-one name="Fuentes" class="Fuentes">
      <column name="IdFuente" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <set name="Protocolos" inverse="true" generic="true">
      <key>
        <column name="IdSubFuente" />
      </key>
      <one-to-many class="Protocolos" />
    </set>
  </class>
</hibernate-mapping>