﻿using DAO;
using Domain;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Moneda
{
    public class MonedaServices
    {
        public static Monedas GetById(int id)
        {
            Monedas m;

            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                m = new MonedasRepository(sess).GetByKey(id);

                if (m != null)
                {
                    NHibernateUtil.Initialize(m.Id);
                }

                sess.Close();
                sess.Dispose();
                return m;
            }

        }

        public static Monedas BuscarMoneda(string Simbolo)
        {
            Monedas M;

            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
               
                M = (Monedas)new MonedasRepository(sess).GetBySimbolo(Simbolo);

                if (M != null)
                {
                    NHibernateUtil.Initialize(M.<PERSON>);
                }

                sess.Close();
                sess.Dispose();
                return M;
            }

        }
        public static void SaveOrUpdate(Domain.Monedas m)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {

                        new MonedasRepository(sess).Add(m);

                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }

            }
        }
        public static void Delete(Domain.Monedas m)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new MonedasRepository(sess).Remove(m);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }

            }
        }
    }
}
