﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 22/5/2023 14:55:38
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.BancoCuentas, Domain in the schema.
    /// </summary>
    public partial class BancoCuentas {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for BancoCuentas constructor in the schema.
        /// </summary>
        public BancoCuentas()
        {
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Descripcion in the schema.
        /// </summary>
        public virtual string Descripcion
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Comision in the schema.
        /// </summary>
        public virtual System.Nullable<decimal> Comision
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Retencion in the schema.
        /// </summary>
        public virtual System.Nullable<decimal> Retencion
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Deshabilitada in the schema.
        /// </summary>
        public virtual System.Nullable<int> Deshabilitada
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for IdOtraMoneda in the schema.
        /// </summary>
        public virtual System.Nullable<int> IdOtraMoneda
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for IdPeriodo in the schema.
        /// </summary>
        public virtual int IdPeriodo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for BancosTarjetas in the schema.
        /// </summary>
        public virtual BancosTarjetas BancosTarjetas
        {
            get;
            set;
        }
    }

}
