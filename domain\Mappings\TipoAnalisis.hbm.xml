﻿<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="TipoAnalisis" table="TipoAnalisis">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Codigo" type="String">
      <column name="Codigo" not-null="false" length="5" sql-type="nvarchar" />
    </property>
    <property name="Descripcion" type="String">
      <column name="Descripcion" not-null="false" length="100" sql-type="nvarchar" />
    </property>
    <property name="Activo" type="Boolean">
      <column name="Activo" not-null="false" sql-type="bit" />
    </property>
    <set name="Protocolos" inverse="true" generic="true">
      <key>
        <column name="IdTipoAnalisis" />
      </key>
      <one-to-many class="Protocolos" />
    </set>
  </class>
</hibernate-mapping>