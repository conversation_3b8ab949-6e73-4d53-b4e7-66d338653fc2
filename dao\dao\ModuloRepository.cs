﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 11/03/2019 14:47:12
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;
using Domain.DTO;
using NHibernate.Transform;

namespace DAO
{
    public partial class CuponesTarjetasrepository : NHibernateRepository<Domain.Modulo>, IModuloRepository
    {
        public CuponesTarjetasrepository(ISession session) : base(session)
        {
        }

        public virtual IList<Domain.Modulo> GetAll()
        {
            return session.CreateQuery(string.Format("from Modulo")).List<Domain.Modulo>();
        }

        public virtual IList<Domain.Modulo> GetByApp(string codigo)
        {
            return session.CreateQuery(string.Format("from Modulo m where upper(m.Aplicacion.Codigo)=:codigo ")).SetParameter("codigo",codigo.Trim().ToUpper()).List<Domain.Modulo>();
        }

        public virtual Domain.Modulo GetByKey(int _Id)
        {
            return session.Get<Domain.Modulo>(_Id);
        }

      

        public virtual IList<Domain.Modulo> obtener_Accesos_By_NodoPadre(int nodo_Padre)
        {
            string hql = "from Modulo v where v.NodoPadre= :nodo_Padre order by v.NodoPadre, v.Orden";
            IQuery q = session.CreateQuery(hql);
            q.SetParameter("nodo_Padre", nodo_Padre);
            return q.List<Domain.Modulo>();
        }

        public virtual Domain.Modulo GetByNodo(int nodo_Padre)
        {
            string hql = "from Modulo v where v.Nodo= :nodo_Padre ";
            IQuery q = session.CreateQuery(hql);
            q.SetParameter("nodo_Padre", nodo_Padre);
            return q.UniqueResult<Domain.Modulo>();
        }

        public virtual Domain.Modulo GetByURL(string url)
        {
            string hql = "from Modulo v where upper(v.Url)= :url ";
            IQuery q = session.CreateQuery(hql);
            q.SetParameter("url", url.ToUpper());
            return q.UniqueResult<Domain.Modulo>();
        }

      

        public virtual IList<ModuloDTO> getByRol_DTO(int idRol, int Padre,int app)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                string cSql = "Select ";
                cSql += " Modulo.Id as Id,Modulo.icon as Icon, Modulo.Nodo_padre as 'NodoPadre', ";
                cSql += " Modulo.Nodo,Modulo.Descripcion,Modulo.Url from Modulo with(nolock) where nodo_padre=" + Padre;
                cSql += " and id in (select idModulo from RolModulo with(nolock) where idRol=" + idRol + ") " ;
                cSql += " order by orden,descripcion; ";

                var q = sess.CreateSQLQuery(cSql);
                q.SetResultTransformer(Transformers.AliasToBean<ModuloDTO>());

                return q.List<ModuloDTO>();
            }

        }

    }
}
