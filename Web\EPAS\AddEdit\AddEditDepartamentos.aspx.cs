﻿using Business.Services.Provincias;
using DevExpress.Web;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.Adapters;
using Domain;
using Business.Services.Epas;
using Business.Provincias;

namespace Web.EPAS.AddEdit
{
    public partial class AddEditDepartamentos : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                Txtcodigo.ClientEnabled = true;
                TxtID.Visible = false;
                btnVolverEditado.Visible = false;
                LlenadoDrop_Provincias();

                if (!IsPostBack)
                {


                    if (Request.QueryString["id"] != null)
                    {
                        //es edición
                        int iddpto = Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["id"].ToString()));
                        TxtID.Text = iddpto.ToString();
                        CargarDpto(iddpto);

                        Txtcodigo.ClientEnabled = false;
                        btnVolverEditado.Visible = false;

                    }
                    else
                    {
                        TxtID.Text = "0";
                       
                        CB_Activo.Checked = true;
                        btnVolverEditado.Visible = false;
                    }


                }
               
            }
            catch
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1",
                    "showMessage('Error al cargar la página, consulte al administrador del sistema', 'error');", true);
            }
        }

        protected void LlenadoDrop_Provincias()
        {

            cmbProvincias.DataSource = ProvinciasService.GetAll();
            cmbProvincias.TextField = "Descripcion";
            cmbProvincias.ValueField = "Id";
            cmbProvincias.DataBind();
            cmbProvincias.Items.Insert(0, new ListEditItem());

        }
        private void CargarDpto(int iddpto)
        {

            try
            {
                Domain.Departamentos dpto = DepartamentosService.GetById(id: iddpto);


                if (dpto != null)
                {
                    TxtID.Text=iddpto.ToString();
                    Txtcodigo.Text = dpto.Codigo.ToString();
                    Txtdescripcion.Text = dpto.Descripcion.ToString();

                    if (cmbProvincias.SelectedItem != null)
                    {
                        cmbProvincias.SelectedItem = cmbProvincias.Items.FindByValue(dpto.Provincias.Id.ToString());
                    }
                    else
                    {
                        cmbProvincias.SelectedItem= new ListEditItem();
                    }
                  
                    CB_Activo.Checked = Convert.ToBoolean(dpto.Activo);
                }

            }
            catch (Exception ex)
            {
            }

        }

        protected void btnGrabar_Click(object sender,EventArgs e) {

            try
            {
                Departamentos dep;

                //Verifica que se haya ingresado un codigo y nombre
                if (!string.IsNullOrEmpty(Txtcodigo.Text) && !string.IsNullOrEmpty(Txtdescripcion.Text))
                {
                    if (Request.QueryString["id"] != null)
                    {
                        // Se está editando un departamento existente.

                        int departamentoId = Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["id"]));
                        //Obtengo el objeto departamento                        
                        dep = DepartamentosService.GetById(departamentoId);
                        if (dep != null)
                        {
                            //El código si es edición, no se puede modificar, sólo actualizo descripción y activo y provincia
                            if (dep.Codigo.ToUpper().Equals(Txtcodigo.Text.Trim().ToUpper()))
                            {
                                dep.Descripcion = Txtdescripcion.Text.Trim().ToUpper();
                                dep.Provincias = ProvinciasService.GetById(Convert.ToInt32(cmbProvincias.SelectedItem.Value));


                                dep.Activo = CB_Activo.Checked;
                                DepartamentosService.SaveOrUpdate(dep);
                                Txtcodigo.ClientEnabled = false;
                                btnGrabar.ClientEnabled = false;
                                btnVolver.Visible = false;
                                btnVolverEditado.Visible = true;

                                ScriptManager.RegisterStartupScript(this, this.GetType(), "message3", "showMessage('Fue editado correctamente.', 'success');", true);
                            }
                        }
                        //  ScriptManager.RegisterStartupScript(this, this.GetType(), "message1", "showMessage('El departamento ya existe en la base de datos.', 'error');", true);                                             
                    }
                    else
                    {
                        // Se está creando un nuevo departamento.
                        // Verificar si ya existe en la lista local, solo se verifica el código
                        if (DepartamentosService.ExisteCodigo(Txtcodigo.Text.Trim()) || DepartamentosService.ExisteNombre(Txtdescripcion.Text.Trim()))
                        {
                            ScriptManager.RegisterStartupScript(this, this.GetType(), "message2", "showMessage('Ya existe un departamento con ese código o nombre, no se puede guardar.', 'error');", true);
                        }
                        else
                        {
                            //No existe el código, puedo guardar en la base
                            dep = new Departamentos();
                            dep.Codigo = Txtcodigo.Text.Trim().ToUpper();
                            dep.Descripcion = Txtdescripcion.Text.Trim().ToUpper();
                            dep.Provincias = ProvinciasService.GetById(Convert.ToInt32(cmbProvincias.SelectedItem.Value));

                            dep.Activo = CB_Activo.Checked;
                            DepartamentosService.SaveOrUpdate(dep);
                            ScriptManager.RegisterStartupScript(this, this.GetType(), "message3", "showMessage('Agregado correctamente.', 'success');", true);
                            Txtcodigo.ClientEnabled = false;
                        }

                    }



                }
                else
                {
                    ScriptManager.RegisterStartupScript(this, this.GetType(), "message4", "showMessage('Busque un departamento.', 'error');", true);
                }
            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "message5", "showMessage('Error al agregar Departamento, intente de nuevo.', 'error');", true);
            }
        }
    }
}

    
