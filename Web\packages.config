﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Antlr3.Runtime" version="3.5.2-rc1" targetFramework="net471" />
  <package id="Iesi.Collections" version="4.0.4" targetFramework="net471" />
  <package id="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" version="2.0.1" targetFramework="net471" />
  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net48" />
  <package id="NHibernate" version="5.3.3" targetFramework="net471" />
  <package id="NHibernate.Caches.SysCache" version="5.7.0" targetFramework="net471" />
  <package id="NHibernate.Caches.SysCache2" version="5.7.0" targetFramework="net471" />
  <package id="Remotion.Linq" version="2.2.0" targetFramework="net471" />
  <package id="Remotion.Linq.EagerFetching" version="2.2.0" targetFramework="net471" />
</packages>