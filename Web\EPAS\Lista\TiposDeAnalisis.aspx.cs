﻿using Business.Services.Epas;
using DevExpress.Web;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web.EPAS.Lista
{
    public partial class TiposDeAnalisis : System.Web.UI.Page
    {
        protected void Page_Load(object sender,EventArgs e)
        {
            try
            {
                    CargarDatosEnGv_TipoAnalisis();
                if (!IsPostBack)
                {

                }

            }
            catch (Exception ex)
            {
                string g = ex.Message;
            }
        }

        private void CargarDatosEnGv_TipoAnalisis()
        {
            try
            {

                List<Domain.TipoAnalisis> tipoAnalisis = (List<Domain.TipoAnalisis>) Business.Services.Epas.AnalisisTipoService.GetAll();
                gv_TipoAnalisis.DataSource = tipoAnalisis;
                gv_TipoAnalisis.DataBind();

            }
            catch (Exception ex)
            {
                string g = ex.Message;
            }

        }

        protected void btnEditar_Click(object sender, EventArgs e)
        {

            try
            {
                ASPxButton btn = (ASPxButton)sender;
                if (btn != null)
                {
                    GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer)btn.NamingContainer;
                    if (c != null)
                    {
                        var ind = DataBinder.Eval(c.DataItem, "Id");

                        if (ind != null)
                        {
                            Domain.TipoAnalisis tipoAnalisis = AnalisisTipoService.GetById(Convert.ToInt32(ind));

                            if(tipoAnalisis.Activo == false)
                            {
                                string message = string.Format("El Tipo de Análisis {0} no se puede editar ya que está anulado.",tipoAnalisis.Descripcion);
                                ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('" + message + "')",true);

                            } else
                            {
                                Response.Redirect(Global.ApplicationPath + "/EPAS/AddEdit/AddEditTiposDeAnalisis.aspx?id=" +
                                    Util.Helpers.Encrypt(ind.ToString()), false);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al tratar de editar un elemento, consulte al administrador del sistema', 'error');", true);
            }
        }

        protected void btnAnularActivar_Click(object sender, EventArgs e)
        {
            try
            {
                ASPxButton btn = (ASPxButton)sender;
                GridViewDataItemTemplateContainer f = (GridViewDataItemTemplateContainer)btn.NamingContainer;
                var ind = DataBinder.Eval(f.DataItem, "Id");

                Domain.TipoAnalisis Tipoanalisis = AnalisisTipoService.GetById(Convert.ToInt32(ind));
                Tipoanalisis.Activo = !Tipoanalisis.Activo;

                AnalisisTipoService.SaveOrUpdate(Tipoanalisis);
                CargarDatosEnGv_TipoAnalisis();


            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al tratar de eliminar un elemento, consulte al administrador del sistema', 'error');", true);

            }




        }
    }
}