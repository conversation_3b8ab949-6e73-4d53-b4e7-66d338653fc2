﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 10/8/2023 15:55:09
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.Recibos, Domain in the schema.
    /// </summary>
    public partial class Recibos {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for Recibos constructor in the schema.
        /// </summary>
        public Recibos()
        {
            this.Transferencias = new HashSet<Transferencias>();
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Descripcion in the schema.
        /// </summary>
        public virtual string Descripcion
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Importe in the schema.
        /// </summary>
        public virtual System.Nullable<decimal> Importe
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ValorOtraMoneda in the schema.
        /// </summary>
        public virtual System.Nullable<decimal> ValorOtraMoneda
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for NombrePagador in the schema.
        /// </summary>
        public virtual string NombrePagador
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Vuelto in the schema.
        /// </summary>
        public virtual decimal Vuelto
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Intereses in the schema.
        /// </summary>
        public virtual System.Nullable<decimal> Intereses
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Recargos in the schema.
        /// </summary>
        public virtual System.Nullable<decimal> Recargos
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ModificadoManual in the schema.
        /// </summary>
        public virtual System.Nullable<bool> ModificadoManual
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for IdMonedaDePago in the schema.
        /// </summary>
        public virtual System.Nullable<int> IdMonedaDePago
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for IdTipoRecibo in the schema.
        /// </summary>
        public virtual int IdTipoRecibo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Comprobantes in the schema.
        /// </summary>
        public virtual Comprobantes Comprobantes
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Cajas in the schema.
        /// </summary>
        public virtual Cajas Cajas
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Transferencias in the schema.
        /// </summary>
        public virtual ISet<Transferencias> Transferencias
        {
            get;
            set;
        }
    }

}
