﻿using Reportes;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web 
{
    public partial class Impresion : System.Web.UI.Page
    {

        protected void Page_Init(object sender, EventArgs e)
        {
            string FileName = Request.QueryString["FileName"];
            MemoryStream _ms = null;
            string filename = "";
            switch (FileName.ToLower())
            {
                case "protocolo":
                    {
                        string Parameter = Request.QueryString["Id"];
                        _ms = ReporteService.GetProtocolo(Convert.ToInt32(Parameter));
                        filename = "Protocolo-Nro" + Parameter + ".pdf";
                        break;
                    }


            }


            Response.Clear();
            Response.Buffer = true;
            Response.ContentType = "application/pdf";
            Response.AppendHeader("Content-Disposition", " filename=" + filename);
            Response.AppendHeader("content-length", _ms.Length.ToString());
            Response.BinaryWrite(_ms.ToArray());
            Response.Flush();
            Response.Close();
            Response.End();


        }


        protected void Page_Load(object sender, EventArgs e)
        {

        }
    }
}