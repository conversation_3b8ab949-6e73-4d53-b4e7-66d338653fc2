﻿using Common.DDL;
using DAO;
using Domain;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel.Channels;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Caja
{
    public class TarjetaServices
    {
        public static IList<BancosTarjetas> GetAll()
        {
            IList<BancosTarjetas> listaAux;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                listaAux = (IList<BancosTarjetas>)new BancosTarjetasRepository(sess).GetAll();
                sess.Close();
                sess.Dispose();
            }

            return (listaAux);
        }
        public static List<ItemCombo> GetByBuscarBancos(bool Activo, string Quees)
        {
            IList<BancosTarjetas> listaAux;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                listaAux = (IList<BancosTarjetas>)new BancosTarjetasRepository(sess).GetByBuscarBancos(true, "T");
                sess.Close();
                sess.Dispose();
            }

            ItemCombo item;
            ListaCombo lista = new ListaCombo();
            foreach (BancosTarjetas p in listaAux)
            {

                item = new ItemCombo();
                item.ID = p.Id.ToString();
                item.Descripcion = p.Nombre;
                lista.Add(item);
            }
            return (lista);
        }



        public static BancosTarjetas getById(int id)
        {
            BancosTarjetas u;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                u = new BancosTarjetasRepository(sess).GetByKey(id);

                if (u != null)
                {
                    NHibernateUtil.Initialize(u);

                }
                sess.Close();
                sess.Dispose();
                return u;
            }
        }
        public static void Delete(Domain.BancosTarjetas u)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new BancosTarjetasRepository(sess).Remove(u);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }

            }
        }
    }
}