﻿//------------------------------------------------------------------------------
// <auto-generated>
//     Este código fue generado por una herramienta.
//     Versión de runtime:4.0.30319.42000
//
//     Los cambios en este archivo podrían causar un comportamiento incorrecto y se perderán si
//     se vuelve a generar el código.
// </auto-generated>
//------------------------------------------------------------------------------

#pragma warning disable 1591

namespace Reportes.xsd {
    
    
    /// <summary>
    ///Represents a strongly typed in-memory cache of data.
    ///</summary>
    [global::System.Serializable()]
    [global::System.ComponentModel.DesignerCategoryAttribute("code")]
    [global::System.ComponentModel.ToolboxItem(true)]
    [global::System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedDataSetSchema")]
    [global::System.Xml.Serialization.XmlRootAttribute("ds_protocolo")]
    [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.DataSet")]
    public partial class ds_protocolo : global::System.Data.DataSet {
        
        private ProtocoloDataTable tableProtocolo;
        
        private DeterminacionesDataTable tableDeterminaciones;
        
        private global::System.Data.SchemaSerializationMode _schemaSerializationMode = global::System.Data.SchemaSerializationMode.IncludeSchema;
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        public ds_protocolo() {
            this.BeginInit();
            this.InitClass();
            global::System.ComponentModel.CollectionChangeEventHandler schemaChangedHandler = new global::System.ComponentModel.CollectionChangeEventHandler(this.SchemaChanged);
            base.Tables.CollectionChanged += schemaChangedHandler;
            base.Relations.CollectionChanged += schemaChangedHandler;
            this.EndInit();
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        protected ds_protocolo(global::System.Runtime.Serialization.SerializationInfo info, global::System.Runtime.Serialization.StreamingContext context) : 
                base(info, context, false) {
            if ((this.IsBinarySerialized(info, context) == true)) {
                this.InitVars(false);
                global::System.ComponentModel.CollectionChangeEventHandler schemaChangedHandler1 = new global::System.ComponentModel.CollectionChangeEventHandler(this.SchemaChanged);
                this.Tables.CollectionChanged += schemaChangedHandler1;
                this.Relations.CollectionChanged += schemaChangedHandler1;
                return;
            }
            string strSchema = ((string)(info.GetValue("XmlSchema", typeof(string))));
            if ((this.DetermineSchemaSerializationMode(info, context) == global::System.Data.SchemaSerializationMode.IncludeSchema)) {
                global::System.Data.DataSet ds = new global::System.Data.DataSet();
                ds.ReadXmlSchema(new global::System.Xml.XmlTextReader(new global::System.IO.StringReader(strSchema)));
                if ((ds.Tables["Protocolo"] != null)) {
                    base.Tables.Add(new ProtocoloDataTable(ds.Tables["Protocolo"]));
                }
                if ((ds.Tables["Determinaciones"] != null)) {
                    base.Tables.Add(new DeterminacionesDataTable(ds.Tables["Determinaciones"]));
                }
                this.DataSetName = ds.DataSetName;
                this.Prefix = ds.Prefix;
                this.Namespace = ds.Namespace;
                this.Locale = ds.Locale;
                this.CaseSensitive = ds.CaseSensitive;
                this.EnforceConstraints = ds.EnforceConstraints;
                this.Merge(ds, false, global::System.Data.MissingSchemaAction.Add);
                this.InitVars();
            }
            else {
                this.ReadXmlSchema(new global::System.Xml.XmlTextReader(new global::System.IO.StringReader(strSchema)));
            }
            this.GetSerializationData(info, context);
            global::System.ComponentModel.CollectionChangeEventHandler schemaChangedHandler = new global::System.ComponentModel.CollectionChangeEventHandler(this.SchemaChanged);
            base.Tables.CollectionChanged += schemaChangedHandler;
            this.Relations.CollectionChanged += schemaChangedHandler;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        [global::System.ComponentModel.Browsable(false)]
        [global::System.ComponentModel.DesignerSerializationVisibility(global::System.ComponentModel.DesignerSerializationVisibility.Content)]
        public ProtocoloDataTable Protocolo {
            get {
                return this.tableProtocolo;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        [global::System.ComponentModel.Browsable(false)]
        [global::System.ComponentModel.DesignerSerializationVisibility(global::System.ComponentModel.DesignerSerializationVisibility.Content)]
        public DeterminacionesDataTable Determinaciones {
            get {
                return this.tableDeterminaciones;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        [global::System.ComponentModel.BrowsableAttribute(true)]
        [global::System.ComponentModel.DesignerSerializationVisibilityAttribute(global::System.ComponentModel.DesignerSerializationVisibility.Visible)]
        public override global::System.Data.SchemaSerializationMode SchemaSerializationMode {
            get {
                return this._schemaSerializationMode;
            }
            set {
                this._schemaSerializationMode = value;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        [global::System.ComponentModel.DesignerSerializationVisibilityAttribute(global::System.ComponentModel.DesignerSerializationVisibility.Hidden)]
        public new global::System.Data.DataTableCollection Tables {
            get {
                return base.Tables;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        [global::System.ComponentModel.DesignerSerializationVisibilityAttribute(global::System.ComponentModel.DesignerSerializationVisibility.Hidden)]
        public new global::System.Data.DataRelationCollection Relations {
            get {
                return base.Relations;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        protected override void InitializeDerivedDataSet() {
            this.BeginInit();
            this.InitClass();
            this.EndInit();
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        public override global::System.Data.DataSet Clone() {
            ds_protocolo cln = ((ds_protocolo)(base.Clone()));
            cln.InitVars();
            cln.SchemaSerializationMode = this.SchemaSerializationMode;
            return cln;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        protected override bool ShouldSerializeTables() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        protected override bool ShouldSerializeRelations() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        protected override void ReadXmlSerializable(global::System.Xml.XmlReader reader) {
            if ((this.DetermineSchemaSerializationMode(reader) == global::System.Data.SchemaSerializationMode.IncludeSchema)) {
                this.Reset();
                global::System.Data.DataSet ds = new global::System.Data.DataSet();
                ds.ReadXml(reader);
                if ((ds.Tables["Protocolo"] != null)) {
                    base.Tables.Add(new ProtocoloDataTable(ds.Tables["Protocolo"]));
                }
                if ((ds.Tables["Determinaciones"] != null)) {
                    base.Tables.Add(new DeterminacionesDataTable(ds.Tables["Determinaciones"]));
                }
                this.DataSetName = ds.DataSetName;
                this.Prefix = ds.Prefix;
                this.Namespace = ds.Namespace;
                this.Locale = ds.Locale;
                this.CaseSensitive = ds.CaseSensitive;
                this.EnforceConstraints = ds.EnforceConstraints;
                this.Merge(ds, false, global::System.Data.MissingSchemaAction.Add);
                this.InitVars();
            }
            else {
                this.ReadXml(reader);
                this.InitVars();
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        protected override global::System.Xml.Schema.XmlSchema GetSchemaSerializable() {
            global::System.IO.MemoryStream stream = new global::System.IO.MemoryStream();
            this.WriteXmlSchema(new global::System.Xml.XmlTextWriter(stream, null));
            stream.Position = 0;
            return global::System.Xml.Schema.XmlSchema.Read(new global::System.Xml.XmlTextReader(stream), null);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        internal void InitVars() {
            this.InitVars(true);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        internal void InitVars(bool initTable) {
            this.tableProtocolo = ((ProtocoloDataTable)(base.Tables["Protocolo"]));
            if ((initTable == true)) {
                if ((this.tableProtocolo != null)) {
                    this.tableProtocolo.InitVars();
                }
            }
            this.tableDeterminaciones = ((DeterminacionesDataTable)(base.Tables["Determinaciones"]));
            if ((initTable == true)) {
                if ((this.tableDeterminaciones != null)) {
                    this.tableDeterminaciones.InitVars();
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        private void InitClass() {
            this.DataSetName = "ds_protocolo";
            this.Prefix = "";
            this.Namespace = "http://tempuri.org/ds_protocolo.xsd";
            this.EnforceConstraints = true;
            this.SchemaSerializationMode = global::System.Data.SchemaSerializationMode.IncludeSchema;
            this.tableProtocolo = new ProtocoloDataTable();
            base.Tables.Add(this.tableProtocolo);
            this.tableDeterminaciones = new DeterminacionesDataTable();
            base.Tables.Add(this.tableDeterminaciones);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        private bool ShouldSerializeProtocolo() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        private bool ShouldSerializeDeterminaciones() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        private void SchemaChanged(object sender, global::System.ComponentModel.CollectionChangeEventArgs e) {
            if ((e.Action == global::System.ComponentModel.CollectionChangeAction.Remove)) {
                this.InitVars();
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        public static global::System.Xml.Schema.XmlSchemaComplexType GetTypedDataSetSchema(global::System.Xml.Schema.XmlSchemaSet xs) {
            ds_protocolo ds = new ds_protocolo();
            global::System.Xml.Schema.XmlSchemaComplexType type = new global::System.Xml.Schema.XmlSchemaComplexType();
            global::System.Xml.Schema.XmlSchemaSequence sequence = new global::System.Xml.Schema.XmlSchemaSequence();
            global::System.Xml.Schema.XmlSchemaAny any = new global::System.Xml.Schema.XmlSchemaAny();
            any.Namespace = ds.Namespace;
            sequence.Items.Add(any);
            type.Particle = sequence;
            global::System.Xml.Schema.XmlSchema dsSchema = ds.GetSchemaSerializable();
            if (xs.Contains(dsSchema.TargetNamespace)) {
                global::System.IO.MemoryStream s1 = new global::System.IO.MemoryStream();
                global::System.IO.MemoryStream s2 = new global::System.IO.MemoryStream();
                try {
                    global::System.Xml.Schema.XmlSchema schema = null;
                    dsSchema.Write(s1);
                    for (global::System.Collections.IEnumerator schemas = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator(); schemas.MoveNext(); ) {
                        schema = ((global::System.Xml.Schema.XmlSchema)(schemas.Current));
                        s2.SetLength(0);
                        schema.Write(s2);
                        if ((s1.Length == s2.Length)) {
                            s1.Position = 0;
                            s2.Position = 0;
                            for (; ((s1.Position != s1.Length) 
                                        && (s1.ReadByte() == s2.ReadByte())); ) {
                                ;
                            }
                            if ((s1.Position == s1.Length)) {
                                return type;
                            }
                        }
                    }
                }
                finally {
                    if ((s1 != null)) {
                        s1.Close();
                    }
                    if ((s2 != null)) {
                        s2.Close();
                    }
                }
            }
            xs.Add(dsSchema);
            return type;
        }
        
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        public delegate void ProtocoloRowChangeEventHandler(object sender, ProtocoloRowChangeEvent e);
        
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        public delegate void DeterminacionesRowChangeEventHandler(object sender, DeterminacionesRowChangeEvent e);
        
        /// <summary>
        ///Represents the strongly named DataTable class.
        ///</summary>
        [global::System.Serializable()]
        [global::System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")]
        public partial class ProtocoloDataTable : global::System.Data.TypedTableBase<ProtocoloRow> {
            
            private global::System.Data.DataColumn columnId;
            
            private global::System.Data.DataColumn columnNroProtocolo;
            
            private global::System.Data.DataColumn columnFechaToma;
            
            private global::System.Data.DataColumn columnRecepcionToma;
            
            private global::System.Data.DataColumn columnObservacionSitioToma;
            
            private global::System.Data.DataColumn columnExpediente;
            
            private global::System.Data.DataColumn columnNro_de_Fuentes;
            
            private global::System.Data.DataColumn columnDescripcion_Fuentes;
            
            private global::System.Data.DataColumn columnDescripcion_SubFuentes;
            
            private global::System.Data.DataColumn columnLocalidad_Fuentes;
            
            private global::System.Data.DataColumn columnDescripcion_Departamento;
            
            private global::System.Data.DataColumn columnNombre_Protocolo_Estado;
            
            private global::System.Data.DataColumn columnNombre_Solicitante_Contrato;
            
            private global::System.Data.DataColumn columnNombre_Tomador_Contrato;
            
            private global::System.Data.DataColumn columnTipo_de_Analisis;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public ProtocoloDataTable() {
                this.TableName = "Protocolo";
                this.BeginInit();
                this.InitClass();
                this.EndInit();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            internal ProtocoloDataTable(global::System.Data.DataTable table) {
                this.TableName = table.TableName;
                if ((table.CaseSensitive != table.DataSet.CaseSensitive)) {
                    this.CaseSensitive = table.CaseSensitive;
                }
                if ((table.Locale.ToString() != table.DataSet.Locale.ToString())) {
                    this.Locale = table.Locale;
                }
                if ((table.Namespace != table.DataSet.Namespace)) {
                    this.Namespace = table.Namespace;
                }
                this.Prefix = table.Prefix;
                this.MinimumCapacity = table.MinimumCapacity;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected ProtocoloDataTable(global::System.Runtime.Serialization.SerializationInfo info, global::System.Runtime.Serialization.StreamingContext context) : 
                    base(info, context) {
                this.InitVars();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn IdColumn {
                get {
                    return this.columnId;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn NroProtocoloColumn {
                get {
                    return this.columnNroProtocolo;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn FechaTomaColumn {
                get {
                    return this.columnFechaToma;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn RecepcionTomaColumn {
                get {
                    return this.columnRecepcionToma;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn ObservacionSitioTomaColumn {
                get {
                    return this.columnObservacionSitioToma;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn ExpedienteColumn {
                get {
                    return this.columnExpediente;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Nro_de_FuentesColumn {
                get {
                    return this.columnNro_de_Fuentes;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Descripcion_FuentesColumn {
                get {
                    return this.columnDescripcion_Fuentes;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Descripcion_SubFuentesColumn {
                get {
                    return this.columnDescripcion_SubFuentes;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Localidad_FuentesColumn {
                get {
                    return this.columnLocalidad_Fuentes;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Descripcion_DepartamentoColumn {
                get {
                    return this.columnDescripcion_Departamento;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Nombre_Protocolo_EstadoColumn {
                get {
                    return this.columnNombre_Protocolo_Estado;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Nombre_Solicitante_ContratoColumn {
                get {
                    return this.columnNombre_Solicitante_Contrato;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Nombre_Tomador_ContratoColumn {
                get {
                    return this.columnNombre_Tomador_Contrato;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Tipo_de_AnalisisColumn {
                get {
                    return this.columnTipo_de_Analisis;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            [global::System.ComponentModel.Browsable(false)]
            public int Count {
                get {
                    return this.Rows.Count;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public ProtocoloRow this[int index] {
                get {
                    return ((ProtocoloRow)(this.Rows[index]));
                }
            }
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public event ProtocoloRowChangeEventHandler ProtocoloRowChanging;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public event ProtocoloRowChangeEventHandler ProtocoloRowChanged;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public event ProtocoloRowChangeEventHandler ProtocoloRowDeleting;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public event ProtocoloRowChangeEventHandler ProtocoloRowDeleted;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void AddProtocoloRow(ProtocoloRow row) {
                this.Rows.Add(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public ProtocoloRow AddProtocoloRow(int Id, int NroProtocolo, System.DateTime FechaToma, System.DateTime RecepcionToma, string ObservacionSitioToma, string Expediente, string Nro_de_Fuentes, string Descripcion_Fuentes, string Descripcion_SubFuentes, string Localidad_Fuentes, string Descripcion_Departamento, string Nombre_Protocolo_Estado, string Nombre_Solicitante_Contrato, string Nombre_Tomador_Contrato, string Tipo_de_Analisis) {
                ProtocoloRow rowProtocoloRow = ((ProtocoloRow)(this.NewRow()));
                object[] columnValuesArray = new object[] {
                        Id,
                        NroProtocolo,
                        FechaToma,
                        RecepcionToma,
                        ObservacionSitioToma,
                        Expediente,
                        Nro_de_Fuentes,
                        Descripcion_Fuentes,
                        Descripcion_SubFuentes,
                        Localidad_Fuentes,
                        Descripcion_Departamento,
                        Nombre_Protocolo_Estado,
                        Nombre_Solicitante_Contrato,
                        Nombre_Tomador_Contrato,
                        Tipo_de_Analisis};
                rowProtocoloRow.ItemArray = columnValuesArray;
                this.Rows.Add(rowProtocoloRow);
                return rowProtocoloRow;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public ProtocoloRow FindById(int Id) {
                return ((ProtocoloRow)(this.Rows.Find(new object[] {
                            Id})));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public override global::System.Data.DataTable Clone() {
                ProtocoloDataTable cln = ((ProtocoloDataTable)(base.Clone()));
                cln.InitVars();
                return cln;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override global::System.Data.DataTable CreateInstance() {
                return new ProtocoloDataTable();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            internal void InitVars() {
                this.columnId = base.Columns["Id"];
                this.columnNroProtocolo = base.Columns["NroProtocolo"];
                this.columnFechaToma = base.Columns["FechaToma"];
                this.columnRecepcionToma = base.Columns["RecepcionToma"];
                this.columnObservacionSitioToma = base.Columns["ObservacionSitioToma"];
                this.columnExpediente = base.Columns["Expediente"];
                this.columnNro_de_Fuentes = base.Columns["Nro_de_Fuentes"];
                this.columnDescripcion_Fuentes = base.Columns["Descripcion_Fuentes"];
                this.columnDescripcion_SubFuentes = base.Columns["Descripcion_SubFuentes"];
                this.columnLocalidad_Fuentes = base.Columns["Localidad_Fuentes"];
                this.columnDescripcion_Departamento = base.Columns["Descripcion_Departamento"];
                this.columnNombre_Protocolo_Estado = base.Columns["Nombre_Protocolo_Estado"];
                this.columnNombre_Solicitante_Contrato = base.Columns["Nombre_Solicitante_Contrato"];
                this.columnNombre_Tomador_Contrato = base.Columns["Nombre_Tomador_Contrato"];
                this.columnTipo_de_Analisis = base.Columns["Tipo_de_Analisis"];
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            private void InitClass() {
                this.columnId = new global::System.Data.DataColumn("Id", typeof(int), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnId);
                this.columnNroProtocolo = new global::System.Data.DataColumn("NroProtocolo", typeof(int), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnNroProtocolo);
                this.columnFechaToma = new global::System.Data.DataColumn("FechaToma", typeof(global::System.DateTime), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnFechaToma);
                this.columnRecepcionToma = new global::System.Data.DataColumn("RecepcionToma", typeof(global::System.DateTime), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnRecepcionToma);
                this.columnObservacionSitioToma = new global::System.Data.DataColumn("ObservacionSitioToma", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnObservacionSitioToma);
                this.columnExpediente = new global::System.Data.DataColumn("Expediente", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnExpediente);
                this.columnNro_de_Fuentes = new global::System.Data.DataColumn("Nro_de_Fuentes", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnNro_de_Fuentes);
                this.columnDescripcion_Fuentes = new global::System.Data.DataColumn("Descripcion_Fuentes", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnDescripcion_Fuentes);
                this.columnDescripcion_SubFuentes = new global::System.Data.DataColumn("Descripcion_SubFuentes", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnDescripcion_SubFuentes);
                this.columnLocalidad_Fuentes = new global::System.Data.DataColumn("Localidad_Fuentes", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnLocalidad_Fuentes);
                this.columnDescripcion_Departamento = new global::System.Data.DataColumn("Descripcion_Departamento", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnDescripcion_Departamento);
                this.columnNombre_Protocolo_Estado = new global::System.Data.DataColumn("Nombre_Protocolo_Estado", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnNombre_Protocolo_Estado);
                this.columnNombre_Solicitante_Contrato = new global::System.Data.DataColumn("Nombre_Solicitante_Contrato", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnNombre_Solicitante_Contrato);
                this.columnNombre_Tomador_Contrato = new global::System.Data.DataColumn("Nombre_Tomador_Contrato", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnNombre_Tomador_Contrato);
                this.columnTipo_de_Analisis = new global::System.Data.DataColumn("Tipo_de_Analisis", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnTipo_de_Analisis);
                this.Constraints.Add(new global::System.Data.UniqueConstraint("ProtocoloKey1", new global::System.Data.DataColumn[] {
                                this.columnId}, true));
                this.columnId.AllowDBNull = false;
                this.columnId.Unique = true;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public ProtocoloRow NewProtocoloRow() {
                return ((ProtocoloRow)(this.NewRow()));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override global::System.Data.DataRow NewRowFromBuilder(global::System.Data.DataRowBuilder builder) {
                return new ProtocoloRow(builder);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override global::System.Type GetRowType() {
                return typeof(ProtocoloRow);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override void OnRowChanged(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanged(e);
                if ((this.ProtocoloRowChanged != null)) {
                    this.ProtocoloRowChanged(this, new ProtocoloRowChangeEvent(((ProtocoloRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override void OnRowChanging(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanging(e);
                if ((this.ProtocoloRowChanging != null)) {
                    this.ProtocoloRowChanging(this, new ProtocoloRowChangeEvent(((ProtocoloRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override void OnRowDeleted(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleted(e);
                if ((this.ProtocoloRowDeleted != null)) {
                    this.ProtocoloRowDeleted(this, new ProtocoloRowChangeEvent(((ProtocoloRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override void OnRowDeleting(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleting(e);
                if ((this.ProtocoloRowDeleting != null)) {
                    this.ProtocoloRowDeleting(this, new ProtocoloRowChangeEvent(((ProtocoloRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void RemoveProtocoloRow(ProtocoloRow row) {
                this.Rows.Remove(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public static global::System.Xml.Schema.XmlSchemaComplexType GetTypedTableSchema(global::System.Xml.Schema.XmlSchemaSet xs) {
                global::System.Xml.Schema.XmlSchemaComplexType type = new global::System.Xml.Schema.XmlSchemaComplexType();
                global::System.Xml.Schema.XmlSchemaSequence sequence = new global::System.Xml.Schema.XmlSchemaSequence();
                ds_protocolo ds = new ds_protocolo();
                global::System.Xml.Schema.XmlSchemaAny any1 = new global::System.Xml.Schema.XmlSchemaAny();
                any1.Namespace = "http://www.w3.org/2001/XMLSchema";
                any1.MinOccurs = new decimal(0);
                any1.MaxOccurs = decimal.MaxValue;
                any1.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any1);
                global::System.Xml.Schema.XmlSchemaAny any2 = new global::System.Xml.Schema.XmlSchemaAny();
                any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1";
                any2.MinOccurs = new decimal(1);
                any2.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any2);
                global::System.Xml.Schema.XmlSchemaAttribute attribute1 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute1.Name = "namespace";
                attribute1.FixedValue = ds.Namespace;
                type.Attributes.Add(attribute1);
                global::System.Xml.Schema.XmlSchemaAttribute attribute2 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute2.Name = "tableTypeName";
                attribute2.FixedValue = "ProtocoloDataTable";
                type.Attributes.Add(attribute2);
                type.Particle = sequence;
                global::System.Xml.Schema.XmlSchema dsSchema = ds.GetSchemaSerializable();
                if (xs.Contains(dsSchema.TargetNamespace)) {
                    global::System.IO.MemoryStream s1 = new global::System.IO.MemoryStream();
                    global::System.IO.MemoryStream s2 = new global::System.IO.MemoryStream();
                    try {
                        global::System.Xml.Schema.XmlSchema schema = null;
                        dsSchema.Write(s1);
                        for (global::System.Collections.IEnumerator schemas = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator(); schemas.MoveNext(); ) {
                            schema = ((global::System.Xml.Schema.XmlSchema)(schemas.Current));
                            s2.SetLength(0);
                            schema.Write(s2);
                            if ((s1.Length == s2.Length)) {
                                s1.Position = 0;
                                s2.Position = 0;
                                for (; ((s1.Position != s1.Length) 
                                            && (s1.ReadByte() == s2.ReadByte())); ) {
                                    ;
                                }
                                if ((s1.Position == s1.Length)) {
                                    return type;
                                }
                            }
                        }
                    }
                    finally {
                        if ((s1 != null)) {
                            s1.Close();
                        }
                        if ((s2 != null)) {
                            s2.Close();
                        }
                    }
                }
                xs.Add(dsSchema);
                return type;
            }
        }
        
        /// <summary>
        ///Represents the strongly named DataTable class.
        ///</summary>
        [global::System.Serializable()]
        [global::System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")]
        public partial class DeterminacionesDataTable : global::System.Data.TypedTableBase<DeterminacionesRow> {
            
            private global::System.Data.DataColumn columnId;
            
            private global::System.Data.DataColumn columnNroDeterminacion;
            
            private global::System.Data.DataColumn columnDescripcion;
            
            private global::System.Data.DataColumn columnUnidad_primaria;
            
            private global::System.Data.DataColumn columnUnidad_secundaria;
            
            private global::System.Data.DataColumn columnFactor_conversion_Prefijo;
            
            private global::System.Data.DataColumn columnFactor_conversion_Sufijo;
            
            private global::System.Data.DataColumn columnValorReferencia;
            
            private global::System.Data.DataColumn columnDecimales_mostrar;
            
            private global::System.Data.DataColumn columnActivo;
            
            private global::System.Data.DataColumn columnResultado;
            
            private global::System.Data.DataColumn columnSigno;
            
            private global::System.Data.DataColumn columnDescripcion_TipoAnalisis;
            
            private global::System.Data.DataColumn columnObservacion;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public DeterminacionesDataTable() {
                this.TableName = "Determinaciones";
                this.BeginInit();
                this.InitClass();
                this.EndInit();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            internal DeterminacionesDataTable(global::System.Data.DataTable table) {
                this.TableName = table.TableName;
                if ((table.CaseSensitive != table.DataSet.CaseSensitive)) {
                    this.CaseSensitive = table.CaseSensitive;
                }
                if ((table.Locale.ToString() != table.DataSet.Locale.ToString())) {
                    this.Locale = table.Locale;
                }
                if ((table.Namespace != table.DataSet.Namespace)) {
                    this.Namespace = table.Namespace;
                }
                this.Prefix = table.Prefix;
                this.MinimumCapacity = table.MinimumCapacity;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected DeterminacionesDataTable(global::System.Runtime.Serialization.SerializationInfo info, global::System.Runtime.Serialization.StreamingContext context) : 
                    base(info, context) {
                this.InitVars();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn IdColumn {
                get {
                    return this.columnId;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn NroDeterminacionColumn {
                get {
                    return this.columnNroDeterminacion;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn DescripcionColumn {
                get {
                    return this.columnDescripcion;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Unidad_primariaColumn {
                get {
                    return this.columnUnidad_primaria;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Unidad_secundariaColumn {
                get {
                    return this.columnUnidad_secundaria;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Factor_conversion_PrefijoColumn {
                get {
                    return this.columnFactor_conversion_Prefijo;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Factor_conversion_SufijoColumn {
                get {
                    return this.columnFactor_conversion_Sufijo;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn ValorReferenciaColumn {
                get {
                    return this.columnValorReferencia;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Decimales_mostrarColumn {
                get {
                    return this.columnDecimales_mostrar;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn ActivoColumn {
                get {
                    return this.columnActivo;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn ResultadoColumn {
                get {
                    return this.columnResultado;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn SignoColumn {
                get {
                    return this.columnSigno;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Descripcion_TipoAnalisisColumn {
                get {
                    return this.columnDescripcion_TipoAnalisis;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn ObservacionColumn {
                get {
                    return this.columnObservacion;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            [global::System.ComponentModel.Browsable(false)]
            public int Count {
                get {
                    return this.Rows.Count;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public DeterminacionesRow this[int index] {
                get {
                    return ((DeterminacionesRow)(this.Rows[index]));
                }
            }
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public event DeterminacionesRowChangeEventHandler DeterminacionesRowChanging;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public event DeterminacionesRowChangeEventHandler DeterminacionesRowChanged;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public event DeterminacionesRowChangeEventHandler DeterminacionesRowDeleting;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public event DeterminacionesRowChangeEventHandler DeterminacionesRowDeleted;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void AddDeterminacionesRow(DeterminacionesRow row) {
                this.Rows.Add(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public DeterminacionesRow AddDeterminacionesRow(int Id, int NroDeterminacion, string Descripcion, string Unidad_primaria, string Unidad_secundaria, decimal Factor_conversion_Prefijo, decimal Factor_conversion_Sufijo, string ValorReferencia, int Decimales_mostrar, bool Activo, decimal Resultado, string Signo, string Descripcion_TipoAnalisis, string Observacion) {
                DeterminacionesRow rowDeterminacionesRow = ((DeterminacionesRow)(this.NewRow()));
                object[] columnValuesArray = new object[] {
                        Id,
                        NroDeterminacion,
                        Descripcion,
                        Unidad_primaria,
                        Unidad_secundaria,
                        Factor_conversion_Prefijo,
                        Factor_conversion_Sufijo,
                        ValorReferencia,
                        Decimales_mostrar,
                        Activo,
                        Resultado,
                        Signo,
                        Descripcion_TipoAnalisis,
                        Observacion};
                rowDeterminacionesRow.ItemArray = columnValuesArray;
                this.Rows.Add(rowDeterminacionesRow);
                return rowDeterminacionesRow;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public DeterminacionesRow FindById(int Id) {
                return ((DeterminacionesRow)(this.Rows.Find(new object[] {
                            Id})));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public override global::System.Data.DataTable Clone() {
                DeterminacionesDataTable cln = ((DeterminacionesDataTable)(base.Clone()));
                cln.InitVars();
                return cln;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override global::System.Data.DataTable CreateInstance() {
                return new DeterminacionesDataTable();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            internal void InitVars() {
                this.columnId = base.Columns["Id"];
                this.columnNroDeterminacion = base.Columns["NroDeterminacion"];
                this.columnDescripcion = base.Columns["Descripcion"];
                this.columnUnidad_primaria = base.Columns["Unidad_primaria"];
                this.columnUnidad_secundaria = base.Columns["Unidad_secundaria"];
                this.columnFactor_conversion_Prefijo = base.Columns["Factor_conversion_Prefijo"];
                this.columnFactor_conversion_Sufijo = base.Columns["Factor_conversion_Sufijo"];
                this.columnValorReferencia = base.Columns["ValorReferencia"];
                this.columnDecimales_mostrar = base.Columns["Decimales_mostrar"];
                this.columnActivo = base.Columns["Activo"];
                this.columnResultado = base.Columns["Resultado"];
                this.columnSigno = base.Columns["Signo"];
                this.columnDescripcion_TipoAnalisis = base.Columns["Descripcion_TipoAnalisis"];
                this.columnObservacion = base.Columns["Observacion"];
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            private void InitClass() {
                this.columnId = new global::System.Data.DataColumn("Id", typeof(int), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnId);
                this.columnNroDeterminacion = new global::System.Data.DataColumn("NroDeterminacion", typeof(int), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnNroDeterminacion);
                this.columnDescripcion = new global::System.Data.DataColumn("Descripcion", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnDescripcion);
                this.columnUnidad_primaria = new global::System.Data.DataColumn("Unidad_primaria", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnUnidad_primaria);
                this.columnUnidad_secundaria = new global::System.Data.DataColumn("Unidad_secundaria", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnUnidad_secundaria);
                this.columnFactor_conversion_Prefijo = new global::System.Data.DataColumn("Factor_conversion_Prefijo", typeof(decimal), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnFactor_conversion_Prefijo);
                this.columnFactor_conversion_Sufijo = new global::System.Data.DataColumn("Factor_conversion_Sufijo", typeof(decimal), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnFactor_conversion_Sufijo);
                this.columnValorReferencia = new global::System.Data.DataColumn("ValorReferencia", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnValorReferencia);
                this.columnDecimales_mostrar = new global::System.Data.DataColumn("Decimales_mostrar", typeof(int), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnDecimales_mostrar);
                this.columnActivo = new global::System.Data.DataColumn("Activo", typeof(bool), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnActivo);
                this.columnResultado = new global::System.Data.DataColumn("Resultado", typeof(decimal), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnResultado);
                this.columnSigno = new global::System.Data.DataColumn("Signo", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnSigno);
                this.columnDescripcion_TipoAnalisis = new global::System.Data.DataColumn("Descripcion_TipoAnalisis", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnDescripcion_TipoAnalisis);
                this.columnObservacion = new global::System.Data.DataColumn("Observacion", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnObservacion);
                this.Constraints.Add(new global::System.Data.UniqueConstraint("DeterminacionesKey1", new global::System.Data.DataColumn[] {
                                this.columnId}, true));
                this.columnId.AllowDBNull = false;
                this.columnId.Unique = true;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public DeterminacionesRow NewDeterminacionesRow() {
                return ((DeterminacionesRow)(this.NewRow()));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override global::System.Data.DataRow NewRowFromBuilder(global::System.Data.DataRowBuilder builder) {
                return new DeterminacionesRow(builder);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override global::System.Type GetRowType() {
                return typeof(DeterminacionesRow);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override void OnRowChanged(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanged(e);
                if ((this.DeterminacionesRowChanged != null)) {
                    this.DeterminacionesRowChanged(this, new DeterminacionesRowChangeEvent(((DeterminacionesRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override void OnRowChanging(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanging(e);
                if ((this.DeterminacionesRowChanging != null)) {
                    this.DeterminacionesRowChanging(this, new DeterminacionesRowChangeEvent(((DeterminacionesRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override void OnRowDeleted(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleted(e);
                if ((this.DeterminacionesRowDeleted != null)) {
                    this.DeterminacionesRowDeleted(this, new DeterminacionesRowChangeEvent(((DeterminacionesRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override void OnRowDeleting(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleting(e);
                if ((this.DeterminacionesRowDeleting != null)) {
                    this.DeterminacionesRowDeleting(this, new DeterminacionesRowChangeEvent(((DeterminacionesRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void RemoveDeterminacionesRow(DeterminacionesRow row) {
                this.Rows.Remove(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public static global::System.Xml.Schema.XmlSchemaComplexType GetTypedTableSchema(global::System.Xml.Schema.XmlSchemaSet xs) {
                global::System.Xml.Schema.XmlSchemaComplexType type = new global::System.Xml.Schema.XmlSchemaComplexType();
                global::System.Xml.Schema.XmlSchemaSequence sequence = new global::System.Xml.Schema.XmlSchemaSequence();
                ds_protocolo ds = new ds_protocolo();
                global::System.Xml.Schema.XmlSchemaAny any1 = new global::System.Xml.Schema.XmlSchemaAny();
                any1.Namespace = "http://www.w3.org/2001/XMLSchema";
                any1.MinOccurs = new decimal(0);
                any1.MaxOccurs = decimal.MaxValue;
                any1.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any1);
                global::System.Xml.Schema.XmlSchemaAny any2 = new global::System.Xml.Schema.XmlSchemaAny();
                any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1";
                any2.MinOccurs = new decimal(1);
                any2.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any2);
                global::System.Xml.Schema.XmlSchemaAttribute attribute1 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute1.Name = "namespace";
                attribute1.FixedValue = ds.Namespace;
                type.Attributes.Add(attribute1);
                global::System.Xml.Schema.XmlSchemaAttribute attribute2 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute2.Name = "tableTypeName";
                attribute2.FixedValue = "DeterminacionesDataTable";
                type.Attributes.Add(attribute2);
                type.Particle = sequence;
                global::System.Xml.Schema.XmlSchema dsSchema = ds.GetSchemaSerializable();
                if (xs.Contains(dsSchema.TargetNamespace)) {
                    global::System.IO.MemoryStream s1 = new global::System.IO.MemoryStream();
                    global::System.IO.MemoryStream s2 = new global::System.IO.MemoryStream();
                    try {
                        global::System.Xml.Schema.XmlSchema schema = null;
                        dsSchema.Write(s1);
                        for (global::System.Collections.IEnumerator schemas = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator(); schemas.MoveNext(); ) {
                            schema = ((global::System.Xml.Schema.XmlSchema)(schemas.Current));
                            s2.SetLength(0);
                            schema.Write(s2);
                            if ((s1.Length == s2.Length)) {
                                s1.Position = 0;
                                s2.Position = 0;
                                for (; ((s1.Position != s1.Length) 
                                            && (s1.ReadByte() == s2.ReadByte())); ) {
                                    ;
                                }
                                if ((s1.Position == s1.Length)) {
                                    return type;
                                }
                            }
                        }
                    }
                    finally {
                        if ((s1 != null)) {
                            s1.Close();
                        }
                        if ((s2 != null)) {
                            s2.Close();
                        }
                    }
                }
                xs.Add(dsSchema);
                return type;
            }
        }
        
        /// <summary>
        ///Represents strongly named DataRow class.
        ///</summary>
        public partial class ProtocoloRow : global::System.Data.DataRow {
            
            private ProtocoloDataTable tableProtocolo;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            internal ProtocoloRow(global::System.Data.DataRowBuilder rb) : 
                    base(rb) {
                this.tableProtocolo = ((ProtocoloDataTable)(this.Table));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public int Id {
                get {
                    return ((int)(this[this.tableProtocolo.IdColumn]));
                }
                set {
                    this[this.tableProtocolo.IdColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public int NroProtocolo {
                get {
                    try {
                        return ((int)(this[this.tableProtocolo.NroProtocoloColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("El valor de la columna \'NroProtocolo\' de la tabla \'Protocolo\' es DBNull.", e);
                    }
                }
                set {
                    this[this.tableProtocolo.NroProtocoloColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public System.DateTime FechaToma {
                get {
                    try {
                        return ((global::System.DateTime)(this[this.tableProtocolo.FechaTomaColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("El valor de la columna \'FechaToma\' de la tabla \'Protocolo\' es DBNull.", e);
                    }
                }
                set {
                    this[this.tableProtocolo.FechaTomaColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public System.DateTime RecepcionToma {
                get {
                    try {
                        return ((global::System.DateTime)(this[this.tableProtocolo.RecepcionTomaColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("El valor de la columna \'RecepcionToma\' de la tabla \'Protocolo\' es DBNull.", e);
                    }
                }
                set {
                    this[this.tableProtocolo.RecepcionTomaColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string ObservacionSitioToma {
                get {
                    try {
                        return ((string)(this[this.tableProtocolo.ObservacionSitioTomaColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("El valor de la columna \'ObservacionSitioToma\' de la tabla \'Protocolo\' es DBNull.", e);
                    }
                }
                set {
                    this[this.tableProtocolo.ObservacionSitioTomaColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Expediente {
                get {
                    try {
                        return ((string)(this[this.tableProtocolo.ExpedienteColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("El valor de la columna \'Expediente\' de la tabla \'Protocolo\' es DBNull.", e);
                    }
                }
                set {
                    this[this.tableProtocolo.ExpedienteColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Nro_de_Fuentes {
                get {
                    try {
                        return ((string)(this[this.tableProtocolo.Nro_de_FuentesColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("El valor de la columna \'Nro_de_Fuentes\' de la tabla \'Protocolo\' es DBNull.", e);
                    }
                }
                set {
                    this[this.tableProtocolo.Nro_de_FuentesColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Descripcion_Fuentes {
                get {
                    try {
                        return ((string)(this[this.tableProtocolo.Descripcion_FuentesColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("El valor de la columna \'Descripcion_Fuentes\' de la tabla \'Protocolo\' es DBNull.", e);
                    }
                }
                set {
                    this[this.tableProtocolo.Descripcion_FuentesColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Descripcion_SubFuentes {
                get {
                    try {
                        return ((string)(this[this.tableProtocolo.Descripcion_SubFuentesColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("El valor de la columna \'Descripcion_SubFuentes\' de la tabla \'Protocolo\' es DBNull" +
                                ".", e);
                    }
                }
                set {
                    this[this.tableProtocolo.Descripcion_SubFuentesColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Localidad_Fuentes {
                get {
                    try {
                        return ((string)(this[this.tableProtocolo.Localidad_FuentesColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("El valor de la columna \'Localidad_Fuentes\' de la tabla \'Protocolo\' es DBNull.", e);
                    }
                }
                set {
                    this[this.tableProtocolo.Localidad_FuentesColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Descripcion_Departamento {
                get {
                    try {
                        return ((string)(this[this.tableProtocolo.Descripcion_DepartamentoColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("El valor de la columna \'Descripcion_Departamento\' de la tabla \'Protocolo\' es DBNu" +
                                "ll.", e);
                    }
                }
                set {
                    this[this.tableProtocolo.Descripcion_DepartamentoColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Nombre_Protocolo_Estado {
                get {
                    try {
                        return ((string)(this[this.tableProtocolo.Nombre_Protocolo_EstadoColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("El valor de la columna \'Nombre_Protocolo_Estado\' de la tabla \'Protocolo\' es DBNul" +
                                "l.", e);
                    }
                }
                set {
                    this[this.tableProtocolo.Nombre_Protocolo_EstadoColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Nombre_Solicitante_Contrato {
                get {
                    try {
                        return ((string)(this[this.tableProtocolo.Nombre_Solicitante_ContratoColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("El valor de la columna \'Nombre_Solicitante_Contrato\' de la tabla \'Protocolo\' es D" +
                                "BNull.", e);
                    }
                }
                set {
                    this[this.tableProtocolo.Nombre_Solicitante_ContratoColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Nombre_Tomador_Contrato {
                get {
                    try {
                        return ((string)(this[this.tableProtocolo.Nombre_Tomador_ContratoColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("El valor de la columna \'Nombre_Tomador_Contrato\' de la tabla \'Protocolo\' es DBNul" +
                                "l.", e);
                    }
                }
                set {
                    this[this.tableProtocolo.Nombre_Tomador_ContratoColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Tipo_de_Analisis {
                get {
                    try {
                        return ((string)(this[this.tableProtocolo.Tipo_de_AnalisisColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("El valor de la columna \'Tipo_de_Analisis\' de la tabla \'Protocolo\' es DBNull.", e);
                    }
                }
                set {
                    this[this.tableProtocolo.Tipo_de_AnalisisColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsNroProtocoloNull() {
                return this.IsNull(this.tableProtocolo.NroProtocoloColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetNroProtocoloNull() {
                this[this.tableProtocolo.NroProtocoloColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsFechaTomaNull() {
                return this.IsNull(this.tableProtocolo.FechaTomaColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetFechaTomaNull() {
                this[this.tableProtocolo.FechaTomaColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsRecepcionTomaNull() {
                return this.IsNull(this.tableProtocolo.RecepcionTomaColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetRecepcionTomaNull() {
                this[this.tableProtocolo.RecepcionTomaColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsObservacionSitioTomaNull() {
                return this.IsNull(this.tableProtocolo.ObservacionSitioTomaColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetObservacionSitioTomaNull() {
                this[this.tableProtocolo.ObservacionSitioTomaColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsExpedienteNull() {
                return this.IsNull(this.tableProtocolo.ExpedienteColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetExpedienteNull() {
                this[this.tableProtocolo.ExpedienteColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsNro_de_FuentesNull() {
                return this.IsNull(this.tableProtocolo.Nro_de_FuentesColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetNro_de_FuentesNull() {
                this[this.tableProtocolo.Nro_de_FuentesColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsDescripcion_FuentesNull() {
                return this.IsNull(this.tableProtocolo.Descripcion_FuentesColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetDescripcion_FuentesNull() {
                this[this.tableProtocolo.Descripcion_FuentesColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsDescripcion_SubFuentesNull() {
                return this.IsNull(this.tableProtocolo.Descripcion_SubFuentesColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetDescripcion_SubFuentesNull() {
                this[this.tableProtocolo.Descripcion_SubFuentesColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsLocalidad_FuentesNull() {
                return this.IsNull(this.tableProtocolo.Localidad_FuentesColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetLocalidad_FuentesNull() {
                this[this.tableProtocolo.Localidad_FuentesColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsDescripcion_DepartamentoNull() {
                return this.IsNull(this.tableProtocolo.Descripcion_DepartamentoColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetDescripcion_DepartamentoNull() {
                this[this.tableProtocolo.Descripcion_DepartamentoColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsNombre_Protocolo_EstadoNull() {
                return this.IsNull(this.tableProtocolo.Nombre_Protocolo_EstadoColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetNombre_Protocolo_EstadoNull() {
                this[this.tableProtocolo.Nombre_Protocolo_EstadoColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsNombre_Solicitante_ContratoNull() {
                return this.IsNull(this.tableProtocolo.Nombre_Solicitante_ContratoColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetNombre_Solicitante_ContratoNull() {
                this[this.tableProtocolo.Nombre_Solicitante_ContratoColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsNombre_Tomador_ContratoNull() {
                return this.IsNull(this.tableProtocolo.Nombre_Tomador_ContratoColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetNombre_Tomador_ContratoNull() {
                this[this.tableProtocolo.Nombre_Tomador_ContratoColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsTipo_de_AnalisisNull() {
                return this.IsNull(this.tableProtocolo.Tipo_de_AnalisisColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetTipo_de_AnalisisNull() {
                this[this.tableProtocolo.Tipo_de_AnalisisColumn] = global::System.Convert.DBNull;
            }
        }
        
        /// <summary>
        ///Represents strongly named DataRow class.
        ///</summary>
        public partial class DeterminacionesRow : global::System.Data.DataRow {
            
            private DeterminacionesDataTable tableDeterminaciones;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            internal DeterminacionesRow(global::System.Data.DataRowBuilder rb) : 
                    base(rb) {
                this.tableDeterminaciones = ((DeterminacionesDataTable)(this.Table));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public int Id {
                get {
                    return ((int)(this[this.tableDeterminaciones.IdColumn]));
                }
                set {
                    this[this.tableDeterminaciones.IdColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public int NroDeterminacion {
                get {
                    try {
                        return ((int)(this[this.tableDeterminaciones.NroDeterminacionColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("El valor de la columna \'NroDeterminacion\' de la tabla \'Determinaciones\' es DBNull" +
                                ".", e);
                    }
                }
                set {
                    this[this.tableDeterminaciones.NroDeterminacionColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Descripcion {
                get {
                    try {
                        return ((string)(this[this.tableDeterminaciones.DescripcionColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("El valor de la columna \'Descripcion\' de la tabla \'Determinaciones\' es DBNull.", e);
                    }
                }
                set {
                    this[this.tableDeterminaciones.DescripcionColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Unidad_primaria {
                get {
                    try {
                        return ((string)(this[this.tableDeterminaciones.Unidad_primariaColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("El valor de la columna \'Unidad_primaria\' de la tabla \'Determinaciones\' es DBNull." +
                                "", e);
                    }
                }
                set {
                    this[this.tableDeterminaciones.Unidad_primariaColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Unidad_secundaria {
                get {
                    try {
                        return ((string)(this[this.tableDeterminaciones.Unidad_secundariaColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("El valor de la columna \'Unidad_secundaria\' de la tabla \'Determinaciones\' es DBNul" +
                                "l.", e);
                    }
                }
                set {
                    this[this.tableDeterminaciones.Unidad_secundariaColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public decimal Factor_conversion_Prefijo {
                get {
                    try {
                        return ((decimal)(this[this.tableDeterminaciones.Factor_conversion_PrefijoColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("El valor de la columna \'Factor_conversion_Prefijo\' de la tabla \'Determinaciones\' " +
                                "es DBNull.", e);
                    }
                }
                set {
                    this[this.tableDeterminaciones.Factor_conversion_PrefijoColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public decimal Factor_conversion_Sufijo {
                get {
                    try {
                        return ((decimal)(this[this.tableDeterminaciones.Factor_conversion_SufijoColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("El valor de la columna \'Factor_conversion_Sufijo\' de la tabla \'Determinaciones\' e" +
                                "s DBNull.", e);
                    }
                }
                set {
                    this[this.tableDeterminaciones.Factor_conversion_SufijoColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string ValorReferencia {
                get {
                    try {
                        return ((string)(this[this.tableDeterminaciones.ValorReferenciaColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("El valor de la columna \'ValorReferencia\' de la tabla \'Determinaciones\' es DBNull." +
                                "", e);
                    }
                }
                set {
                    this[this.tableDeterminaciones.ValorReferenciaColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public int Decimales_mostrar {
                get {
                    try {
                        return ((int)(this[this.tableDeterminaciones.Decimales_mostrarColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("El valor de la columna \'Decimales_mostrar\' de la tabla \'Determinaciones\' es DBNul" +
                                "l.", e);
                    }
                }
                set {
                    this[this.tableDeterminaciones.Decimales_mostrarColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool Activo {
                get {
                    try {
                        return ((bool)(this[this.tableDeterminaciones.ActivoColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("El valor de la columna \'Activo\' de la tabla \'Determinaciones\' es DBNull.", e);
                    }
                }
                set {
                    this[this.tableDeterminaciones.ActivoColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public decimal Resultado {
                get {
                    try {
                        return ((decimal)(this[this.tableDeterminaciones.ResultadoColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("El valor de la columna \'Resultado\' de la tabla \'Determinaciones\' es DBNull.", e);
                    }
                }
                set {
                    this[this.tableDeterminaciones.ResultadoColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Signo {
                get {
                    try {
                        return ((string)(this[this.tableDeterminaciones.SignoColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("El valor de la columna \'Signo\' de la tabla \'Determinaciones\' es DBNull.", e);
                    }
                }
                set {
                    this[this.tableDeterminaciones.SignoColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Descripcion_TipoAnalisis {
                get {
                    try {
                        return ((string)(this[this.tableDeterminaciones.Descripcion_TipoAnalisisColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("El valor de la columna \'Descripcion_TipoAnalisis\' de la tabla \'Determinaciones\' e" +
                                "s DBNull.", e);
                    }
                }
                set {
                    this[this.tableDeterminaciones.Descripcion_TipoAnalisisColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Observacion {
                get {
                    try {
                        return ((string)(this[this.tableDeterminaciones.ObservacionColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("El valor de la columna \'Observacion\' de la tabla \'Determinaciones\' es DBNull.", e);
                    }
                }
                set {
                    this[this.tableDeterminaciones.ObservacionColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsNroDeterminacionNull() {
                return this.IsNull(this.tableDeterminaciones.NroDeterminacionColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetNroDeterminacionNull() {
                this[this.tableDeterminaciones.NroDeterminacionColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsDescripcionNull() {
                return this.IsNull(this.tableDeterminaciones.DescripcionColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetDescripcionNull() {
                this[this.tableDeterminaciones.DescripcionColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsUnidad_primariaNull() {
                return this.IsNull(this.tableDeterminaciones.Unidad_primariaColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetUnidad_primariaNull() {
                this[this.tableDeterminaciones.Unidad_primariaColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsUnidad_secundariaNull() {
                return this.IsNull(this.tableDeterminaciones.Unidad_secundariaColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetUnidad_secundariaNull() {
                this[this.tableDeterminaciones.Unidad_secundariaColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsFactor_conversion_PrefijoNull() {
                return this.IsNull(this.tableDeterminaciones.Factor_conversion_PrefijoColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetFactor_conversion_PrefijoNull() {
                this[this.tableDeterminaciones.Factor_conversion_PrefijoColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsFactor_conversion_SufijoNull() {
                return this.IsNull(this.tableDeterminaciones.Factor_conversion_SufijoColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetFactor_conversion_SufijoNull() {
                this[this.tableDeterminaciones.Factor_conversion_SufijoColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsValorReferenciaNull() {
                return this.IsNull(this.tableDeterminaciones.ValorReferenciaColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetValorReferenciaNull() {
                this[this.tableDeterminaciones.ValorReferenciaColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsDecimales_mostrarNull() {
                return this.IsNull(this.tableDeterminaciones.Decimales_mostrarColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetDecimales_mostrarNull() {
                this[this.tableDeterminaciones.Decimales_mostrarColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsActivoNull() {
                return this.IsNull(this.tableDeterminaciones.ActivoColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetActivoNull() {
                this[this.tableDeterminaciones.ActivoColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsResultadoNull() {
                return this.IsNull(this.tableDeterminaciones.ResultadoColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetResultadoNull() {
                this[this.tableDeterminaciones.ResultadoColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsSignoNull() {
                return this.IsNull(this.tableDeterminaciones.SignoColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetSignoNull() {
                this[this.tableDeterminaciones.SignoColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsDescripcion_TipoAnalisisNull() {
                return this.IsNull(this.tableDeterminaciones.Descripcion_TipoAnalisisColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetDescripcion_TipoAnalisisNull() {
                this[this.tableDeterminaciones.Descripcion_TipoAnalisisColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsObservacionNull() {
                return this.IsNull(this.tableDeterminaciones.ObservacionColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetObservacionNull() {
                this[this.tableDeterminaciones.ObservacionColumn] = global::System.Convert.DBNull;
            }
        }
        
        /// <summary>
        ///Row event argument class
        ///</summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        public class ProtocoloRowChangeEvent : global::System.EventArgs {
            
            private ProtocoloRow eventRow;
            
            private global::System.Data.DataRowAction eventAction;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public ProtocoloRowChangeEvent(ProtocoloRow row, global::System.Data.DataRowAction action) {
                this.eventRow = row;
                this.eventAction = action;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public ProtocoloRow Row {
                get {
                    return this.eventRow;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataRowAction Action {
                get {
                    return this.eventAction;
                }
            }
        }
        
        /// <summary>
        ///Row event argument class
        ///</summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        public class DeterminacionesRowChangeEvent : global::System.EventArgs {
            
            private DeterminacionesRow eventRow;
            
            private global::System.Data.DataRowAction eventAction;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public DeterminacionesRowChangeEvent(DeterminacionesRow row, global::System.Data.DataRowAction action) {
                this.eventRow = row;
                this.eventAction = action;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public DeterminacionesRow Row {
                get {
                    return this.eventRow;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataRowAction Action {
                get {
                    return this.eventAction;
                }
            }
        }
    }
}

#pragma warning restore 1591