<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="Comprobantes" table="Comprobantes">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Fecha" type="DateTime">
      <column name="Fecha" not-null="false" sql-type="datetime" />
    </property>
    <property name="Letra" type="String">
      <column name="Letra" not-null="false" length="50" sql-type="nvarchar" />
    </property>
    <property name="Prefijo" type="Int16">
      <column name="Prefijo" not-null="false" precision="5" scale="0" sql-type="smallint" />
    </property>
    <property name="Numero" type="Int32">
      <column name="Numero" not-null="false" precision="10" scale="0" sql-type="int" />
    </property>
    <property name="Importe" type="Decimal">
      <column name="Importe" not-null="false" precision="19" scale="4" sql-type="money" />
    </property>
    <property name="Vencimiento" type="DateTime">
      <column name="Vencimiento" not-null="false" sql-type="datetime" />
    </property>
    <property name="Observaciones" type="String">
      <column name="Observaciones" not-null="false" sql-type="nvarchar" />
    </property>
    <property name="Saldo" type="Decimal">
      <column name="Saldo" not-null="false" precision="19" scale="4" sql-type="money" />
    </property>
    <property name="NroCopia" type="Int16">
      <column name="NroCopia" not-null="false" precision="5" scale="0" sql-type="smallint" />
    </property>
    <property name="NroOperacion" type="Int32">
      <column name="NroOperacion" not-null="false" precision="10" scale="0" sql-type="int" />
    </property>
    <property name="Anulado" type="Int16">
      <column name="Anulado" not-null="true" precision="5" scale="0" sql-type="smallint" />
    </property>
    <property name="InteresMensualAplicableRecargos" type="Single">
      <column name="InteresMensualAplicableRecargos" not-null="false" precision="24" sql-type="real" />
    </property>
    <property name="Eliminado" type="Int32">
      <column name="Eliminado" not-null="true" precision="10" scale="0" sql-type="int" />
    </property>
    <property name="Orden" type="Int32">
      <column name="Orden" not-null="false" precision="10" scale="0" sql-type="int" />
    </property>
    <property name="ValorMoneda" type="Decimal">
      <column name="ValorMoneda" not-null="true" precision="19" scale="4" sql-type="money" />
    </property>
    <property name="Contado" type="Int16">
      <column name="Contado" not-null="true" precision="5" scale="0" sql-type="smallint" />
    </property>
    <property name="Copias" type="Int32">
      <column name="Copias" not-null="false" precision="10" scale="0" sql-type="int" />
    </property>
    <property name="Cae" type="String">
      <column name="Cae" not-null="false" length="50" sql-type="nvarchar" />
    </property>
    <property name="VencimientoCae" type="String">
      <column name="VencimientoCae" not-null="false" length="50" sql-type="nvarchar" />
    </property>
    <property name="IdContrato" type="Int32">
      <column name="IdContrato" not-null="false" precision="10" scale="0" sql-type="int" />
    </property>
    <many-to-one name="ComprobanteTipos" class="ComprobanteTipos">
      <column name="IdComprobanteTipo" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <many-to-one name="BancosTarjetas" class="BancosTarjetas">
      <column name="IdBancoTarjeta" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <many-to-one name="Monedas" class="Monedas">
      <column name="IdMoneda" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <set name="Cheques" inverse="true" generic="true">
      <key>
        <column name="IdComprobante" />
      </key>
      <one-to-many class="Cheques" />
    </set>
    <one-to-one name="ComprobantesCabeceras" class="ComprobantesCabeceras" />
    <set name="ComprobantesRelacionados" inverse="true" generic="true">
      <key>
        <column name="IdComprobantePrincipal" />
      </key>
      <one-to-many class="ComprobantesRelacionados" />
    </set>
    <set name="ComprobantesRetenciones" inverse="true" generic="true">
      <key>
        <column name="IdComprobante" />
      </key>
      <one-to-many class="ComprobantesRetenciones" />
    </set>
    <set name="CuponesTarjetas" inverse="true" generic="true">
      <key>
        <column name="IdComprobante" />
      </key>
      <one-to-many class="CuponesTarjetas" />
    </set>
    <set name="Efectivos" inverse="true" generic="true">
      <key>
        <column name="IdComprobante" />
      </key>
      <one-to-many class="Efectivo" />
    </set>
    <set name="Recibos" inverse="true" generic="true">
      <key>
        <column name="IdComprobante" />
      </key>
      <one-to-many class="Recibos" />
    </set>
    <set name="Transferencias" inverse="true" generic="true">
      <key>
        <column name="IdComprobante" />
      </key>
      <one-to-many class="Transferencias" />
    </set>
    <set name="Contratos" table="ComprobantesContratos" inverse="true" generic="true">
      <key>
        <column name="IdComprobante" />
      </key>
      <many-to-many class="Contratos" fetch="join">
        <column name="IdContrato" />
      </many-to-many>
    </set>
  </class>
</hibernate-mapping>