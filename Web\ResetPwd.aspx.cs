﻿using Business.Services.Usuarios;
using Common.Encrypt;
using Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web
{
    public partial class ResetPwd : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {

        }

        protected void btnRestablecer_Click(object sender, EventArgs e)
        {
            //Verifico la existencia del mail ingresado
            String correo = txtEmail.Text;

            try
            {
                Usuario u = UsuarioService.ExisteEmail(correo);
                if (u != null)
                {
                   

                    String persona = u.Nombre + " " + u.Apellido;
                    String urlRestablecer = Request.Url.GetLeftPart(UriPartial.Authority) + Global.ApplicationPath + "/CheckPwd.aspx?id=";
                    String encrypt = persona + u.Email + DateTime.Now;
                    SimpleAES sp = new SimpleAES();
                    String encryptData = sp.EncryptToString(encrypt);

                    //Guardo la encriptación para recuperación de contraseña
                    u.Confirmacion = encryptData;
                    UsuarioService.SaveOrUpdate(u);

                    String mailUser = Properties.Settings.SMTP_user;
                    String mailPassword = Properties.Settings.SMTP_pwd;
                    String from = Properties.Settings.SMTP_from;
                    String smtpServer = Properties.Settings.SMTP_server;
                    String smtpPort = Properties.Settings.SMTP_port  ;
                    String body = "Hola " + persona +
                                  "<br><br>Se hizo una solicitud de cambio de contraseña recientemente en tu cuenta.<br>" +
                                  "Si solicitaste este cambio de contraseña, por favor, ingresa una contraseña nueva usando el enlace a continuación:<br><br>" +
                                  "<a href='" + urlRestablecer + encryptData + "'>Cambiar Contraseña</a>" +
                                  "<br><br>Si no deseas cambiar tu contraseña, ignora este mensaje.<br><br>" +
                                  "Gracias.<br>" + Properties.Settings.NombreApp;

                    MailMessage mail = new MailMessage()
                    {
                        From = new MailAddress(from, Properties.Settings.NombreApp),
                        Body = body,
                        Subject = "Recuperación de contraseña de " + Properties.Settings.NombreApp,
                        IsBodyHtml = true,
                    };

                    mail.To.Add(new MailAddress(correo, persona));
                    SmtpClient smtp = new SmtpClient();
                    smtp.Host = smtpServer;
                    smtp.Port = Convert.ToInt32(smtpPort);
                    smtp.Credentials = new NetworkCredential(mailUser, mailPassword);
                    smtp.Send(mail);

                    lblMsg.Visible = true;
                    lblMsg.Text = "Email enviado correctamente";
                    txtEmail.Text = "";
                    lblError.Visible = false;
                }
                else
                {
                    lblMsg.Visible = false;
                    lblError.Visible = true;
                    lblError.Text = "No se encontro una cuenta asociada a ese email.";
                    txtEmail.Text = "";
                }
            }
            catch (Exception ex)
            {
                lblMsg.Visible = false;
                lblError.Visible = true;
                lblError.Text = "Error enviando email. Consulte al administrador del sistema.";
        
            }
        }
    }
}