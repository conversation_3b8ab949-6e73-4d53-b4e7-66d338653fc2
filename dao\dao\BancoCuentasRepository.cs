﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 22/5/2023 14:55:38
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class BancoCuentasRepository : NHibernateRepository<Domain.BancoCuentas>, IBancoCuentasRepository
    {
        public BancoCuentasRepository(ISession session) : base(session)
        {
        }

        public virtual ICollection<Domain.BancoCuentas> GetAll()
        {
            return session.CreateQuery(string.Format("from BancoCuentas")).List<Domain.BancoCuentas>();
        }

        public virtual Domain.BancoCuentas GetByKey(int _Id)
        {
            return session.Get<Domain.BancoCuentas>(_Id);
        }
    }
}
