﻿using Business.Services.Provincias;
using Business.Services.Epas;
using DevExpress.Web;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.Adapters;
using DevExpress.Utils.Extensions;
using Domain;
using Business.Provincias;
using System.Security.Cryptography;
using DevExpress.XtraPrinting;

namespace Web.EPAS.AddEdit
{
    public partial class AddEditFuentes : System.Web.UI.Page
    {
        protected void Page_Init(Object sender,EventArgs e)
        {
            if(!IsPostBack)
            {
                Session["subFuentes"] = new List<Domain.SubFuentes>();
            }
        }

        protected void Page_Load(object sender,EventArgs e)
        {
            try
            {
                btnVolverEditado.Visible = false;
                if (!IsPostBack)
                {
                    LlenadoCmb_Localidades();
                    LlenadoCmb_TipodeFuente();

                    if (Request.QueryString["id"] != null)
                    {

                        int idfuente = Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["id"].ToString()));
                        TxtID.Text = idfuente.ToString();
                        btnVolverEditado.Visible = false;
                        CargarFuentes(idfuente);

                    }
                    else
                    {
                        TxtID.Text = "0";
                        TxtID.Visible = false;
                        CB_Activo.Checked = true;
                        btnVolverEditado.Visible = false;
                    }
                  
                }
                gv_datos.DataBind();
            }

            catch
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1",
                    "showMessage('Error al cargar la página, consulte al administrador del sistema', 'error');", true);
            }
        }

        private void CargarFuentes(int idfuente)
        {

            try
            {
                Domain.Fuentes fuentes = FuentesService.GetById(idfuente);
               
                if (fuentes != null)
                {
                  txtNumerodefuente.Text = fuentes.NroDeFuente.ToString();
                  txtDescripcion.Text = fuentes.Descripcion.ToString();

                  CB_Activo.Checked = Convert.ToBoolean(fuentes.Activo);
                    if (txtNota.Text != null)
                    {
                        txtNota.Text = fuentes.Notas.ToString();
                    }
                  CmbLocalidades.Items.FindByValue(fuentes.Localidades.Id.ToString()).Selected = true;
                    CmbTipodeFuente.Items.FindByValue(fuentes.FuentesTipos.Id.ToString()).Selected = true;

                    Session["subFuentes"] = fuentes.SubFuentes.ToList<Domain.SubFuentes>();

                    txtLat.Text = fuentes.Latitud.ToString();
                    txtLng.Text = fuentes.Longitud.ToString();
                }

            }
            catch (Exception ex)
            {
            }

        }
        protected void LlenadoCmb_Localidades()
        {
            CmbLocalidades.DataSource = LocalidadesService.GetAll();
            CmbLocalidades.TextField = "Nombre";
            CmbLocalidades.ValueField = "Id";
            CmbLocalidades.DataBind();
            CmbLocalidades.Items.Insert(0, new ListEditItem());

        }
        protected void LlenadoCmb_TipodeFuente()
        {
            CmbTipodeFuente.DataSource = FuentesTiposService.GetAll();
            CmbTipodeFuente.TextField = "Descripcion";
            CmbTipodeFuente.ValueField = "Id";
            CmbTipodeFuente.DataBind();
            CmbTipodeFuente.Items.Insert(0, new ListEditItem());

        }
        protected void gv_datos_DataBinding(object sender, EventArgs e)
        {
            try
            {
                gv_datos.DataSource = GetDatos();
            }
            catch (Exception ex)
            {

            }
        }

        IList<SubFuentes> GetDatos()
        {

            return (IList<Domain.SubFuentes>) Session["subFuentes"];
        }

        protected void btnAnularActivar_Init(object sender, EventArgs e)
        {
            try
            {
                ASPxButton btn = (ASPxButton)sender;
                GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer)btn.NamingContainer;
                var ind = DataBinder.Eval(c.DataItem, "Id");

                //mando el id como parametro en el callback
                btn.ClientSideEvents.Click = String.Format("function(s,e){{ callback_quitar.PerformCallback('{0}');}}", ind);

            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1ddse4", "showMessage('Error al cargar la página, consulte al administrador del sistema', 'error');", true);
            }
        }

        protected void callback_agregar_Callback(object source, CallbackEventArgs e)
        {            
            try
            {
                if (!string.IsNullOrEmpty(txtNumerodefuente.Text) && !string.IsNullOrEmpty(txtDescripcion.Text))
                {

                    Domain.Fuentes fuente = null;

                    if (Request.QueryString["id"] != null)
                    {
                        fuente = FuentesService.GetById(Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["id"])));

                    } else
                    {
                        fuente = new Domain.Fuentes();
                    }

                    if (fuente != null)
                    {

                        if (SubFuentesService.ExisteSubFuentes(txtSubCodigo.Text.Trim().ToUpper(), txtSubDescripcion.Text.Trim().ToUpper(),Convert.ToInt32(TxtID.Text)))
                        {
                            ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('Ya existe una SubFuentes con ese código y descripción!', 'error');",true);
                            lblMsg.Visible = true;
                            lblMsg.Text = "Ya existe una subfuentes con ese código y descripción";
                            lblMsg.Visible = true;
                        }
                        else
                        {
                            //verifico si existe en la lista
                            List<SubFuentes> lst;
                            if (Session["subFuentes"] != null)
                            {
                                lst = (List<SubFuentes>)Session["subFuentes"];

                            }
                            else
                            {
                                lst = new List<SubFuentes>();

                            }

                            if (lst.Exists(pre => pre.Codigo.ToUpper().Trim() == txtSubCodigo.Text.Trim().ToUpper() || pre.Descripcion.Trim().ToUpper() == txtSubDescripcion.Text.Trim().ToUpper()))
                            {
                            lblMsg.Text = "Ya existe una subfuentes con ese código y descripcion";
                            lblMsg.Visible = true;

                            }
                            else
                            {
                                SubFuentes sf = new SubFuentes();
                                Random rnd = new Random();
                                sf.Id = Convert.ToInt32(rnd.Next(2000) * -1);
                                sf.Codigo = txtSubCodigo.Text.Trim().ToUpper();
                                sf.Descripcion = txtSubDescripcion.Text.Trim().ToUpper();
                                if(!string.IsNullOrEmpty(txtSubCalle.Text))
                                {
                                    sf.Calle = txtSubCalle.Text.Trim().ToUpper();
                                }

                                if(!string.IsNullOrEmpty(txtSubAltura.Text))
                                {
                                    sf.Altura = Convert.ToInt32(txtSubAltura.Text);
                                }

                                if(!string.IsNullOrEmpty(txtSubObservacion.Text))
                                {
                                    sf.Observacion = txtSubObservacion.Text.Trim().ToUpper();
                                }

                                sf.Lantitud = txtLatSF.Text;
                                sf.Longitud = txtLngSF.Text;

                                sf.Fuentes = fuente;                                    

                                lst.Add(sf);
                                Session["subFuentes"] = lst;

                                ScriptManager.RegisterStartupScript(this, this.GetType(), "ksdfsd4eddsedy1", "showMessage('Agregado correctamente', 'success');", true);
                            }
                        }
                    }
                    else
                    {
                        ScriptManager.RegisterStartupScript(this, this.GetType(), "ksdfsd4eddsedy1", "showMessage('Seleccione una fuente!', 'error');", true);

                    }
                }
                else
                {
                    ScriptManager.RegisterStartupScript(this, this.GetType(), "ksdfsd4eddsedy1", "showMessage('Busque una fuente!', 'error');", true);

                }

            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "ksdfsd4eddsedy1", "showMessage('Error al agregar fuente, intente de nuevo', 'error');", true);

            }
            
        }

        protected void callback_quitar_guarda_Callback(object source, CallbackEventArgs e)
        {
            try
            {
                if(hf_idRef_quitar.Get("idSubFuente") != null)
                {
                    int idCat = Convert.ToInt32(hf_idRef_quitar.Get("idSubFuente"));

                    if(idCat > 0)
                    {
                        SubFuentes cat = SubFuentesService.GetById(idCat);
                        if(cat != null)
                        {
                            // El objeto ya está en la base de datos
                            cat.Activa = !cat.Activa;

                            SubFuentesService.SaveOrUpdate(cat);

                            // Actualiza la lista en sesión para reflejar el cambio en la grilla
                            List<SubFuentes> lst;
                            if(Session["subFuentes"] != null)
                            {
                                lst = (List<SubFuentes>) Session["subFuentes"];
                                var subCatToUpdate = lst.FirstOrDefault(p => p.Id == idCat);
                                if(subCatToUpdate != null)
                                {
                                    subCatToUpdate.Activa = !subCatToUpdate.Activa;
                                    Session["subFuentes"] = lst;
                                }
                            }
                        }

                    } else
                    {
                        // Objeto nuevo, aún no guardado en la base de datos
                        List<SubFuentes> lst;
                        if(Session["subFuentes"] != null)
                        {
                            lst = (List<SubFuentes>) Session["subFuentes"];
                            lst.RemoveAll(p => p.Id == idCat);
                            Session["subFuentes"] = lst;
                        }
                    }
                }

            } catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "ksdfsd4eddsedy1", "showMessage('Error al cargar la página, consulte al administrador del sistema', 'error');", true);
            }
        }

        protected void submitButton_Click(object sender,EventArgs e) {

        }

        protected void btnGrabar_Click(object sender,EventArgs e) {

            Fuentes F = null;
            SubFuentes SF =null;

            try {
                //Fuentes F;

                //Verifica que se haya ingresado un codigo y nombre
                if(!string.IsNullOrEmpty(txtNumerodefuente.Text) && !string.IsNullOrEmpty(txtDescripcion.Text)) {

                    //Verifico si el id que recibo está en base de datos (edición)
                    if(Request.QueryString["id"] != null) {

                        // Se está editando un departamento existente.
                        int fuenteId = Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["id"]));

                        //Obtengo el objeto departamento                        
                        F = FuentesService.GetById(fuenteId);

                        //Verifico si el objeto que obtengo no es nulo.
                        if(F != null) {

                            //El código si es edición, no se puede modificar, sólo actualizo descripción y activo y provincia
                            if(F.NroDeFuente.ToString().Equals(txtNumerodefuente.Text.Trim().ToUpper())) {
                                F.Descripcion = txtDescripcion.Text.Trim().ToUpper();
                                F.Notas = Convert.ToString(txtNota.Text);
                                F.Latitud = Convert.ToString(txtLat.Text);
                                F.Longitud = Convert.ToString(txtLng.Text);
                                F.Activo = CB_Activo.Checked;

                                if(CmbLocalidades.SelectedItem != null) {
                                    Domain.Localidades z = LocalidadesService.GetById(id: Convert.ToInt32(CmbLocalidades.SelectedItem.Value));
                                    F.Localidades = z;

                                }

                                if(CmbTipodeFuente.SelectedItem != null) {
                                    Domain.FuentesTipos sub = FuentesTiposService.GetById(id: Convert.ToInt32(CmbTipodeFuente.SelectedItem.Value));
                                    F.FuentesTipos = sub;

                                }

                                //Actualizo la base de datos con los cambios realizados.
                                FuentesService.SaveOrUpdate(F);                                

                                //Verifico si la sesión existente de subFuentes es nula.
                                if(Session["subFuentes"] != null)
                                {
                                    //Asigno a una variable local de la lista de subFuentes, la sesión de subFuentes
                                    List<Domain.SubFuentes> subFuente = (List<Domain.SubFuentes>) Session["subFuentes"];

                                    //Verifico que la variable local de la lista de subFuentes no sea nula.
                                    if(subFuente != null)
                                    {
                                        //Asigno a la lista de subFuentes de la sesión una nueva subFuente en el caso de haberla cargado.
                                        foreach(Domain.SubFuentes sf in subFuente)
                                        {
                                            if(sf.Id < 0)
                                            {
                                                sf.Id = 0;
                                            }
                                            sf.Fuentes = F;

                                            //Actualizo la base de datos con los cambios realizados.
                                            SubFuentesService.SaveOrUpdate(sf);

                                        }

                                    }

                                }

                                ScriptManager.RegisterStartupScript(this,this.GetType(),"message3","showMessage('Fue editado correctamente.', 'success');",true);
                                txtNumerodefuente.ClientEnabled = false;
                                btnGrabar.ClientEnabled = false;
                                btnVolver.Visible = false;
                                btnVolverEditado.Visible = true;
                                limpiezadecamposSubfuentes();
                            }
                        }

                        //  ScriptManager.RegisterStartupScript(this, this.GetType(), "message1", "showMessage('El departamento ya existe en la base de datos.', 'error');", true);                                             
                    } else {
                        // Se está creando un nuevo departamento.
                        // Verificar si ya existe en la lista local, solo se verifica el código
                        if(FuentesService.ExisteCodigo(txtNumerodefuente.Text.Trim()) || FuentesService.ExisteNombre(txtDescripcion.Text.Trim())) {
                            ScriptManager.RegisterStartupScript(this,this.GetType(),"message2","showMessage('Ya existe una fuente con ese código o nombre, no se puede guardar.', 'error');",true);
                        } else {

                            //No existe el código, puedo guardar en la base
                            F = new Domain.Fuentes();

                            F.NroDeFuente = txtNumerodefuente.Text.Trim().ToUpper();
                            F.Descripcion = txtDescripcion.Text;
                            F.Notas = Convert.ToString(txtNota.Text);
                            F.Latitud = Convert.ToString(txtLat.Text);
                            F.Longitud = Convert.ToString(txtLng.Text);
                            F.Activo = CB_Activo.Checked;

                            if(CmbLocalidades.SelectedItem != null) {
                                Domain.Localidades loc = LocalidadesService.GetById(id: Convert.ToInt32(CmbLocalidades.SelectedItem.Value));
                                F.Localidades = loc;
                            }

                            if(CmbTipodeFuente.SelectedItem != null) {
                                Domain.FuentesTipos subTipo = FuentesTiposService.GetById(id: Convert.ToInt32(CmbTipodeFuente.SelectedItem.Value));
                                F.FuentesTipos = subTipo;
                            }

                            //Actualizo la base de datos de Fuentes.
                            FuentesService.SaveOrUpdate(F);

                            //Si la sesión de subFuentes no es nula, la actualiza.
                            if(Session["subFuentes"] != null)
                            {
                                //Asigno la sesión a una variable local
                                List<Domain.SubFuentes> subFuente = (List<Domain.SubFuentes>) Session["subFuentes"];

                                foreach(Domain.SubFuentes sf in subFuente)
                                {
                                    if(sf.Id < 0)
                                    {
                                        sf.Id = 0;
                                    }

                                    sf.Fuentes = F;

                                    //Actualizo la base de datos de SubFuentes.
                                    SubFuentesService.SaveOrUpdate(sf);
                                }
                            }

                            ScriptManager.RegisterStartupScript(this,this.GetType(),"message3","showMessage('Agregado correctamente.', 'success');",true);
                            txtNumerodefuente.ClientEnabled = false;
                            btnGrabar.ClientEnabled = false;
                            btnVolver.Visible = false;
                            btnVolverEditado.Visible = true;

                        }
                    }

                } else {
                    ScriptManager.RegisterStartupScript(this,this.GetType(),"message4","showMessage('Busque una Fuente.', 'error');",true);
                }
            } catch(Exception ex) {
                ScriptManager.RegisterStartupScript(this,this.GetType(),"message5","showMessage('Error al agregar una Fuente, intente de nuevo.', 'error');",true);
            }
        }

        protected void limpiezadecamposSubfuentes()
        {
            // Limpiar TextBox
            txtSubCodigo.Text = "";
            txtSubDescripcion.Text = "";
            txtSubCalle.Text = "";
            txtSubAltura.Text = "";
            txtSubObservacion.Text = "";
        }
    }
}
