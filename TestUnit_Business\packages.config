﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Antlr3.Runtime" version="3.5.1" targetFramework="net481" />
  <package id="Castle.Core" version="5.1.1" targetFramework="net481" />
  <package id="Iesi.Collections" version="4.0.4" targetFramework="net481" />
  <package id="Moq" version="4.20.70" targetFramework="net481" />
  <package id="NHibernate" version="5.3.3" targetFramework="net481" />
  <package id="Remotion.Linq" version="2.2.0" targetFramework="net481" />
  <package id="Remotion.Linq.EagerFetching" version="2.2.0" targetFramework="net481" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.5.3" targetFramework="net481" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net481" />
  <package id="xunit.abstractions" version="2.0.3" targetFramework="net481" />
  <package id="xunit.assert" version="2.6.2" targetFramework="net481" />
  <package id="xunit.extensibility.core" version="2.6.2" targetFramework="net481" />
</packages>