using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;
using DAO;
using Domain;
using NHibernate.Criterion;
using NHibernate;
using System.Data;
using Common.DDL;


namespace Business.Services.Usuarios
{


    public class UsuarioService
    {



        public static Usuario ExisteUsername(string username)
        {
            Usuario lista;

            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                lista = new UsuarioRepository(sess).GetUsername(username);

                sess.Close();
                sess.Dispose();
                return lista;
            }
        }

        public static Usuario ExisteEmail(string email)
        {
            Usuario lista;

            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                lista = new UsuarioRepository(sess).GetEmail(email);

                sess.Close();
                sess.Dispose();
                return lista;
            }
        }


        public static int agregar_User(int idUsuario, Usuario u)
        {
            int value_Return = 0;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                switch (existe_Username(idUsuario,u))
                {
                    case 0:
                        value_Return = 1;
                        break;
                                               
                    case 1:
                       
                        UsuarioService.SaveOrUpdate(u);
                        break;
                }
                sess.Close();
                sess.Dispose();
                return value_Return;
            }

        }

        
        public static int existe_Username(int idUsuario, Usuario u)
        {
            Usuario user;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                user = new UsuarioRepository(sess).existe_UserName(idUsuario,u);  
                sess.Close();
                sess.Dispose();
                if (user != null)
                {
                    return 1;//Username existente
                }
                else
                {
                    return 0;
                }
            }
        }

        public static IList<Usuario> GetAll()
        {

            IList<Usuario> lista;
            IList<Usuario> listaSalida = new List<Usuario>();
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {

                lista = (IList<Usuario>)new UsuarioRepository(sess).GetAll();


                foreach (Usuario us in lista)
                {
                    if (us.Eliminado == false)
                    {
                        NHibernateUtil.Initialize(us.Rols);

                        listaSalida.Add(us);
                    }
                }

                var list = (from row in listaSalida
                            orderby row.Id descending
                            select row).ToList<Usuario>();

                listaSalida = list;
                sess.Close();
                sess.Dispose();
                return listaSalida;
            }

        }

        public static List<ItemCombo> getAllDDL()
        {
            IList<Usuario> listaAux;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                listaAux = (IList<Usuario>)new UsuarioRepository(sess).GetAll();
                sess.Close();
                sess.Dispose();
            }

            ItemCombo item;
            ListaCombo lista = new ListaCombo();
            foreach (Usuario p in listaAux)
            {

                item = new ItemCombo();
                item.ID = p.Id.ToString();
                item.Descripcion = p.Username + "(" + p.Apellido + " " + p.Nombre + ")";
                lista.Add(item);
            }
            return (lista);
        }







        /// <summary>
        /// Solo trae relacion con roles
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static Usuario getByIdNoInitialize(int id)
        {
            Usuario u;

            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                u = new UsuarioRepository(sess).GetByKey(id);
                if (u != null)
                {
                    NHibernateUtil.Initialize(u.Rols);
                }
                sess.Close();
                sess.Dispose();
                return u;
            }

        }

        public static Usuario GetById(int id)
        {
            Usuario u;

            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                u = new UsuarioRepository(sess).GetByKey(id);
                if (u != null)
                {
                    foreach(Rol r in u.Rols) {
                        foreach(RolModulo rm in r.RolModulos) {

                            foreach(ModuloControl mc in rm.ModuloControls) {
                                NHibernateUtil.Initialize(mc.RolModulo);
                            }
                        }

                    }



                }
                sess.Close();
                sess.Dispose();
                return u;
            }

        }




        public static Usuario Login(string username, string password)
        {
            Usuario u;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                ICriteria criteria = sess.CreateCriteria<Usuario>();
                criteria.Add(Restrictions.Eq("Username", username));
                criteria.Add(Restrictions.Eq("Password", password));

                u = criteria.UniqueResult<Usuario>();
                sess.Close();
                sess.Dispose();
                if (u != null)
                {
                    //NHibernateUtil.Initialize(u.Rols);
                    if (u.IngresoActual != null)
                    {
                        u.UltimoIngreso = u.IngresoActual;
                    }
                    u.IngresoActual = DateTime.Now;
                    //object value = SaveOrUpdate(u);




                }

                return u;

            }

        }

        public static Usuario ConfirmarPwd(string query)
        {
            Usuario u;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                ICriteria criteria = sess.CreateCriteria<Usuario>();
                criteria.Add(Restrictions.Eq("Eliminado", false));
                criteria.Add(Restrictions.Like("Confirmacion", query, MatchMode.Exact).IgnoreCase());
                u = criteria.UniqueResult<Usuario>();

                if (u != null)
                {
                    NHibernateUtil.Initialize(u.Rols);

                }
                sess.Close();
                sess.Dispose();
                return u;
            }
        }

        public static void SaveOrUpdate(Usuario u)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new UsuarioRepository(sess).Add(u);

                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }

            }
        }

        public static void Delete(Usuario u)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new UsuarioRepository(sess).Remove(u);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }

            }
        }



    }
}
