﻿using System;
using System.Collections.Generic;
using System.Linq;


namespace Domain.DTO {

    public static partial class ContratoContactosConverter {

        public static ContratoContactosDTO ToDto(this ContratoContactos source) {
            return source.ToDtoWithRelated(0);
        }

        public static ContratoContactosDTO ToDtoWithRelated(this ContratoContactos source,int level) {
            if(source == null)
                return null;

            var target = new ContratoContactosDTO();

            // Properties
            target.Id = source.Id;
            target.Nombre = source.Nombre;
            target.Apellido = source.Apellido;
            target.DocumentosTipo = source.DocTipos.Abreviatura;
            target.IdDocumentosTipo = source.DocTipos.Id;
            target.NumeroDoc = source.NumeroDoc;
            target.FechaNac = source.FechaNac;
            target.Activo = source.Activo;
            target.Email = source.ContratoContactosEmails.First().Email;
            target.IdTelefonoTipo = source.ContratoContactosTels.First().TelefonoTipo.Id;
            target.Tel = source.ContratoContactosTels.First().NroTelefono;
            target.TelefonoTipoDescripcion = source.ContratoContactosTels.First().TelefonoTipo.Tipo;
            target.SexoDescripcion = source.Sexos.Descripcion;
            target.IdSexo = source.Sexos.Id;

            // Navigation Properties
            //if(level > 0) {
            //    target.Sexos = source.Sexos.ToDtoWithRelated(level - 1);
            //}

            // User-defined partial method
            OnDtoCreating(source,target);

            return target;
        }


        public static ContratoContactos ToEntity(this ContratoContactosDTO source) {
            if(source == null)
                return null;

            var target = new ContratoContactos();

            // Properties
            target.Id = source.Id;
            target.Nombre = source.Nombre;
            target.Apellido = source.Apellido;
            target.NumeroDoc = source.NumeroDoc;
            target.FechaNac = source.FechaNac;
            target.Activo = source.Activo;
            // User-defined partial method
            OnEntityCreating(source,target);

            return target;
        }

        public static List<ContratoContactosDTO> ToDtos(this IEnumerable<ContratoContactos> source) {
            if(source == null)
                return null;

            var target = source
              .Select(src => src.ToDto())
              .ToList();

            return target;
        }

        public static List<ContratoContactosDTO> ToDtosWithRelated(this IEnumerable<ContratoContactos> source,int level) {
            if(source == null)
                return null;

            var target = source
              .Select(src => src.ToDtoWithRelated(level))
              .ToList();

            return target;
        }

        public static List<ContratoContactos> ToEntities(this IEnumerable<ContratoContactosDTO> source) {
            if(source == null)
                return null;

            var target = source
              .Select(src => src.ToEntity())
              .ToList();

            return target;
        }

        static partial void OnDtoCreating(ContratoContactos source,ContratoContactosDTO target);

        static partial void OnEntityCreating(ContratoContactosDTO source,ContratoContactos target);

    }

}
