﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{F02FE28F-4AE8-40A4-B318-3EB39DFF7156}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Business</RootNamespace>
    <AssemblyName>Business</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <PlatformTarget>x86</PlatformTarget>
    <OutputPath>bin\x86\Debug\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <PlatformTarget>x86</PlatformTarget>
    <OutputPath>bin\x86\Release\</OutputPath>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Antlr3.Runtime, Version=*******, Culture=neutral, PublicKeyToken=eb42632606e9261f, processorArchitecture=MSIL">
      <HintPath>..\packages\Antlr3.Runtime.3.5.2-rc1\lib\net40-client\Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib, Version=********, Culture=neutral, PublicKeyToken=1b03e6acf1164f73, processorArchitecture=MSIL">
      <HintPath>..\packages\SharpZipLib.1.3.3\lib\net45\ICSharpCode.SharpZipLib.dll</HintPath>
    </Reference>
    <Reference Include="Iesi.Collections, Version=4.0.0.4000, Culture=neutral, PublicKeyToken=aa95f207798dfdb4, processorArchitecture=MSIL">
      <HintPath>..\packages\Iesi.Collections.4.0.4\lib\net461\Iesi.Collections.dll</HintPath>
    </Reference>
    <Reference Include="NHibernate, Version=*******, Culture=neutral, PublicKeyToken=aa95f207798dfdb4, processorArchitecture=MSIL">
      <HintPath>..\packages\NHibernate.5.3.3\lib\net461\NHibernate.dll</HintPath>
    </Reference>
    <Reference Include="NPOI, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.4.1\lib\net45\NPOI.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OOXML, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.4.1\lib\net45\NPOI.OOXML.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXml4Net, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.4.1\lib\net45\NPOI.OpenXml4Net.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXmlFormats, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.4.1\lib\net45\NPOI.OpenXmlFormats.dll</HintPath>
    </Reference>
    <Reference Include="Remotion.Linq, Version=2.2.0.0, Culture=neutral, PublicKeyToken=fee00910d6e5f53b, processorArchitecture=MSIL">
      <HintPath>..\packages\Remotion.Linq.2.2.0\lib\net45\Remotion.Linq.dll</HintPath>
    </Reference>
    <Reference Include="Remotion.Linq.EagerFetching, Version=2.2.0.0, Culture=neutral, PublicKeyToken=fee00910d6e5f53b, processorArchitecture=MSIL">
      <HintPath>..\packages\Remotion.Linq.EagerFetching.2.2.0\lib\net45\Remotion.Linq.EagerFetching.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.1.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.3.0\lib\portable-net45+win8+wp8+wpa81\System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Services\Caja\BancosTarjetasService.cs" />
    <Compile Include="Services\Caja\CajaServices.cs" />
    <Compile Include="Services\Caja\ChequeServices.cs" />
    <Compile Include="Services\Caja\ComprobanteServices.cs" />
    <Compile Include="Services\Caja\ComprobantesRetencionesServices.cs" />
    <Compile Include="Services\Caja\ComprobanteTiposServices.cs" />
    <Compile Include="Services\Caja\CuponesTarjetasServices.cs" />
    <Compile Include="Services\Caja\EfectivoServices.cs" />
    <Compile Include="Services\Caja\MonedaServices.cs" />
    <Compile Include="Services\Caja\Pagoservice.cs" />
    <Compile Include="Services\Caja\RetencionesServices.cs" />
    <Compile Include="Services\Caja\TarjetaService.cs" />
    <Compile Include="Services\Caja\TrasferenciaService.cs" />
    <Compile Include="Services\Contratos\ContratoContactosEmailServices.cs" />
    <Compile Include="Services\Contratos\ContratoContactosTelServices.cs" />
    <Compile Include="Services\Contratos\ContratoDomiciliosServices.cs" />
    <Compile Include="Services\Contratos\DomiciliosTiposServices.cs" />
    <Compile Include="Services\Contratos\NumeracionServices.cs" />
    <Compile Include="Services\Contratos\ContratoContactosServices.cs" />
    <Compile Include="Services\Contratos\ContratoTipoServices.cs" />
    <Compile Include="Services\Contratos\TelefonoTipoServices.cs" />
    <Compile Include="Services\Contratos\IVACondicionesServices.cs" />
    <Compile Include="Services\Contratos\SexosServices.cs" />
    <Compile Include="Services\Contratos\DocTiposServices.cs" />
    <Compile Include="Services\Contratos\ContratosServices.cs" />
    <Compile Include="Services\Contratos\PersonasServices.cs" />
    <Compile Include="Services\Epas\ProtocoloDeterminacionesService.cs" />
    <Compile Include="Services\Epas\ProtocoloEstadoService.cs" />
    <Compile Include="Services\Epas\AnalisisTipoService.cs" />
    <Compile Include="Services\Epas\DeterminacionesService.cs" />
    <Compile Include="Services\Epas\FuentesService.cs" />
    <Compile Include="Services\Epas\MetodosdeMedicionService.cs" />
    <Compile Include="Services\Epas\PlantillaProtocolosService.cs" />
    <Compile Include="Services\Epas\FuentesTiposService.cs" />
    <Compile Include="Services\Epas\ProtocolosService.cs" />
    <Compile Include="Services\Epas\SubFuentesService.cs" />
    <Compile Include="Services\Provincias\CallesService.cs" />
    <Compile Include="Services\Provincias\DepartamentosService.cs" />
    <Compile Include="Services\Provincias\LocalidadesService.cs" />
    <Compile Include="Services\Provincias\ProvinciasService.cs" />
    <Compile Include="Services\Provincias\ZonasService.cs" />
    <Compile Include="Services\Usuarios\ModuloControlService.cs" />
    <Compile Include="Services\Usuarios\ModuloService.cs" />
    <Compile Include="Services\Usuarios\RolModuloService.cs" />
    <Compile Include="Services\Usuarios\RolService.cs" />
    <Compile Include="Services\Usuarios\TipoPermisoService.cs" />
    <Compile Include="Services\Usuarios\UsuarioService.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Common\Common.csproj">
      <Project>{B240BC08-FBE1-46B6-93E1-F1146A829C4C}</Project>
      <Name>Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\dao\DAO.csproj">
      <Project>{ee87fd63-87bc-4406-884f-185cd3597792}</Project>
      <Name>DAO</Name>
    </ProjectReference>
    <ProjectReference Include="..\domain\Domain.csproj">
      <Project>{a1e15de3-73e9-42a5-8e4c-41753f59dbe4}</Project>
      <Name>Domain</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="packages.config">
      <SubType>Designer</SubType>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Globals\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>