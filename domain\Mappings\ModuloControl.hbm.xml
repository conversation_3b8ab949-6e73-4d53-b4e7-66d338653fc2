﻿<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="ModuloControl" table="ModuloControl">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Visible" type="Boolean">
      <column name="visible" not-null="false" sql-type="bit" />
    </property>
    <property name="Lectura" type="Boolean">
      <column name="lectura" not-null="false" sql-type="bit" />
    </property>
    <property name="Escritura" type="Boolean">
      <column name="escritura" not-null="false" sql-type="bit" />
    </property>
    <property name="Eliminar" type="Boolean">
      <column name="eliminar" not-null="false" sql-type="bit" />
    </property>
    <many-to-one name="RolModulo" class="RolModulo">
      <column name="IdRolModulo" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
  </class>
</hibernate-mapping>