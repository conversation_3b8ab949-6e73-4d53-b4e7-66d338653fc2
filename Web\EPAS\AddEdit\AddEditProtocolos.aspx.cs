﻿using Business.Provincias;
using Business.Services.Contratos;
using Business.Services.Epas;
using Business.Services.Provincias;
using Business.Services.Usuarios;
using DevExpress.Web;
using DevExpress.Xpo.DB.Helpers;
using Domain;
using Reportes.xsd;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics.Contracts;
using System.Linq;
using System.ServiceModel.Channels;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Web.EPAS.Lista;

namespace Web.EPAS.AddEdit
{
    public partial class AddEditProtocolos : System.Web.UI.Page
    {
        protected void Page_Init(Object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                Session["ListaDeterminaciones"] = new List<Domain.ProtocoloDeterminaciones>();
            }

            LlenadoCbDeterminaciones();
            LlenadoCbPlantilla();
            LlenadocmbAnalisisTipo();
            LlenadocmbFuentes();


            if (!IsPostBack)
            {

                if (Request.QueryString["idContrato"] != null)
                {

                    int idSolicitante = Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["idContrato"].ToString()));
                    CargarSolicitante(idSolicitante);

                }
                else
                {

                }

                if(Request.QueryString["idRevision"] != null)
                {

                    int idRevision = Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["idRevision"].ToString()));
                    CargarRevision(idRevision);
                    ltr_nombreBreadcrumb.Text = "Revisión de Protocolo";

                } else
                {

                }

                if (Request.QueryString["id"] != null)
                {

                    int idProtocolo = Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["id"].ToString()));
                    CargarProtocolo(idProtocolo);
                    ltr_nombreBreadcrumb.Text = "Editar Protocolo";
                }
                else
                {

                }
            
            }


        }

        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {

                if (!IsPostBack)
                {

                    gvDeterminaciones.DataBind();
                    GrVTomador.DataBind();

                }
            }
            catch
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al cargar la página, consulte al administrador del sistema', 'error');", true);
            }
        }


        protected override void OnPreRender(EventArgs e)
        {
            base.OnPreRender(e);

            if (!IsPostBack)
            {

                DateTime n = DateTime.Now;

               /* deFechaCert.MaxDate = n;
                deFechaCert.EditFormat = DevExpress.Web.EditFormat.Custom;
                deFechaCert.EditFormatString = "dd/MM/yyyy";
                deFechaCert.DisplayFormatString = "dd/MM/yyyy";*/

            }
        }

        private void CargarRevision(int idRevision)
        {
            CargarProtocolo(idRevision);

            AnularCamposRevision();
            btnGuardarProtocolo.Visible = false;
            btnAprobarProtocolo.Visible = true;
            btnCancelarProtocolo.Visible = false;
            btnRechazarProtocolo.Visible = true;

        }

        private void CargarProtocolo(int idProtocolo)
        {
            try
            {
                Domain.Protocolos protocolo = ProtocolosService.GetById(idProtocolo);
                if(protocolo != null)
                {
                    Contratos contrato = ContratosServices.GetById(protocolo.Contratos_IdContratoSolicitante.Id);

                    txtNroProtocolo.Text = protocolo.NroProtocolo.ToString();
                    if(protocolo.Contratos_IdContratoSolicitante != null)
                    {
                        hf_idSolicitante.Value = protocolo.Contratos_IdContratoSolicitante.Id.ToString();
                        txtSolicitante.Text = protocolo.Contratos_IdContratoSolicitante.RazonSocial + " (" + protocolo.Contratos_IdContratoSolicitante.NroContrato + ")";
                    }
                    cmbAnalisisTipo.SelectedItem = cmbAnalisisTipo.Items.FindByValue(protocolo.TipoAnalisis.Id);
                    cmbFuente.SelectedItem = cmbFuente.Items.FindByValue(protocolo.Fuentes.Id);
                    if (protocolo.SubFuentes != null)
                    {
                        CompletarComboSubFuentes(Convert.ToInt32(protocolo.SubFuentes.Fuentes.Id));
                        cmbSubFuente.SelectedItem = cmbSubFuente.Items.FindByValue(protocolo.SubFuentes.Id);
                    }
                    txtSitioToma.Text = protocolo.ObservacionSitioToma;
                    dateFechaToma.Date = Convert.ToDateTime(protocolo.FechaToma);
                    chbImprimirLeyenda.Checked = Convert.ToBoolean(protocolo.ImprimirLeyendas);
                    chbUnidadesPrimarias.Checked = Convert.ToBoolean(protocolo.UnidadesPrimarias);
                    dateRecepToma.Date = Convert.ToDateTime(protocolo.RecepcionToma);
                    txtModulo.Text = protocolo.Modulo.ToString();
                    seMontoTotal.Text = protocolo.MontoTotal.ToString();
                    txtExpediente.Text = protocolo.Expediente;
                    txtLaboratorio.Text = protocolo.Laboratorio;
                    txtOpcionesResultados.Text = protocolo.OpcionesResultados;
                    txtInformes.Text = protocolo.Informes;
                    memoObservacion.Text = protocolo.Observaciones;

                    if(!string.IsNullOrEmpty(protocolo.Veredicto))
                    {
                        rbVeredicto.SelectedItem = rbVeredicto.Items.FindByText(protocolo.Veredicto);
                    }
                    memoObservacionVeredicto.Text = protocolo.ObservacionVeredicto;

                    //tomador
                    if (protocolo.Contratos_IdContratoTomador != null)
                    {
                        hf_idTomador.Value = protocolo.Contratos_IdContratoTomador.Id.ToString();
                        txtTomadorMuestra.Text = protocolo.Contratos_IdContratoTomador.RazonSocial+" (" + protocolo.Contratos_IdContratoTomador.NroContrato + ")";
                    }

                    IList<Domain.ProtocoloDeterminaciones> listaDet = ProtocoloDeterminacionesService.GetByIDProtocolo(protocolo.Id);

                    Session["ListaDeterminaciones"] = listaDet;
                    gvDeterminaciones.DataSource = listaDet;
                    gvDeterminaciones.DataBind();


                }
            } catch(Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al cargar la página, consulte al administrador del sistema', 'error');", true);

            }
        }

        protected void LlenadoCbDeterminaciones()
        {
            CmbDeterminaciones.DataSource = DeterminacionesService.GetByDeterminacionActiva(true);
            CmbDeterminaciones.TextField = "Descripcion";
            CmbDeterminaciones.ValueField = "Id";
            CmbDeterminaciones.DataBind();
        }

        protected void LlenadoCbPlantilla()
        {
            CmbPlantilla.DataSource = PlantillaProtocolosService.GetByPlantillaActiva(true);
            CmbPlantilla.TextField = "Descripcion";
            CmbPlantilla.ValueField = "Id";
            CmbPlantilla.DataBind();

        }

        protected void LlenadocmbAnalisisTipo()
        {

            cmbAnalisisTipo.DataSource = AnalisisTipoService.GetByAnalisisTipo(true);
            cmbAnalisisTipo.TextField = "Descripcion";
            cmbAnalisisTipo.ValueField = "Id";
            cmbAnalisisTipo.DataBind();
            ListEditItem list = new ListEditItem();
            list.Text = "Seleccione...";
            list.Value = -1;
            cmbAnalisisTipo.Items.Insert(0,list);

        }

        protected void LlenadocmbFuentes()
        {

            cmbFuente.DataSource = FuentesService.GetAll();
            cmbFuente.TextField = "Descripcion";
            cmbFuente.ValueField = "Id";
            cmbFuente.DataBind();
            ListEditItem list = new ListEditItem();
            list.Text = "Seleccione...";
            list.Value = -1;
            cmbFuente.Items.Insert(0,list);
        }

        protected void cmbSubFuente_Callback(object sender,CallbackEventArgsBase e)
        {
            try
            {
                if(cmbFuente.SelectedItem != null)
                {

                    CompletarComboSubFuentes(Convert.ToInt32(cmbFuente.SelectedItem.Value));
                }
            } catch(Exception ex)
            {
            }
        }

        protected void CompletarComboSubFuentes(int idFuente)
        {
            if (idFuente > 0)
            {

                cmbSubFuente.DataSource = SubFuentesService.GetByFuentes(idFuente);
                cmbSubFuente.TextField = "Descripcion";
                cmbSubFuente.ValueField = "Id";
                cmbSubFuente.DataBind();
                ListEditItem list = new ListEditItem();
                list.Text = "Seleccione...";
                list.Value = -1;
                cmbSubFuente.Items.Insert(0,list);
            }
        }

        private void CargarSolicitante(int idSolicitante)
        {

            try
            {
                Contratos contrato = ContratosServices.GetById(idSolicitante);

                if (contrato != null)
                {
                    txtSolicitante.Text = contrato.Nombre + " (" + contrato.NroContrato + ")";
                    hf_idSolicitante.Value = contrato.Id.ToString();

                }
            }
            catch
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al cargar la página, consulte al administrador del sistema', 'error');", true);

            }
        }

        protected void btnBuscarTomador_Click(object sender, EventArgs e)
        {

        }

      

      

        protected void btnSeleccionarTomador_Init(object sender, EventArgs e)
        {
            try
            {
                ASPxButton btn = (ASPxButton)sender;
                GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer)btn.NamingContainer;
                var ind = DataBinder.Eval(c.DataItem, "Id");
                string cto= DataBinder.Eval(c.DataItem, "NroContrato").ToString();
                string razonsocial= DataBinder.Eval(c.DataItem, "RazonSocial").ToString();
                string nombre = razonsocial + " (" + cto + ")";
                //mando el id como parametro en el callback
                btn.ClientSideEvents.Click = String.Format("function(s,e){{ pcBuscarTomador.Hide(); $('#hf_idTomador').val('{0}');txtTomadorMuestra.SetText('{1}');}}", ind.ToString(), nombre.ToString());

            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "ksdfsd4ey1", "showMessage('" + ex.Message + "', 'error');", true);
            }
        }

      

       

        protected void gvDeterminaciones_DataBinding(object sender, EventArgs e)
        {
            gvDeterminaciones.DataSource = GetListaDeterminaciones();
        }

        private IList<Domain.ProtocoloDeterminaciones> GetListaDeterminaciones()
        {
            IList<Domain.ProtocoloDeterminaciones> listaPd = (IList<Domain.ProtocoloDeterminaciones>) Session["ListaDeterminaciones"];
            if(listaPd!=null && listaPd.Count > 0)
            {
                return (IList<Domain.ProtocoloDeterminaciones>)listaPd.Where(p => p.Id >= 0).ToList<Domain.ProtocoloDeterminaciones>();
            }
            else
            {
                return new List<Domain.ProtocoloDeterminaciones>();
            }
           
        }

        protected void btnAgregarDeterminacion_Click(object sender, EventArgs e)
        {
            try
            {
                if (CmbDeterminaciones.SelectedItem != null)
                {
                    int determinacionId = Convert.ToInt32(CmbDeterminaciones.SelectedItem.Value);
                    Domain.Determinaciones determinacion = DeterminacionesService.GetById(determinacionId);

                    if (determinacion != null)
                    {
                        IList<Domain.ProtocoloDeterminaciones> lista = GetListaDeterminaciones();


                        if (!lista.Any(item => item.Determinaciones.Id == determinacionId))
                        {
                            ProtocoloDeterminaciones pd = new ProtocoloDeterminaciones();
                            pd.Determinaciones = determinacion;

                            lista.Add(pd);

                            Session["ListaDeterminaciones"] = lista;

                            gvDeterminaciones.DataBind();

                            CmbDeterminaciones.Text = "";

                        }
                        else
                        {
                            CmbDeterminaciones.Text = "";
                            string message = string.Format("La Determinación {0} ya ha sido seleccionada!", determinacion.Descripcion);
                            ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('" + message + "')", true);
                        }
                    }
                    else
                    {
                        ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Debe seleccionar una Determinación!')", true);
                    }
                }
            }catch(Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Se produjo un error, por favor intente nuevamente. " + ex.Message + "')", true);

            }
        }

        protected void btnEliminarDeterminacion_Click(object sender, EventArgs e)
        {
            try
            {
                ASPxButton btn = (ASPxButton)sender;
                GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer)btn.NamingContainer;
                var ind = DataBinder.Eval(c.DataItem,"Determinaciones.NroDeterminacion");

                List<Domain.ProtocoloDeterminaciones> ListaDeterminaciones;

                if (Session["ListaDeterminaciones"] != null)
                {
                    ListaDeterminaciones = (List<Domain.ProtocoloDeterminaciones>)Session["ListaDeterminaciones"];

                }
                else
                {
                    ListaDeterminaciones = new List<Domain.ProtocoloDeterminaciones>();
                }

                Domain.ProtocoloDeterminaciones det = ListaDeterminaciones.Find(q => q.Determinaciones.NroDeterminacion == Convert.ToInt32(ind));

                if (det != null)
                {
                    if(det.Id != 0)
                    {
                        det.Id = new Random().Next(0,1000) * -1;
                    } else
                    {
                        ListaDeterminaciones.Remove(det);
                    }
                    Session["ListaDeterminaciones"] = ListaDeterminaciones;

                    gvDeterminaciones.DataBind();

                    string message = string.Format("La Determinación {0} se quitó con éxito!", det.Determinaciones.Descripcion);
                    ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('" + message + "')", true);

                }


            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al tratar de eliminar un elemento, consulte al administrador del sistema', 'error');", true);

            }
        }

        protected void btnAgregarPlantilla_Click(object sender, EventArgs e)
        {
            try
            {
                if (CmbPlantilla.SelectedItem != null)
                {
                    int plantillaId = Convert.ToInt32(CmbPlantilla.SelectedItem.Value);
                    Domain.PlantillaProtocolos plantilla = PlantillaProtocolosService.GetById(plantillaId);

                    if (plantilla != null && plantilla.Determinaciones != null && plantilla.Determinaciones.Any())
                    {
                        // Obtiene la lista de Determinaciones de la sesión.
                        List<Domain.ProtocoloDeterminaciones> listaDeterminaciones = (List<Domain.ProtocoloDeterminaciones>)Session["ListaDeterminaciones"];

                        // Verifica si las Determinaciones de la Plantilla ya existen en la grilla.
                        foreach(Domain.Determinaciones determinacion in plantilla.Determinaciones)
                        {
                            // Verifica si el ID de la Determinación ya existe en la lista de Determinaciones de la sesión.
                            // Si ya existe alguna Determinación, sólo agregará las que no estén en la grilla.
                            if(!listaDeterminaciones.Any(d => d.Determinaciones.Id == determinacion.Id))
                            {
                                ProtocoloDeterminaciones pd = new ProtocoloDeterminaciones();

                                pd.Determinaciones = determinacion;
                                // Aquí agrega la/las Determinación/ones que no estén, a la lista de Determinaciones de la sesión.
                                listaDeterminaciones.Add(pd);

                                Session["ListaDeterminaciones"] = listaDeterminaciones;

                                // Actualiza la grilla con las nuevas Determinaciones agregadas.
                                gvDeterminaciones.DataBind();

                            }
                            else
                            {
                                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Una o más Determinaciones ya han sido cargadas. Se agregarán a la grilla sólo las que no están!')", true);
                            }
                        }

                        // Actualiza la sesión con la lista combinada
                        Session["ListaDeterminaciones"] = listaDeterminaciones;

                    }
                    else
                    {
                        ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('La Plantilla no tiene Determinaciones cargadas!')", true);
                    }
                }
                else
                {
                    ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Debe seleccionar una Plantilla!')", true);
                }
            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Se produjo un error, por favor intente nuevamente. "+ex.Message+"')", true);

            }
        }

        protected void btnGuardarProtocolo_Click(object sender,EventArgs e)
        {
            try
            {
                Domain.Protocolos protocolo;

                if(Request.QueryString["id"] != null)
                {
                    protocolo = ProtocolosService.GetById(Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["id"].ToString())));
                } else
                {

                    //primero validar antes de guardar
                    if (string.IsNullOrEmpty(hf_idSolicitante.Value))
                    {
                        string messagePendiente = string.Format("Asigne un solicitante!");
                        ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('" + messagePendiente + "')", true);
                        return;
                    }

                        protocolo = new Domain.Protocolos();
                    protocolo.NroProtocolo = NumeracionServices.getByCodigo("P").Numero;
                    protocolo.ProtocolosEstados = ProtocoloEstadoService.GetByCodigo("P");

                    if(Session["usuario"] != null)
                    {
                        int idUsuario = (int) Session["usuario"];
                        Usuario u = UsuarioService.getByIdNoInitialize(idUsuario);
                        if(u != null)
                        {
                            protocolo.Usuario_IdUsuario = u;
                        }
                    }
                };

                //Carga de Solicitante
                if(!string.IsNullOrEmpty(hf_idSolicitante.Value))
                {
                    Contratos solicitante = ContratosServices.GetById(Convert.ToInt32(hf_idSolicitante.Value));
                    if(solicitante != null)
                    {
                        protocolo.Contratos_IdContratoSolicitante = solicitante;
                    }                    
                }

                if(cmbAnalisisTipo.SelectedItem != null && cmbAnalisisTipo.SelectedItem.Value != null)
                {
                    TipoAnalisis AnalisisTipo = AnalisisTipoService.GetById(Convert.ToInt32(cmbAnalisisTipo.SelectedItem.Value));
                    protocolo.TipoAnalisis = AnalisisTipo;
                };

                if(cmbFuente.SelectedItem != null && cmbFuente.SelectedItem.Value != null)
                {
                    Domain.Fuentes fuente = FuentesService.GetById(Convert.ToInt32(cmbFuente.SelectedItem.Value));
                    protocolo.Fuentes = fuente;
                }

                if(cmbSubFuente.SelectedItem != null && cmbSubFuente.SelectedItem.Value != null)
                {
                    Domain.SubFuentes subFuente = SubFuentesService.GetById(Convert.ToInt32(cmbSubFuente.SelectedItem.Value));
                    protocolo.SubFuentes = subFuente;
                }

                protocolo.ObservacionSitioToma = txtSitioToma.Text;
                protocolo.FechaToma = Convert.ToDateTime(dateFechaToma.Date);
                protocolo.ImprimirLeyendas = chbImprimirLeyenda.Checked;
                protocolo.UnidadesPrimarias = chbUnidadesPrimarias.Checked;

                //Carga de tomador
                if(!string.IsNullOrEmpty(hf_idTomador.Value))
                {
                    Contratos tomador = ContratosServices.GetById(Convert.ToInt32(hf_idTomador.Value));
                    if(tomador != null)
                    {
                        protocolo.Contratos_IdContratoTomador = tomador;
                    }
                }

                protocolo.RecepcionToma = Convert.ToDateTime(dateRecepToma.Date);

                if(!string.IsNullOrEmpty(txtModulo.Text))
                {
                    protocolo.Modulo = Convert.ToInt32(txtModulo.Text);
                }

                if(!string.IsNullOrEmpty(seMontoTotal.Text))
                {
                    protocolo.MontoTotal = Convert.ToDecimal(seMontoTotal.Text);
                }
                //string expediente = $"{txtExpediente1.Text}-{txtExpediente2.Text}-{txtExpediente3.Text}-{txtExpediente4.Text}";                    
                protocolo.Expediente = txtExpediente.Text;
                protocolo.Laboratorio = txtLaboratorio.Text;
                protocolo.OpcionesResultados = txtOpcionesResultados.Text;
                protocolo.Informes = txtInformes.Text;
                protocolo.Observaciones = memoObservacion.Text;
                protocolo.OpcionesResultados = txtOpcionesResultados.Text;
                protocolo.Informes = txtInformes.Text;
                protocolo.Veredicto = rbVeredicto.SelectedItem == null ? string.Empty : rbVeredicto.SelectedItem.Text;
                protocolo.ObservacionVeredicto = memoObservacionVeredicto.Text;
                protocolo.FechaCreado = DateTime.Now;

                ProtocolosService.SaveOrUpdate(protocolo);

                if(Session["ListaDeterminaciones"] != null)
                {
                    List<Domain.ProtocoloDeterminaciones> listaDeterminaciones = (List<Domain.ProtocoloDeterminaciones>) Session["ListaDeterminaciones"];
                    int totalDet = listaDeterminaciones.Count;
                    int totalDetCompletas = 0;

                    //recorro la lista de determinaciones del protocolo
                    foreach(Domain.ProtocoloDeterminaciones det in listaDeterminaciones)
                    {
                        if (det != null)
                        {
                            //verifico si tiene cargado el resultado
                            if (det.Resultado != null && !string.IsNullOrEmpty(det.Resultado.ToString()))
                            {
                                totalDetCompletas++;
                            }

                            if (det.Id == 0)
                            {
                                //carga nueva determinación
                                det.Protocolos = protocolo;
                                ProtocoloDeterminacionesService.SaveOrUpdate(det);

                            }
                            else
                            {
                                if (det.Id < 0)
                                {
                                    //Elimina la determinación de la base
                                    ProtocoloDeterminaciones pd1 = ProtocoloDeterminacionesService.GetByIDProtocoloAndIDDeterminacion(protocolo.Id, det.Determinaciones.Id);
                                    if (pd1 != null)
                                    {
                                        ProtocoloDeterminacionesService.Delete(pd1);
                                    }
                                }
                                else
                                {
                                    //Actualiza resultado y observación
                                    ProtocoloDeterminacionesService.SaveOrUpdate(det);
                                }
                            }
                        }                        
                    }

                    //cambio estado si completo todos los resultados
                    if (totalDet == totalDetCompletas)
                    {
                        ProtocolosEstados pe = ProtocoloEstadoService.GetByCodigo("ER");
                        protocolo.ProtocolosEstados = pe;
                        
                        ProtocolosService.SaveOrUpdate(protocolo);

                        string messagePendiente = string.Format("El Protocolo {0} se guardó con éxito y paso a estado En Revisión!",protocolo.TipoAnalisis.Descripcion);
                        ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('" + messagePendiente + "')",true);
                    }

                }

                btnGuardarProtocolo.ClientEnabled = false;
                btnCancelarProtocolo.Visible = false;
                btnVolverProtocolo.Visible = true;
                AnularCamposRevision();

                string messageNuevo = string.Format("El Protocolo {0} se guardó con éxito y paso a estado Pendiente!",protocolo.TipoAnalisis.Descripcion);
                ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('" + messageNuevo + "')",true);

            } catch(Exception ex)
            {
                ScriptManager.RegisterStartupScript(this,this.GetType(),"keyError",$"alert('Error al guardar los datos!');",true);
            }



        }

        protected void btnGuardarResultado_Click(object sender,EventArgs e)
        {
            try
            {
                if (Session["ListaDeterminaciones"] != null)
                {
                    List<Domain.ProtocoloDeterminaciones> listaDeterminaciones = (List<Domain.ProtocoloDeterminaciones>)Session["ListaDeterminaciones"];

                    if (listaDeterminaciones != null && listaDeterminaciones.Count > 0)
                    {
                        int nroDet = Convert.ToInt32(hf_ResultadoDeterminacion.Value);
                        Domain.ProtocoloDeterminaciones pd = listaDeterminaciones.Find(q => q.Determinaciones.NroDeterminacion == nroDet);

                        if (pd != null)
                        {
                            pd.Signo = txtSigno.Text.ToString();
                            pd.Resultado = Convert.ToDecimal(txtResultado.Text);
                            pd.Observacion = memoObservacionResultado.Text;

                            Session["ListaDeterminaciones"] = listaDeterminaciones;
                            gvDeterminaciones.DataBind();
                        }
                    }

                }
            }catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "keyError", $"alert('Error al guardar los datos!');", true);
            }
        }

        protected void btnCargarResultado_Init(object sender,EventArgs e)
        {
            try
            {
                ASPxButton btn = (ASPxButton) sender;
                GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer) btn.NamingContainer;
                var ind = DataBinder.Eval(c.DataItem,"Determinaciones.NroDeterminacion");

                //mando el id como parametro en el callback
                btn.ClientSideEvents.Click = String.Format("function(s,e){{ pcCargarResultado.Show();$('#hf_ResultadoDeterminacion').val('{0}'); }}",ind.ToString());

            } catch(Exception ex)
            {
                ScriptManager.RegisterStartupScript(this,this.GetType(),"ksdfsd4ey1","showMessage('" + ex.Message + "', 'error');",true);
            }
        }

        protected void GrVTomador_DataBinding(object sender, EventArgs e)
        {
            try{
                if (!string.IsNullOrEmpty(txtBarraBusquedaTomador.Text))
                {
                    IList<Domain.Contratos> lst = Business.Services.Contratos.ContratosServices.GetByCamposBusqueda(Convert.ToInt32(cmbBuscarTomador.Value),txtBarraBusquedaTomador.Text);
                    IList<Domain.Contratos> contratosActivos = new List<Domain.Contratos>();

                    if (lst != null && lst.Count >= 1)
                    {
                        foreach (var contrato in lst)
                        {
                            if (contrato.EsTomador == true)
                            {
                                contratosActivos.Add(contrato);
                            }
                        }
                    }

                    if (contratosActivos.Count >= 1)
                    {
                        GrVTomador.DataSource = contratosActivos;
                       // GrVTomador.DataBind();
                    }
                    else
                    {
                        GrVTomador.DataSource = null;
                       // GrVTomador.DataBind();
                    }
                }
            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "ksdfsd4ey1", "showMessage('" + ex.Message + "', 'error');", true);
            }
        }

        protected void GrVSolicitante_DataBinding(object sender,EventArgs e)
        {
            GrVSolicitante.DataSource = GetDatosSolicitante();
        }

        private object GetDatosSolicitante()
        {
            if(cmbBuscarSolicitante.SelectedItem != null)
            {

                IList<Domain.Contratos> lst = Business.Services.Contratos.ContratosServices.GetByCamposBusqueda(Convert.ToInt32(cmbBuscarSolicitante.Value),txtBarraBusquedaSolicitante.Text);

                if(lst != null && lst.Count > 0)
                {
                    return lst;

                } else
                {

                    ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('No se encontraron resultados.')",true);
                    return null;
                }
            } else
            {
                return null;
            }
        }

        protected void btnSeleccionarSolicitante_Init(object sender,EventArgs e)
        {
            try
            {
                ASPxButton btn = (ASPxButton) sender;
                GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer) btn.NamingContainer;
                var ind = DataBinder.Eval(c.DataItem,"Id");

                string cto = DataBinder.Eval(c.DataItem,"NroContrato").ToString();
                string razonsocial = DataBinder.Eval(c.DataItem,"RazonSocial").ToString();
                string nombre = razonsocial + " (" + cto + ")";

                //Mando el id como parámetro en el callback
                btn.ClientSideEvents.Click = String.Format("function(s,e){{ pcBuscarSolicitante.Hide(); $('#hf_idSolicitante').val('{0}');txtSolicitante.SetText('{1}');}}",ind.ToString(),nombre.ToString());

            } catch(Exception ex)
            {
                ScriptManager.RegisterStartupScript(this,this.GetType(),"ksdfsd4ey1","showMessage('" + ex.Message + "', 'error');",true);
            }
        }

        private void AnularCamposRevision()
        {
            btnBuscarSolicitante.ClientEnabled = false;
            cmbAnalisisTipo.ClientEnabled = false;
            cmbFuente.ClientEnabled = false;
            cmbSubFuente.ClientEnabled = false;
            txtSitioToma.ClientEnabled = false;
            dateFechaToma.ClientEnabled = false;
            chbImprimirLeyenda.ClientEnabled = false;
            chbUnidadesPrimarias.ClientEnabled = false;
            txtTomadorMuestra.ClientEnabled = false;
            btnBuscarTomador.ClientEnabled = false;
            dateRecepToma.ClientEnabled = false;
            txtModulo.ClientEnabled = false;
            seMontoTotal.ClientEnabled = false;
            txtExpediente.ClientEnabled = false;
            txtLaboratorio.ClientEnabled = false;
            txtOpcionesResultados.ClientEnabled = false;
            txtInformes.ClientEnabled = false;
            memoObservacion.ClientEnabled = false;
            rbVeredicto.ClientEnabled = false;
            memoObservacionVeredicto.ClientEnabled = false;
            CmbDeterminaciones.ClientEnabled = false;
            btnAgregarDeterminacion.ClientEnabled = false;
            CmbPlantilla.ClientEnabled = false;
            btnAgregarPlantilla.ClientEnabled = false;

        }

        protected void btnAprobarProtocolo_Click(object sender,EventArgs e)
        {
            try
            {
                Domain.Protocolos protocolo;

                if(Request.QueryString["idRevision"] != null)
                {
                    protocolo = ProtocolosService.GetById(Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["idRevision"].ToString())));

                    if(Session["usuario"] != null)
                    {
                        int idUsuario = (int) Session["usuario"];
                        Usuario u = UsuarioService.getByIdNoInitialize(idUsuario);
                        if(u != null)
                        {
                            protocolo.Usuario_IdUsuarioAprobo = u;
                        }
                    }

                    ProtocolosEstados aprobado = ProtocoloEstadoService.GetByCodigo("A");
                    protocolo.ProtocolosEstados = aprobado;                    
                    protocolo.FechaAprobado = DateTime.Now;
                    
                    ProtocolosService.SaveOrUpdate(protocolo);

                    string messageAprobado = string.Format("El Protocolo {0} se aprobó con éxito!",protocolo.TipoAnalisis.Descripcion);
                    ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('" + messageAprobado + "')",true);

                    btnAprobarProtocolo.Visible = false;
                    btnRechazarProtocolo.Visible = false;
                    btnVolverProtocolo.Visible = true;
                }

            }catch(Exception ex) { 
            }
        }

        protected void btnRechazarProtocolo_Click(object sender,EventArgs e)
        {
            try
            {
                Domain.Protocolos protocolo;

                if(Request.QueryString["idRevision"] != null)
                {
                    protocolo = ProtocolosService.GetById(Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["idRevision"].ToString())));

                    ProtocolosEstados pendiente = ProtocoloEstadoService.GetByCodigo("P");
                    protocolo.ProtocolosEstados = pendiente;

                    ProtocolosService.SaveOrUpdate(protocolo);

                    string message = string.Format("El Protocolo {0} fue rechazado y volvió al estado Pendiente.",protocolo.TipoAnalisis.Descripcion);
                    ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('" + message + "')",true);

                    btnAprobarProtocolo.ClientEnabled = false;
                    btnRechazarProtocolo.Visible = false;
                    btnVolverProtocolo.Visible = true;

                }

            } catch(Exception ex)
            {
            }
        }

        protected bool GetVisibleQuitar(object P)
        {
            bool salida = false;

            if(P == null)
            {
                salida = true;

            } else
            {
                Domain.Protocolos protocolo = (Domain.Protocolos)P;

                if(protocolo != null)
                {
                    if(protocolo.ProtocolosEstados.Codigo == "P")
                    {
                        salida = true;
                    }                    
                }
            }

            return salida;
        }

        protected bool GetVisibleCargarResultado(object P)
        {
            bool salida = false;

            if(P == null)
            {
                salida = true;

            } else
            {
                Domain.Protocolos protocolo = (Domain.Protocolos)P;

                if(protocolo != null)
                {
                    if(protocolo.ProtocolosEstados.Codigo == "P")
                    {
                        salida = true;
                    }                    
                }
            }

            return salida;
        }
    }
}