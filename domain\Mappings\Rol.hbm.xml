<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="Rol" table="Rol">
    <id name="Id" type="Int32">
      <column name="id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Codigo" type="String">
      <column name="codigo" not-null="false" length="50" sql-type="nvarchar" />
    </property>
    <property name="Nombre" type="String">
      <column name="nombre" not-null="true" length="50" sql-type="nvarchar" />
    </property>
    <property name="Observacion" type="String">
      <column name="observacion" not-null="false" sql-type="nvarchar" />
    </property>
    <property name="Eliminable" type="Boolean">
      <column name="eliminable" not-null="true" sql-type="bit" />
    </property>
    <property name="Renombrable" type="Boolean">
      <column name="renombrable" not-null="true" sql-type="bit" />
    </property>
    <property name="Editable" type="Boolean">
      <column name="editable" not-null="true" sql-type="bit" />
    </property>
    <property name="Superadmin" type="Boolean">
      <column name="superadmin" default="false" not-null="true" sql-type="bit" />
    </property>
    <property name="Admin" type="Boolean">
      <column name="admin" not-null="false" sql-type="bit" />
    </property>
    <property name="IdRolModulo" type="Int32">
      <column name="IdRolModulo" not-null="false" precision="10" scale="0" sql-type="int" />
    </property>
    <many-to-one name="Determinaciones" class="Determinaciones">
      <column name="IdDeterminacion" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <set name="Usuarios" table="RolUsuario" generic="true">
      <key>
        <column name="IdRol" not-null="false" precision="10" scale="0" sql-type="int" />
      </key>
      <many-to-many class="Usuario" fetch="join">
        <column name="IdUsuario" not-null="false" precision="10" scale="0" sql-type="int" />
      </many-to-many>
    </set>
    <set name="RolDeterminaciones" inverse="true" generic="true">
      <key>
        <column name="IdRol" />
      </key>
      <one-to-many class="RolDeterminaciones" />
    </set>
    <set name="RolModulos" inverse="true" generic="true">
      <key>
        <column name="IdRol" />
      </key>
      <one-to-many class="RolModulo" />
    </set>
  </class>
</hibernate-mapping>