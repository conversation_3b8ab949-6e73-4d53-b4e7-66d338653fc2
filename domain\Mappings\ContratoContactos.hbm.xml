<?xml version="1.0" encoding="utf-8"?>
<hibernate-mapping schema="dbo" assembly="Domain" namespace="Domain" xmlns="urn:nhibernate-mapping-2.2">
  <class name="ContratoContactos" table="ContratoContactos">
    <id name="Id" type="Int32">
      <column name="Id" not-null="true" precision="10" scale="0" sql-type="int" />
      <generator class="identity" />
    </id>
    <property name="Nombre" type="String">
      <column name="Nombre" not-null="true" length="50" sql-type="nvarchar" />
    </property>
    <property name="Apellido" type="String">
      <column name="Apellido" not-null="true" length="50" sql-type="nvarchar" />
    </property>
    <property name="NumeroDoc" type="String">
      <column name="NumeroDoc" not-null="true" length="11" sql-type="nvarchar" />
    </property>
    <property name="FechaNac" type="DateTime">
      <column name="FechaNac" not-null="false" sql-type="datetime" />
    </property>
    <property name="Activo" type="Boolean">
      <column name="Activo" default="1" not-null="false" sql-type="bit" />
    </property>
    <many-to-one name="DocTipos" class="DocTipos">
      <column name="IdDocTipo" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <many-to-one name="Sexos" class="Sexos">
      <column name="IdTipoSexo" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <many-to-one name="Contratos" class="Contratos">
      <column name="IdContrato" not-null="false" precision="10" scale="0" sql-type="int" />
    </many-to-one>
    <set name="ContratoContactosEmails" inverse="true" cascade="delete" generic="true">
      <key>
        <column name="IdContratoContacto" />
      </key>
      <one-to-many class="ContratoContactosEmail" />
    </set>
    <set name="ContratoContactosTels" inverse="true" generic="true">
      <key>
        <column name="IdContratoContacto" />
      </key>
      <one-to-many class="ContratoContactosTel" />
    </set>
  </class>
</hibernate-mapping>