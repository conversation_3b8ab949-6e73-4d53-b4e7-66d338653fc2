﻿using System;
using Business.Services.Epas;
using DevExpress.Web;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Business.Services.Provincias;

namespace Web.EPAS.Lista
{
    public partial class Zonas : System.Web.UI.Page {
        protected void Page_Load(object sender,EventArgs e) {

            CargarDatosEnGv_Zonas();
            if (!IsPostBack) {
              
            }

        }

        private void CargarDatosEnGv_Zonas()
        {
            try
            {
                List<Domain.Zonas> Subfuentes = (List<Domain.Zonas>)
                   ZonasService.GetAll(true);
                gv_Zonas.DataSource = Subfuentes;
                gv_Zonas.DataBind();
            }
            catch (Exception ex)
            {
                string g = ex.Message;
            }

        }

        protected void btnEditar_Click(object sender, EventArgs e)
        {
            try
            {
                ASPxButton btn = (ASPxButton)sender;
                if (btn != null)
                {
                    GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer)btn.NamingContainer;
                    if (c != null)
                    {
                        var ind = DataBinder.Eval(c.DataItem, "Id");

                        if (ind != null)
                        {
                            Domain.Zonas zona = ZonasService.GetById(Convert.ToInt32(ind));

                            if(zona.Activo == false)
                            {
                                string message = string.Format("La zona {0} no se puede editar ya que está anulada.",zona.Descripcion);
                                ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('" + message + "')",true);

                            } else
                            {
                                Response.Redirect(Global.ApplicationPath + "/EPAS/AddEdit/AddEditZonas.aspx?id=" + Util.Helpers.Encrypt(ind.ToString()), false);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al tratar de editar un elemento, consulte al administrador del sistema', 'error');", true);
            }
        }

        protected void btnAnularActivar_Click(object sender, EventArgs e)
        {
            try
            {
                ASPxButton btn = (ASPxButton)sender;
                GridViewDataItemTemplateContainer f = (GridViewDataItemTemplateContainer)btn.NamingContainer;
                var ind = DataBinder.Eval(f.DataItem, "Id");

                Domain.Zonas zona = ZonasService.GetById(Convert.ToInt32(ind));
                zona.Activo = !zona.Activo;

                ZonasService.SaveOrUpdate(zona);
                CargarDatosEnGv_Zonas();

            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "key1", "showMessage('Error al tratar de eliminar un elemento, consulte al administrador del sistema', 'error');", true);

            }
        }        
    }
}