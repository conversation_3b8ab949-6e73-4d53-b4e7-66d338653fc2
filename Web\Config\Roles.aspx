﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Roles.aspx.cs" Inherits="Web.Config.Roles" %>

<%@ Register Assembly="DevExpress.Web.v18.1" Namespace="DevExpress.Web" TagPrefix="dx" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
     <script type="text/javascript">
        function OnToolbarItemClick(s, e) {
            if (IsCustomExportToolbarCommand(e.item.name)) {
                e.processOnServer = true;
                e.usePostBack = true;
            } else {

            }
        }
        function IsCustomExportToolbarCommand(command) {
            return command == "CustomExportToXLS" || command == "CustomExportToXLSX";
        }
    </script>
</asp:Content>


<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

     <!-- Page header -->
   <div class="page-header page-header-light shadow">
     <div class="page-header-content d-lg-flex border-top">
         <div class="d-flex">
             <div class="breadcrumb py-2">
                    <a href="<%= Web.Util.Helpers.GetApplicationPath() %>/Index.aspx" class="breadcrumb-item"><i class="icon-home2 mr-2"></i>Inicio</a>
                    <a href="<%= Web.Util.Helpers.GetApplicationPath() %>/Config/Roles.aspx" class="breadcrumb-item"><i class="icon-gear mr-2"></i>Áreas</a>
                    <span class="breadcrumb-item active">
                        <asp:Literal ID="ltr_nombreBreadcrumb" runat="server">Lista</asp:Literal></span>
                </div>

                <a href="#" class="header-elements-toggle text-default d-md-none"><i class="icon-more"></i></a>
            </div>

                    </div>


    </div>
    <!-- /page header -->

    <!-- Page content -->
    <div class="page-content pt-0">

        <!-- Main content -->
        <div class="content-wrapper">

            <!-- Content area -->
            <div class="content">

                <!-- Basic card -->
                <div class="card">
                  <%--  <div class="card-header header-elements-inline">
                        <h5 class="card-title"></h5>
                        <div class="header-elements">
                            <div class="list-icons">
                                <a class="list-icons-item" data-action="collapse"></a>

                            </div>
                        </div>
                    </div>--%>

                    <div class="card-body">

                        <dx:ASPxButton ID="btn_agregar" Theme="Material" runat="server" Text="Agregar" RenderMode="Button" Image-IconID="actions_new_16x16office2013" OnClick="btn_agregar_Click"></dx:ASPxButton>

                        <dx:ASPxGridView ID="gv_areas" runat="server" EnableRowsCache="false" ViewStateMode="Disabled" Theme="Moderno"
                            DataSourceForceStandardPaging="false" EnableViewState="false" Font-Size="Small"
                            ClientInstanceName="gv_users"
                            AutoGenerateColumns="False"
                            KeyFieldName="Id">
                            <Toolbars>
                                <dx:GridViewToolbar ItemAlign="Right" EnableAdaptivity="true">
                                    <Items>

                                        <dx:GridViewToolbarItem Command="Refresh" BeginGroup="true" />
                                        <dx:GridViewToolbarItem Text="Exportar a" Image-IconID="actions_refresh_16x16office2013" DisplayMode="ImageWithText" BeginGroup="true">
                                            <Items>
                                                <dx:GridViewToolbarItem Command="ExportToPdf" />
                                                <dx:GridViewToolbarItem Command="ExportToXlsx" Text="Export to XLSX" />

                                            </Items>
                                        </dx:GridViewToolbarItem>
                                        <dx:GridViewToolbarItem BeginGroup="true">
                                            <Template>
                                                <dx:ASPxButtonEdit ID="tbToolbarSearch" runat="server" NullText="Buscar..." Height="100%" CssClass="form-control">
                                                    <Buttons>
                                                        <dx:SpinButtonExtended Image-IconID="find_find_16x16gray" />
                                                    </Buttons>
                                                </dx:ASPxButtonEdit>
                                            </Template>
                                        </dx:GridViewToolbarItem>
                                    </Items>
                                </dx:GridViewToolbar>
                            </Toolbars>
                            <SettingsSearchPanel CustomEditorID="tbToolbarSearch" />

                            <SettingsExport EnableClientSideExportAPI="true" ExcelExportMode="DataAware" />
                            <ClientSideEvents ToolbarItemClick="OnToolbarItemClick" />

                            <SettingsBehavior AllowFocusedRow="true" FilterRowMode="Auto" AllowDragDrop="false" AllowEllipsisInText="true" />
                            <SettingsPager PageSize="20" AlwaysShowPager="true"></SettingsPager>

                            <SettingsText CommandEdit="Opciones" />
                            <SettingsAdaptivity AdaptivityMode="HideDataCellsWindowLimit" AllowOnlyOneAdaptiveDetailExpanded="true">
                            </SettingsAdaptivity>
                            <SettingsBehavior AllowEllipsisInText="true" ColumnResizeMode="NextColumn" />
                            <EditFormLayoutProperties>
                                <SettingsAdaptivity AdaptivityMode="SingleColumnWindowLimit" SwitchToSingleColumnAtWindowInnerWidth="600" />
                            </EditFormLayoutProperties>
                            <Styles>
                                <Cell Wrap="False" />
                            </Styles>

                            <Settings ShowFilterRow="false" ShowFilterRowMenu="false" VerticalScrollBarMode="Hidden" />
                            <Columns>

                                <dx:GridViewDataColumn Caption="Id" FieldName="Id" SortOrder="Ascending" Width="70px">
                                    <Settings AllowAutoFilter="true" />
                                    <DataItemTemplate>
                                        <dx:ASPxLabel ID="ASPxLabel1" runat="server" Text='<%# Eval("Id")  %>'></dx:ASPxLabel>
                                    </DataItemTemplate>
                                </dx:GridViewDataColumn>
                                    <dx:GridViewDataTextColumn Caption="Codigo" FieldName="Codigo" Width="80px">
                                    <Settings AllowAutoFilter="true" />
                                    <DataItemTemplate>
                                        <dx:ASPxLabel ID="ASPxLabel2" runat="server" Text='<%# Eval("Codigo")   %>'></dx:ASPxLabel>
                                    </DataItemTemplate>
                                </dx:GridViewDataTextColumn>
                                <dx:GridViewDataTextColumn Caption="Nombre" FieldName="Nombre">
                                    <Settings AllowAutoFilter="true" />
                                    <DataItemTemplate>
                                        <dx:ASPxLabel ID="ASPxLabel123" runat="server" Text='<%# Eval("Nombre")  %>'></dx:ASPxLabel>
                                    </DataItemTemplate>
                                </dx:GridViewDataTextColumn>
                            
                             

                                <dx:GridViewDataCheckColumn Caption="SuperAdmin" FieldName="Superadmin">
                                </dx:GridViewDataCheckColumn>

                                <dx:GridViewDataColumn Caption="" VisibleIndex="4" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center" Width="90">
                                    <DataItemTemplate>
                                        <dx:ASPxButton runat="server" ID="btnEditar" RenderMode="Link" Text="Editar"  OnClick="btnEditar_Click" AutoPostBack="false" CommandArgument='<%# Eval("Id" )%>' >
                                               <ClientSideEvents Click="function (s,e){MostrarLoading();}" />
                                        </dx:ASPxButton>
                                    </DataItemTemplate>
                                </dx:GridViewDataColumn>

                                <dx:GridViewDataColumn Caption="" VisibleIndex="5" Settings-AllowEllipsisInText="False" CellStyle-HorizontalAlign="Center" Width="90">
                                    <DataItemTemplate>
                                        <dx:ASPxButton runat="server" ID="btnEliminar" RenderMode="Link" Text="Eliminar" OnClick="btnEliminar_Click" AutoPostBack="false"
                                            CommandArgument='<%# Eval("Id" )%>' Visible='<%# !Convert.ToBoolean(Eval("Superadmin" ))%>' >
                                                <ClientSideEvents Click="function(s,e){e.processOnServer ==confirm('¿Desea eliminar?');}" />
                                        </dx:ASPxButton>
                                    </DataItemTemplate>
                                </dx:GridViewDataColumn>


                            </Columns>

                        </dx:ASPxGridView>
                    </div>
                </div>
                <!-- /basic card -->
            </div>
        </div>

    </div>
</asp:Content>


<asp:Content ID="Content3" ContentPlaceHolderID="footer" runat="server">

</asp:Content>
