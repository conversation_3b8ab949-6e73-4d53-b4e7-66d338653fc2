﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 20/10/2023 15:17:33
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class SubFuentesRepository : NHibernateRepository<Domain.SubFuentes>, ISubFuentesRepository
    {
        public SubFuentesRepository(ISession session) : base(session)
        {
        }

        public virtual ICollection<Domain.SubFuentes> GetAll()
        {
            return session.CreateQuery(string.Format("from SubFuentes")).List<Domain.SubFuentes>();
        }
        public virtual Domain.SubFuentes ExisteSubFuentes(string stringCodigo, string stringDescripcion, int idFuente)
        {
            string hql = "from SubFuentes where Fuentes.Id =:idFuente and (upper(Codigo)=:stringCodigo or upper(Descripcion)=:stringDescripcion) and Activa='true'";
            return session.CreateQuery(hql)
            .SetParameter("stringDescripcion", stringDescripcion.ToUpper())
            .SetParameter("stringCodigo", stringCodigo.ToUpper())
            .SetParameter("idFuente",idFuente)
            .UniqueResult<Domain.SubFuentes>();
        }
        public virtual Domain.SubFuentes GetByKey(int _Id)
        {
            return session.Get<Domain.SubFuentes>(_Id);
        }

        public virtual ICollection<Domain.SubFuentes> GetByFuentes(int idFuentes) {
            return session.CreateQuery(string.Format("from SubFuentes where Fuentes.Id=:idFuentes ORDER BY Descripcion asc")).SetParameter("idFuentes",idFuentes).List<Domain.SubFuentes>();
        }
    }
}
