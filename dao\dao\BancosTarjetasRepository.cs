﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 22/5/2023 14:55:38
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class BancosTarjetasRepository : NHibernateRepository<Domain.BancosTarjetas>, IBancosTarjetasRepository
    {
        public BancosTarjetasRepository(ISession session) : base(session)
        {
        }

        public virtual ICollection<Domain.BancosTarjetas> GetAll()
        {
            return session.CreateQuery(string.Format("from BancosTarjetas")).List<Domain.BancosTarjetas>();
        }
        public virtual ICollection<Domain.BancosTarjetas> GetByBuscarBancos(bool Activo, string Quees)
        {
            string hql = "from BancosTarjetas  WHERE Activo = :Activo and QueEs = :Quees order BY Nombre asc";

            IQuery q = session.CreateQuery(hql);
            q.SetParameter("Activo", Activo);
            q.SetParameter("Quees", Quees);
            return q.List<Domain.BancosTarjetas>();

        }

        public BancosTarjetas GetByKey(int _Id)
        {
            return session.Get<Domain.BancosTarjetas>(_Id);
        }

    }
}

