﻿using Business.Services.Contratos;
using Business.Services.Epas;
using DevExpress.Web;
using DevExpress.Web.Internal;
using Domain;
using NHibernate.Mapping;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Web.EPAS.Lista;

namespace Web.EPAS.AddEdit
{
    public partial class AddEditPlantilla : System.Web.UI.Page
    {
        protected void Page_Init(Object sender,EventArgs e)
        {
            if(!IsPostBack)
            {
                Session["ListaDeterminaciones"] = new List<Domain.Determinaciones>();
            }

            LlenadoCbDeterminaciones();
        }
        protected void Page_Load(object sender,EventArgs e)
        {
            try
            {
                btnVolverEditado.Visible = false;
                if(!IsPostBack)
                {

                    if(Request.QueryString["id"] != null)
                    {

                        int idProtocolo = Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["id"].ToString()));
                        CargarProtocolo(idProtocolo);
                        btnVolverEditado.Visible = false;
                        ltr_PlantillaProtocolo.Text = "Editar Protocolo";

                    } else
                    {
                        txtNumero.ClientEnabled = false;
                        ltr_PlantillaProtocolo.Text = "Nuevo Protocolo";
                        btnVolverEditado.Visible = false;
                    }
                }

                gvDeterminaciones.DataBind();

            } catch
            {
                ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('Error al cargar la página, consulte al administrador del sistema', 'error');",true);
            }
        }

        protected void LlenadoCbDeterminaciones()
        {
            CmbDeterminaciones.DataSource = DeterminacionesService.GetAll();
            CmbDeterminaciones.TextField = "Descripcion";
            CmbDeterminaciones.ValueField = "ID";
            CmbDeterminaciones.DataBind();
            CmbDeterminaciones.Items.Insert(0,new ListEditItem());
        }

        protected void gvDeterminaciones_DataBinding(object sender,EventArgs e)
        {
            gvDeterminaciones.DataSource = GetListaDeterminaciones();
        }

        private IList<Domain.Determinaciones> GetListaDeterminaciones()
        {
            return (IList<Domain.Determinaciones>) Session["ListaDeterminaciones"];
        }

        protected void btnAgregarDeterminacion_Click(object sender,EventArgs e)
        {
            if(CmbDeterminaciones.SelectedItem != null)
            {
                int determinacionId = Convert.ToInt32(CmbDeterminaciones.SelectedItem.Value);
                Domain.Determinaciones determinacion = DeterminacionesService.GetById(determinacionId);

                if(determinacion != null)
                {
                    IList<Domain.Determinaciones> lista = GetListaDeterminaciones();


                    if(!lista.Any(item => item.Id == determinacionId))
                    {
                        lista.Add(determinacion);
                        Session["ListaDeterminaciones"] = lista;

                        gvDeterminaciones.DataBind();

                        CmbDeterminaciones.Text = "";

                    } else
                    {
                        CmbDeterminaciones.Text = "";
                        ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('La Determinación ya ha sido seleccionada!')",true);
                    }
                } else
                {
                    ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('Debe seleccionar una Determinación!')",true);
                }
            }
        }

        protected void btnEliminar_Click(object sender,EventArgs e)
        {
            try
            {
                ASPxButton btn = (ASPxButton) sender;
                GridViewDataItemTemplateContainer c = (GridViewDataItemTemplateContainer) btn.NamingContainer;
                var ind = DataBinder.Eval(c.DataItem,"Id");

                List<Domain.Determinaciones> ListaDeterminaciones;

                if(Session["ListaDeterminaciones"] != null)
                {
                    ListaDeterminaciones = (List<Domain.Determinaciones>) Session["ListaDeterminaciones"];

                } else
                {
                    ListaDeterminaciones = new List<Domain.Determinaciones>();
                }

                int idEliminar = Convert.ToInt32(btn.CommandArgument);
                Domain.Determinaciones det = ListaDeterminaciones.Find(q => q.Id == idEliminar);

                if(det != null)
                {
                    ListaDeterminaciones.Remove(det);
                    Session["ListaDeterminaciones"] = ListaDeterminaciones;
                    DeterminacionesService.SaveOrUpdate(det);
                }

                gvDeterminaciones.DataBind();

                string message = string.Format("La Determinación {0} se quitó con éxito!",det.Descripcion);
                ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('" + message + "')", true);

            } catch(Exception ex)
            {
                ScriptManager.RegisterStartupScript(this,this.GetType(),"key1","showMessage('Error al tratar de eliminar un elemento, consulte al administrador del sistema', 'error');",true);

            }
        }

        protected void btnGrabar_Click(object sender,EventArgs e)
        {

            try
            {
                PlantillaProtocolos PP;

                //Verifica que se haya ingresado un Protocolo y Determinación

                if(!string.IsNullOrEmpty(txtDescripcion.Text) && gvDeterminaciones.VisibleRowCount > 0)
                {
                    if(Request.QueryString["id"] != null)
                    {
                        // Se está editando un Protocolo existente.

                        int ProtocoloId = Convert.ToInt32(Util.Helpers.Decrypt(Request.QueryString["id"]));

                        //Obtengo el objeto Protocolo                        
                        PP = PlantillaProtocolosService.GetById(ProtocoloId);

                        //Verifico que el objeto Protocolo no sea nulo para comenzar a editarlo.
                        if(PP != null)
                        {
                            PP.Descripcion = txtDescripcion.Text.Trim().ToUpper();
                            PP.Numero = PP.Numero;

                            if(Session["ListaDeterminaciones"] != null)
                            {
                                List<Domain.Determinaciones> determinacion = (List<Domain.Determinaciones>) Session["ListaDeterminaciones"];

                                PP.Determinaciones.Clear();

                                foreach(Domain.Determinaciones deter in determinacion)
                                {
                                    PP.Determinaciones.Add(deter);

                                }
                            }

                            PP.Activo = true;
                            txtDescripcion.ClientEnabled = false;
                            CmbDeterminaciones.Text = "";
                            btnGrabar.ClientEnabled = false;
                            btnVolver.Visible = false;
                            btnVolverEditado.Visible = true;

                            PlantillaProtocolosService.SaveOrUpdate(PP);
                            ScriptManager.RegisterStartupScript(this,this.GetType(),"message3","showMessage('Plantilla editada correctamente.', 'success');",true);
                         
                        }

                    } else
                    {
                        // Se creó una nueva Plantilla.

                        PP = new PlantillaProtocolos();
                        PP.Numero = NumeracionServices.getByCodigo("PP").Numero;
                        PP.Descripcion = txtDescripcion.Text.Trim().ToUpper();
                        PP.Activo = true;

                        if(Session["ListaDeterminaciones"] != null)
                        {
                            List<Domain.Determinaciones> determinacion = (List<Domain.Determinaciones>) Session["ListaDeterminaciones"];

                            foreach(Domain.Determinaciones deter in determinacion)
                            {
                                PP.Determinaciones.Add(deter);
                            }

                        }

                        txtDescripcion.ClientEnabled = false;
                        CmbDeterminaciones.Text = "";
                        btnGrabar.ClientEnabled = false;
                        btnVolver.Visible = false;
                        btnVolverEditado.Visible = true;

                        PlantillaProtocolosService.SaveOrUpdate(PP);

                        ScriptManager.RegisterStartupScript(this,this.GetType(),"message3","showMessage('Plantilla creada correctamente correctamente.', 'success');",true);                     

                    }

                } else
                {
                    ScriptManager.RegisterStartupScript(this,this.GetType(),"message4","showMessage('Debe ingresar un Protocolo y al menos una Determinación.', 'error');",true);
                }

            } catch(Exception ex)
            {
                ScriptManager.RegisterStartupScript(this,this.GetType(),"message5","showMessage('Error al agregar Plantilla, intente de nuevo.', 'error');",true);
            }

        }

        private void CargarProtocolo(int idProtocolo)
        {
            try
            {
                Domain.PlantillaProtocolos pp = PlantillaProtocolosService.GetById(id: idProtocolo);


                if(pp != null)
                {
                    txtNumero.Text = pp.Numero.ToString();
                    txtDescripcion.Text = pp.Descripcion;

                    Session["ListaDeterminaciones"] = pp.Determinaciones.ToList<Domain.Determinaciones>();
                }



            } catch(Exception ex)
            {
            }
        }
    }
}