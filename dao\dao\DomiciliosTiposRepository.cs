//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using the template for generating Repositories and a Unit of Work for NHibernate model.
// Code is generated on: 3/11/2023 09:44:13
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------
using System;
using System.Linq;
using System.Collections.Generic;
using NHibernate;
using NHibernate.Linq;
using Domain;

namespace DAO
{
    public partial class DomiciliosTiposRepository : NHibernateRepository<Domain.DomiciliosTipos>, IDomiciliosTiposRepository
    {
        public DomiciliosTiposRepository(ISession session) : base(session) {
        }

        public virtual IList<Domain.DomiciliosTipos> GetAll() {
            return session.CreateQuery(string.Format("from DomiciliosTipos")).List<Domain.DomiciliosTipos>();
        }

        public virtual Domain.DomiciliosTipos GetByKey(int _Id) {
            return session.Get<Domain.DomiciliosTipos>(_Id);
        }

        public virtual Domain.DomiciliosTipos GetByCodigo(int Codigo) {
            string hql = "from DomiciliosTipos WHERE Codigo = :Codigo";

            IQuery q = session.CreateQuery(hql);
            q.SetParameter("Codigo",Codigo);
            return q.UniqueResult<Domain.DomiciliosTipos>();

        }
    }
}
