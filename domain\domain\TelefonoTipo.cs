//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON><PERSON><PERSON> Developer tool using NHibernate template.
// Code is generated on: 6/11/2023 09:33:14
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Collections.Generic;

namespace Domain
{

    /// <summary>
    /// There are no comments for Domain.TelefonoTipo, Domain in the schema.
    /// </summary>
    [Serializable]
    
    public partial class TelefonoTipo {
    
        #region Extensibility Method Definitions
        
        /// <summary>
        /// There are no comments for OnCreated in the schema.
        /// </summary>
        partial void OnCreated();
        
        #endregion
        /// <summary>
        /// There are no comments for TelefonoTipo constructor in the schema.
        /// </summary>
        public TelefonoTipo()
        {
            this.ContratoContactosTels = new HashSet<ContratoContactosTel>();
            this.Contratos_IdTelefonoTipo = new HashSet<Contratos>();
            this.Contratos_IdTelefonoTipoAlter = new HashSet<Contratos>();
            OnCreated();
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Tipo in the schema.
        /// </summary>
        public virtual string Tipo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Observacion in the schema.
        /// </summary>
        public virtual string Observacion
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ContratoContactosTels in the schema.
        /// </summary>
        public virtual ISet<ContratoContactosTel> ContratoContactosTels
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Contratos_IdTelefonoTipo in the schema.
        /// </summary>
        public virtual ISet<Contratos> Contratos_IdTelefonoTipo
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Contratos_IdTelefonoTipoAlter in the schema.
        /// </summary>
        public virtual ISet<Contratos> Contratos_IdTelefonoTipoAlter
        {
            get;
            set;
        }
    }

}
