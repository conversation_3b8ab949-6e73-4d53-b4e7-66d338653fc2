﻿using DAO;
using Domain;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Services.Caja
{
  public class ComprobanteTiposServices
    {
        public static Domain.ComprobanteTipos GetById(int id)
        {

            Domain.ComprobanteTipos ct;
            using (ISession sess = NHibernateSessionProvider.GetSession())
            {
                ct = (Domain.ComprobanteTipos)new ComprobanteTiposRepository(sess).GetByKey(id);
                if (ct != null)
                {
                    NHibernateUtil.Initialize(ct.Id);
                }
                sess.Close();
                sess.Dispose();
                return ct;
            }
        }

        public static IList<ComprobanteTipos> BuscarTipoComprobante(string Abreviatura)
        {
            IList<ComprobanteTipos> CT;

            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {

                CT = (IList<ComprobanteTipos>) new ComprobanteTiposRepository(sess).GetByPorAbreviatura(Abreviatura);

                sess.Close();
                sess.Dispose();
                return CT;
            }

        }

        public static IList<ComprobanteTipos> GetAll()
        {
            IList<ComprobanteTipos> listaAux;
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                listaAux = (IList<ComprobanteTipos>)new ComprobanteTiposRepository(sess).GetAll();
                sess.Close();
                sess.Dispose();
            }

            return (listaAux);
        }

        public static void SaveOrUpdate(ComprobanteTipos ct)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new ComprobanteTiposRepository(sess).Add(ct);

                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }
            }
        }

        public static void Delete(Domain.ComprobanteTipos ct)
        {
            using (NHibernate.ISession sess = NHibernateSessionProvider.GetSession())
            {
                using (NHibernate.ITransaction tx = sess.BeginTransaction())
                {
                    try
                    {
                        new ComprobanteTiposRepository(sess).Remove(ct);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        throw e;
                    }
                    finally
                    {
                        sess.Close();
                        sess.Dispose();
                    }
                }

            }

        }

    }
}
