﻿using DAO;
using Moq;
using System;
using System.ServiceModel.Channels;
using Xunit;

namespace TestUnit_Business.DAO
{
    /// <summary>
    /// Prueba unitaria para ProtocolosRepository
    /// </summary>
    public class ProtocolosRepositoryTests
    {
        private MockRepository mockRepository;

        private Mock<ISession> mockSession;

        public ProtocolosRepositoryTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Strict);

            this.mockSession = this.mockRepository.Create<ISession>();
        }

        private ProtocolosRepository CreateProtocolosRepository()
        {
            return new ProtocolosRepository(
                (NHibernate.ISession)mockSession.Object);
        }

        [Fact]
        public void GetAll_StateUnderTest_ExpectedBehavior()
        {
            // Arrange
            var protocolosRepository = this.CreateProtocolosRepository();

            // Act
            var result = protocolosRepository.GetAll();

            // Assert
            Assert.True(false);
            this.mockRepository.VerifyAll();
        }

        [Fact]
        public void GetByEstado_StateUnderTest_ExpectedBehavior()
        {
            // Arrange
            var protocolosRepository = this.CreateProtocolosRepository();
            string codigoEstado = null;

            // Act
            var result = protocolosRepository.GetByEstado(
                codigoEstado);

            // Assert
            Assert.True(false);
            this.mockRepository.VerifyAll();
        }

        [Fact]
        public void GetByKey_StateUnderTest_ExpectedBehavior()
        {
            // Arrange
            var protocolosRepository = this.CreateProtocolosRepository();
            int _Id = 0;

            // Act
            var result = protocolosRepository.GetByKey(
                _Id);

            // Assert
            Assert.True(false);
            this.mockRepository.VerifyAll();
        }
    }
}
