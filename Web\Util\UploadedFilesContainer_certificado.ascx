﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="UploadedFilesContainer_certificado.ascx.cs" Inherits="Web.Util.UploadedFilesContainer_certificado" %>

<%@ Register Assembly="DevExpress.Web.v18.1" Namespace="DevExpress.Web" TagPrefix="dx" %>
<script runat="server">
    
    public int Width { get; set; }
    public int Height { get; set; }
    public int NameColumnWidth { get; set; }
    public int SizeColumnWidth { get; set; }
    public string HeaderText { get; set; }
    public bool UseExtendedPopup { get; set; }
    protected void Page_Load(object sender, EventArgs e) {
        FilesRoundPanel_certificado.Width = Width;
        FilesRoundPanel_certificado.Height = Height;
        FilesRoundPanel_certificado.HeaderText = HeaderText;
    }
    protected string GetOptionsString() {
        return "'" + GetStyleAttributeValue(NameColumnWidth) + "', '" 
            + GetStyleAttributeValue(SizeColumnWidth) + "', " 
            +  UseExtendedPopup.ToString().ToLower();
    }
    protected string GetStyleAttributeValue(int width) {
        return width > 0 ? string.Format("width: {0}px; max-width: {0}px", width) : string.Empty;
    }
 </script>
<script type="text/javascript">
    DXUploadedFilesContainer_certificado.ApplySettings(<%= GetOptionsString() %>);
</script>

<dx:ASPxRoundPanel ID="FilesRoundPanel_certificado" ClientInstanceName="FilesRoundPanel_certificado" runat="server">
    <PanelCollection>
        <dx:PanelContent runat="server">
            <table id="uploadedFilesContainer_certificado" class="uploadedFilesContainer">
                <tbody></tbody>
            </table>
        </dx:PanelContent>
    </PanelCollection>
</dx:ASPxRoundPanel>

